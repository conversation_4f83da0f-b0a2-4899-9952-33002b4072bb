<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Overview</h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><strong>FusionCharts Free</strong> is an open-source FREE flash charting component that 
      can be used to render data-driven animated charts. Made in Macromedia Flash MX, FusionCharts  can be used with any web scripting 
      language like PHP, ASP, .NET, JSP, ColdFusion, JavaScript, Ruby on Rails etc., to deliver interactive and powerful charts. Using XML 
      as its data interface, FusionCharts  makes full use of  fluid beauty of Flash to 
      create <strong>compact</strong>, <strong>interactive</strong> and <strong>visually-arresting</strong> 
      charts. </td>
  </tr>
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="header">Advantages of using FusionCharts  </td>
  </tr>
  <tr> 
    <td valign="top" class="text"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="0">
        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Animated and interactive Charts</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>Using FusionCharts , you can quickly and easily render animated charts 
            that have a lot of interactive options for the end users.</td>
        </tr>
        <tr valign="top" class="text">
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text">
          <td valign="middle"><img src="Images/Bullet.gif" width="9" height="9" /></td>
          <td class="textBold">Easy yet powerful JavaScript integration</td>
        </tr>
        <tr valign="top" class="text">
          <td>&nbsp;</td>
          <td>FusionCharts  offers advanced options to integrate 
          charts with JavaScript modules. You can update charts on client side, invoke JavaScript functions as hotspot links.</td>
        </tr>
        
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">No installation hassles</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>To use FusionCharts , you do not have to install anything on your 
            server. All you need to do is copy-paste the SWF files (the core files 
            of FusionCharts) to your server, just like you would do to any image 
            files - and you're ready to go! So, even on those servers which do 
            not allow installation of ActiveX or any other form of components, 
            FusionCharts can run without any hassles. </td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td width="15" valign="middle"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Easy to use</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>FusionCharts makes the chart creation process a painless experience 
            for you. Since it uses XML as its data, all you need to do is convert 
            your data into XML using a programming language or even using a text editor like Notepad etc. - and that's all what is required to 
            create interactive and animated charts. The best part is  you DO NOT need to know anything about Flash to use FusionCharts .</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td width="15" valign="middle"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Runs on a variety of platforms</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>Irrespective of what server side scripting language you're using, 
            FusionCharts  can be used to create charts in that. Since FusionCharts 
             uses XML as the data interface, you can run it on any server and against 
            any scripting language. Also, to view the charts, your users just 
            need to have Adobe Flash Player 6 (or above), which is one of the most used browser 
            plugins on the planet.</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td width="15" valign="middle"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Reduces load on your servers</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td><p>In traditional image based charting systems, the charts are generated 
              as image at the server side. So, for each chart that you need to 
              serve to a user, you'll have to build complex images on the server 
              and then stream it to the client. When the need of hour is high, 
              this can be an expensive resource on the server, as image creation 
              takes a lot of toll on the server. </p>
            <p>FusionCharts  brings you great relief - all the charts are rendered 
              at client side using the widely installed Adobe Flash Platform. 
              The server is just responsible for streaming the pre-built SWF files 
              and your XML data files to the end viewers. From there on, Flash 
              Player takes the onus of rendering the charts. Also, the chart SWF 
              Files can be cached so that you can just update the data, and not 
              send chart SWF files every time.</p></td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td valign="middle"><img src="Images/Bullet.gif" width="9" height="9" /></td>
          <td class="textBold">A plethora of chart types</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>FusionCharts  offers you a plethora of chart types. From the 
            basic bar, column, line, pie etc. to the advanced combination charts, gantt chart, 
            you can build all the charts with the same ease of use.</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td valign="middle"><img src="Images/Bullet.gif" width="9" height="9" /></td>
          <td class="textBold">Avail it absolutely FREE. Period. </td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td><p>You can get FusionCharts Free without having to spend a single buck. It has no hidden gimmicks or advertisement linking back to our website. Free means truly FREE here. </p>
            <p> <a href="http://www.fusioncharts.com" target="_blank"><strong>FusionCharts v3</strong></a>, on the other hand, offers advanced options like gradients, 3D lightings (glow, blur, bevel &amp; shadow effects), animation, debugging, AJAX support,  dynamic data loading, more chart types etc. </p></td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
      </table></td>
  </tr>
</table>
</body>
</html>
