<?php

//session_start();
//include("db.inc.php");
//include("../db.inc.php");
include("./db.inc.php");
include("class/class.log4My.php");
$log = new log4My(); //Se instancia la clase.
$idXLog = $log->getId4Log(); //Se genera el identificador. 
//$log->write($idXLog, 0, 'commonFunctions.php/', 'empieza el file');	

if (isset($_POST['id'])) { //Se puede pasar el $id por POST o GET.
    $id = $_POST['id'];
}

if ($id == 1) {
// Chequea si ya se inicio sesion, si es asi... se carga de vuelta el menu y los datos
// de la empresa y el usuario.
    if (isset($_SESSION['dbOraConn']) && ($_SESSION['dbOraConn'] == 1)) {
        $est = '1';
        $msg = 'OK';
        $adic = $_SESSION['infoXuser_ge'];
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        $log->write($idXLog, 0, 'commonFunctions.php/Primera funcion id_1', 'Respuesta: ' . $msg);
        echo $respuesta;
    } else {
        unset($_SESSION['inicioSesion_gi']);
        $est = '2';
        $msg = 'El mismo usuario inicio sesion en otra maquina, cierre sesion e inicie de vuelta o pongase en contacto con el SAC para obtener mas informacion.';
        $adic = '***';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        $log->write($idXLog, 0, 'commonFunctions.php/Primera funcion id_1', 'Respuesta: ' . $msg);
        echo $respuesta;
    }
} elseif ($id == 2) {
// Se destruye le sesion y se devuelve el menú inicial con la opcion de LOGIN y
// el nombre del producto (Eficash - Pronet S.A.).
    //$log->write($idXLog, 0, 'commonFunctions.php/session_destroy', 'Se destruyo la sesion para el usuario Nro.: '.$_SESSION["cod_usuario"]);
    session_destroy();
    echo '<ul>
              <li><a href="javascript:loadContent(\'login.php\');">LOGIN</a></li>
            </ul>|<h1><b>Gest&oacute;n de Usuarios Externos</b></h1>';
} elseif ($id == 3) {
    echo $aux = buscarUsuarioDisponible($log, $idXLog, $_POST['id'], $_POST['usuario']);
} elseif ($id == 4) {
    echo $aux = insertarUsuario($log, $idXLog, $_POST['id'], $_POST['nombre'], $_POST['apellido'], $_POST['in_password']);
} elseif ($id == 5) {
    echo $aux = getData($log, $idXLog, $_POST['id'], $_POST['usuario']);
} elseif ($id == 6) {
    echo $aux = updateUsuario($log, $idXLog, $_POST['id'], $_POST['usuario'], $_POST['campo'], $_POST['valor'], $_POST['tipo'], $_POST['nombre'], $_POST['apellido']);
} elseif ($id == 7) {
    echo $aux = reporteUsuarios($_POST['id'], $log, $idXLog, $_SESSION['codEmpresa']);
} elseif ($id == 8) {
//	$log->write($idXLog, 0, 'commonFunctions.php/', 'llama a la func');	
    echo $aux = consolidadoRecaudadoras($log, $idXLog, $_POST['id'], $_POST['fecha1_inicio'], $_POST['fecha1_fin'], $_POST['fecha2_inicio'], $_POST['fecha2_fin'], $_POST['recaudadora'], $_POST['des_rec'], $_SESSION['codEmpresa']);
} elseif ($id == 9) {
    $parametros = $_POST['fecha_inicio'] . ' ' . $_POST['fecha_fin'] . ' ' . $_POST['corte'] . ' ' . $_POST['recaudadora'] . ' ' . $_POST['emisora'];
    $log->write($idXLog, 0, 'commonFunctions.php/ id: ' . $id, 'Parametros: ' . $parametros);
    echo $aux = histograma($log, $idXLog, $_POST['id'], $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['corte'], $_POST['recaudadora'], $_POST['emisora'], $_POST['desde'], $_POST['hasta'], $_SESSION['codEmpresa']);
} elseif ($id == 10) {
    echo $aux = consolidadoEmisoras($log, $idXLog, $_POST['id'], $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['emisora'], $_POST['des_em'], $_SESSION['codEmpresa']);
} elseif ($id == 11) {
    echo $aux = queryLibre($log, $idXLog, $_POST['id'], $_POST['query']);
} elseif ($id == 12) {
    echo $aux = rankingRecaudadoras($log, $idXLog, $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['top'], $_SESSION['codEmpresa']);
} elseif ($id == 13) {
    echo $aux = subRedesResumenSaldo($log, $idXLog, $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['rec'], $_POST['des_rec'], $_POST['tipoFecha'], $_POST['moneda']);
} elseif ($id == 14) {
    echo $aux = subRedesMovimientosRecaudadoras($log, $idXLog, $_POST['fecha1_inicio'], $_POST['fecha1_fin'], $_POST['fecha2_inicio'], $_POST['fecha2_fin'], $_POST['rec'], $_POST['des_rec'], $_POST['conciliado'], $_POST['moneda']);
} elseif ($id == 15) {
    echo $aux = comboRecaudadorasSubRED($log, $idXLog, $_POST['id']);
} elseif ($id == 16) {
    echo $aux = reporteDepositoCorresponsaliaFechaCobro($log, $idXLog, $_POST['fechaCierre'], $_POST['codRecaudador'], $id, $_POST['dias_juliano'], $_POST['tipoRep'], $_SESSION['codEmpresa']);
} elseif ($id == 17) {
    echo $aux = comboTipoDoc($log, $idXLog, $id);
} elseif ($id == 18) {
    echo $aux = repTransXdoc($log, $idXLog, $id, $_POST['nroDoc'], $_POST['tipoDoc']);
} elseif ($id == 19) {
    echo $aux = comboRecaudadorasRED($log, $idXLog);
} elseif ($id == 20) {
    echo $aux = listaSucursalRecaudadorasRED($log, $idXLog, $_POST['codRecaudadora']);
} elseif ($id == 21) {
    echo $aux = ActualizarCoordenadas($log, $idXLog, $_POST['codRecaudadora'], $_POST['codSucursal'], $_POST['latitud'], $_POST['longitud']);
} elseif ($id == 22) {
    echo $aux = comboBancos($log, $idXLog, $_POST['id']);
} elseif ($id == 23) {
    echo $aux = comboMoneda($log, $idXLog, $_POST['id']);
} elseif ($id == 24) {
    echo $aux = insertarChequeRechazado($_POST['id'], $log, $idXLog, $_POST['idrecaudadora'], $_POST['idBancoCheque'], $_POST['importe'], $_POST['moneda'], $_POST['fechaOperacion'], $_POST['idTipoPago'], $_POST['observacion'], $_POST['nroCheque'], $_POST['motivos']);
} elseif ($id == 25) {
    echo $aux = genlistaAprobarChequesRech($log, $idXLog, $_POST['codOperacion']);
} elseif ($id == 26) {
    echo $aux = actualizarChqRechazados($log, $idXLog, $_POST['codOperacion'], $_POST['estado'], $_POST['obsAutorizacion']);
} elseif ($id == 27) {
    echo $aux = genListadoDesembolsoSucursalEficash($log, $idXLog, $_POST['fecha'], $_POST['estado'], $_POST['moneda']);
} elseif ($id == 28) {
    echo $aux = cmbMoneda($log, $idXLog, $id);
} elseif ($id == 29) {
    echo $aux = genListadoCobroCajaCajeroFecha($log, $idXLog, $id, $_POST['fechaRep'], $_POST['fechaRepFin']);
} elseif ($id == 30) {
    echo $aux = genListaEntidadEficash($log, $idXLog, $id);
} elseif ($id == 31) {
    echo $aux = listTransEficash($log, $idXLog, $id, $_POST['fecha'], $_POST['tipoFecha'], $_POST['entidad'], $_POST['moneda'], $_POST['tipoReporte']);
} elseif ($id == 32) {
    echo $aux = comboRecaudadoras($log, $idXLog);
} elseif ($id == 33) {
    echo $aux = comboEjecutivo($log, $idXLog);
} elseif ($id == 34) {
    echo $aux = repMetaCuatrimestral($log, $idXLog, $id, $_POST['anhoProyectado'], $_POST['cuatrimestre'], $_POST['ejecutivo'], $_POST['recaudadora']);
} elseif ($id == 35) {
    echo $aux = reporteRemesasVsDeposito($log, $idXLog, $id, $_POST['fechaDesde'], $_POST['fechaHasta'], $_POST['emisor']);
} elseif ($id == 36) {
    echo $aux = listadoEntidad($log, $idXLog, $id);
} elseif ($id == 37) {
    echo $aux = reporteRemesasFechaProceso($log, $idXLog, $id, $_POST['fechaDesde'], $_POST['fechaHasta'], $_POST['emisor']);
} elseif ($id == 38) {
    echo $aux = reportePerfilDeClientes($log, $idXLog, $id, $_POST['fechaDesde'], $_POST['fechaHasta'], $_POST['nroDocumento']);
} elseif ($id == 39) {
    echo $aux = listadoRemesadora($log, $idXLog, $id);
} elseif ($id == 40) {
    echo $aux = listadoAnuladosEnvio($log, $idXLog, $id, $remesadora, $empresa, $idDiv);
} elseif ($id == 41) {
    echo $aux = pendienteAnulacionEnvio($log, $idXLog, $id, $cod_envio, $observacion);
} elseif ($id == 42) {
    echo $aux = listadoAnuladosEntrega($log, $idXLog, $id, $remesadora, $empresa, $idDiv);
} elseif ($id == 43) {
    echo $aux = pendienteAnulacionEntrega($log, $idXLog, $id, $cod_entrega, $observacion);
} elseif ($id == 44) {
    echo $aux = listadoAutorizaAnulacionEnvio($log, $idXLog, $id, $remesadora);
} elseif ($id == 45) {
    echo $aux = listadoAutorizaAnulacionEntrega($log, $idXLog, $id, $remesadora);
} elseif ($id == 46) {
    /* Tipo: ENTREGA o  ENVIO */
    $log->write($idXLog, 0, 'ID: 46', 'Llamando a funcion tipo: ' . $tipo . ', Remesadora: ' + $remesadora);
    echo $aux = autorizarAnulacion($log, $idXLog, $id, $codigo, $remesadora, $observacion, $tipo);
} elseif ($id == 47) {
    echo $aux = saldoRecaudadora($log, $idXLog, $_POST['fecha_inicio'], $_POST['fecha_fin']);
} elseif ($id == 48) {
    echo $aux = saldoRecaudadoraDetalle($log, $idXLog, $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['rec']);
} elseif ($id == 49) {
    echo $aux = saldoRecaudadoraTipoServicio($log, $idXLog, $_POST['fecha_inicio'], $_POST['fecha_fin'], $_POST['rec']);
} elseif ($id == 50) {
    echo $aux = saldoConsolidado($log, $idXLog, $_POST['fechaDesde'], $_POST['fechaHasta'], $_POST['rec'], $_POST['moneda']);
} elseif ($id == 51) {
    //echo $aux = insertarChequeRechazado($log, $idXLog, $_POST['idrecaudadora'], $_POST['idBancoCheque'], $_POST['importe'], $_POST['moneda'], $_POST['fechaOperacion'], $_POST['idTipoPago'], $_POST['observacion'], $_POST['nroCheque']);
    echo $aux = reporteChequeRechazado($id, $log, $idXLog, $_POST['fechaDesde'], $_POST['fechaHasta'], $_POST['codRecaudadora'], $_POST['codBancoCheque'], $_POST['importe'], $_POST['nroCheque'], $_POST['tipo']);
} elseif ($id == 52) {
    echo $aux = insertarChequeRechazadoCodTrx($log, $idXLog, $_POST['observacion'], $_POST['codTrx'], $_POST['motivos']);
} elseif ($id == 53) {
    echo $aux = genlistaGenerarChequesRech($log, $idXLog, $_POST['codOperacion']);
} elseif ($id == 54) {

    echo $aux = saldoArrastre($log, $idXLog, $_POST['id'], $_POST['fecha'], $_POST['rec']);
} elseif ($id == 55) {
    echo $aux = VerNota($log, $idXLog, $_POST['operacion'], $_POST['recaudadora']);
} elseif ($id == 56) {
    echo $aux = listadoMotivo($log, $idXLog, $_POST['id']);
} elseif ($id == 57) {
     die(consultaRV());
}elseif ($id == 58) {
    die(guardarRangos());
}

/* * ****************** */
/* Bloque de funciones */
/* * ****************** */
/*
  function pedidoActualizacion($id, $log, $idXLog){
  echo $aux = enviarPedidoActualizacion($id, $log, $idXLog, 3);
  }
 */
function guardarRangos(){
   global $connOraSet, $log, $idXLog;
   $ubicacion = getcwd() . "/" . __FUNCTION__;
   
   $operacion =isset($_POST['operacion'])?$_POST['operacion']:'';
   $desde =isset($_POST['desde'])?$_POST['desde']:'';
   $hasta =isset($_POST['hasta'])?$_POST['hasta']:'';
   $moneda =isset($_POST['moneda'])?$_POST['moneda']:'';
   $puntoR =isset($_POST['puntoR'])?$_POST['puntoR']:'';
   
   $est='0';
   $msg='Ha ocurrido un error. NO se ha podido guardar los datos';
   
   if($operacion == 0){  //isertar datos
       $query="insert into REMESAS.RANGO_VOLUMEN (
           COD_RANGO_VOLUMEN,
           DESDE,
           HASTA,
           COD_MONEDA,
           PUNTAJE_RIESGO )
           values(
           (select (nvl((select max(COD_RANGO_VOLUMEN) from REMESAS.RANGO_VOLUMEN), 0)+1) from dual),
           '$desde',
           '$hasta',
            $moneda,
            $puntoR)";
       if(ejecutarSql($query, $ubicacion, 'ora')){
           $est='1';
           $msg='<h2 style=color:red;>Los datos se guardaron con exito</h2>';
           $respuesta=$est.'|'.$msg;
           return $respuesta;
       }else{
           $respuesta=$est.'|'.$msg;
           return $respuesta;
       }
       
   }elseif($operacion == 1){  //actualizar datos
   
       
   }
   
}
function listadoMotivo($log, $idXLog, $id) {
    include("db.inc.php");
    $est = 0;
    //consulta para cargar motivos de cheques
    $query1 = "select * from admin.MOTIVOS_RECHAZO_CHEQUE ";
    $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech ', 'Query: ' . $query1);

    $id_sentencia2 = @oci_parse($ora_conn, $query1);

    if (!$id_sentencia2) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech ' . __LINE__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia2, OCI_DEFAULT);

    $selec = '<select size="10" class="multiselect" multiple="multiple">';
    while ($fila1 = oci_fetch_array($id_sentencia2, OCI_RETURN_NULLS)) {
        $selec.=" <option value='$fila1[0]'>$fila1[1]</option>"; //loo 
        $est = '1';
    }
    $selec.=' </select>';
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $selec;
    return $respuesta;
}

function VerNota($log, $idXLog, $codOperacion, $recaudadora) {
    //conectarme cn bd
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $query = " select  t.cod_operacion, t.cod_transaccion, e.des_entidad , b.des_banco, t.nro_cheque , t.importe
from admin.CTRL_CHEQUES_SUB_RED t, admin.recaudadora r, admin.entidad e,  admin.banco b where 
t.cod_operacion= '$codOperacion'
and r.cod_empresa=e.cod_empresa
and b.cod_banco=t.cod_banco
and r.cod_recaudadora=e.cod_entidad
and r.cod_recaudadora='$recaudadora'";
    //hacer select
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    //consulta motivos 
    $query1 = "select *
  from admin.controlcheque_motivo c , admin.motivos_rechazo_cheque m
 where c.cod_operacion = $codOperacion  
 and c.tid_motivo= m.tid
";
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query1);
    $id_sentencia1 = @oci_parse($ora_conn, $query1);
    if (!$id_sentencia1) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query1);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia1, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia1);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query1);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    //llo
    $detalleMotivos = '<ul>';
    while ($fila1 = oci_fetch_array($id_sentencia1, OCI_RETURN_NULLS)) {
        $detalleMotivos.='<li>';
        $detalleMotivos.=$fila1['DESCRIPCION'];
        $detalleMotivos.='</li>';
    }
    $detalleMotivos.='</ul>';


    //introducir datos en tabla
    $fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS);
    //var_dump($fila);
    $cabecera = "";
    $tabla = "";
    $footer = "";
    $cabecera = "<div id='rep' align='center'>
		<table border='0'>
			<tr>
				<td><a href='ticket/impresion.php' target='_blank'><img src='img/imprimir.jpg' alt='IMPRIMIR' border='0'></a></td>
				<td><a href='ticket/xls.php' target='_blank'><img src='img/excel.jpg' alt='EXCEL' border='0'></a></td>				
				<td><a href='ticket/pdf.php' target='_blank'><img src='img/pdf.jpg' width='40' height='41' alt='PDF' border='0' /></a></td>								
			</tr>
		</table>
	</div>";
    $tabla = '
   <br/>
   <br/>
<p></p>    
<table width="50%" border="0" id="tablanota" height:100px;>
  <tr>
    <td>
    <br/>
    <br/>
    <br/>
    <br/>
    <br/>
    </td>
    <td> 
    <br/>
    <br/>
    <br/><p align="right">
    Nro Operacion: ' . $fila['COD_OPERACION'] . ' <br/><br/>
      <br/>Asuncion, 20 de Marzo  de 2014</p></td>
  </tr>
  <tr>
    <td><p>SE&Ntilde;ORES:</p></td>
    <td></td>
  </tr>
  <tr>
    <td><p><b>' . $fila['DES_ENTIDAD'] . '</b></p></td>
    <td></td>
  </tr>
  <tr>
    <td colspan="2"><p>Por la presente nota hacemos entrega del cheque rechazado  con los datos detallados m&aacute;s abajo:</p></td>
  </tr>
  <tr>
    <td colspan="2"><p> Banco: <b>' . $fila['DES_BANCO'] . '</b></p></td>
  </tr>
  <tr>
    <td colspan="2"><p>N&deg; Cheque: <b>' . $fila['NRO_CHEQUE'] . '</b></p></td>
  </tr>
  <tr>
    <td colspan="2"><p>Monto: <b>' . $fila['NRO_CHEQUE'] . '</b> </p></td>
  </tr>
  <tr>
    <td colspan="2"><p><u>Motivo del Rechazo:</u></p>
    ' . $detalleMotivos . '
</td>
  </tr>
  <tr>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="2"><p>En  vista que el rechazo se debi&oacute; a un defecto de forma y que contractualmente su  regularizaci&oacute;n corresponde al recaudador, solicitamos tengan a bien realizar el  depósito inmediato del importe correspondiente a fin de cubrir el sobregiro  generado por esta situación, aclarando adem&aacute;s que se estar&aacute; asignando la multa  correspondiente por el evento. </p></td>
  </tr>
  <tr>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="2"><p>Ante cualquier duda, por favor,  no dude en comunicarse con nosotros al Departamento de Riesgos con Cesar  Mancuello al tel&eacute;fono: 444-544.<br />
        Atentamente.</p></td>
  </tr>
  <tr>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="2"><p>PRONET S.A.</p></td>
  </tr>
  <tr>
    <td colspan="2"><p>C.M</p></td>
  </tr>
  <tr>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="2"><p>Recibido Fecha:</p></td>
  </tr>
  <tr>
    <td colspan="2"><p><u>Nombre y Apellido</u></p></td>
  </tr>
  <tr>
    <td colspan="2"><p><u>C.I.:</u></p></td>
  </tr>
  <tr>
    <td colspan="2"><p><u>Firma:</u></p></td>
  </tr>
</table>';
    $footer = '<style type="text/css" >
p{font-size:12px;
color:#000;}
#tablanota{
 border: 1px dashed #333333;
    border-radius: 8px;
    padding: 33px;}
    ul li{
       color: #000000;
    font-size: 12px;
    font-weight: bold;
    list-style: none outside none;
    }
</style>';
    $tablaimpresion = str_replace('img/pronetLogo.jpg', '../img/pronetLogo.jpg', $tabla);
    $tablaimpresion = str_replace('img/aquipago_ticket.PNG', '../img/aquipago_ticket.PNG', $tablaimpresion);
    $_SESSION['datosImprimir'] = $tablaimpresion;
    $_SESSION['repName'] = "Nota ";
    return '1' . '|' . $cabecera . $tabla . $footer;
}
function ejecutarSql($query, $ubicacion = '', $bd = 'ora') {
    include("db.inc.php");
      $ora_conn = oci_connect($user, $password, $host);
    global $log, $idXLog;
    $ubicacion = ($ubicacion == '') ? $ubicacion : __FILE__ . "/" . __FUNCTION__;
    if (trim($bd) == 'ora') {
        include './reportesSet/etc/ORAconnect.php';
        //include ' etc/ORAconnect.php';
        $conn = $ora_conn;
    } else {
        include './reportesSet/etc/POSSconnect.php';
        
        $conn = $poss;
    }

    $log->write($idXLog, 0, $ubicacion, "QUERY: " . $query);

    $conn->SetFetchMode(ADODB_FETCH_ASSOC);
    $result = $conn->Execute($query);
    $log->write($idXLog, 0, $ubicacion, "QUERY: " . $conn->ErrorMsg());
    $commit = $conn->CommitTrans();
    if (!$commit) {
        $_SESSION['lastError'] = str_replace('ORA-', '', $conn->ErrorMsg());
        $log->write($idXLog, 3, $ubicacion, 'ERROR: ' . htmlentities($conn->ErrorMsg() . '<br />' . $query));
        return false;
    } else {
        return $result;
    }
}
function saldoArrastre($log, $idXLog, $id, $fecha, $rec) {
    include("db.inc.php");



    $ora_conn = oci_connect($user, $password, $host);
    $query = "
SELECT C.COD_BANCO,
       B.DES_BANCO,
       C.COD_RECAUDADORA,
       E.DES_ENTIDAD,
       TO_CHAR(FECHA, 'DD/MM/YYYY') AS FECHA,
       UPPER(DES_MONEDA) AS MONEDA,
       DES_TIPO_PAGO,       
       SUM(SERVICIOS) SERVICIOS,
       SUM(ENVIOS) ENVIOS,
       SUM(COBRANZAS_MH) COBRANZAS_MH,
       SUM(CHQ_RECHAZADO) CHQ_RECHAZADO,
       SUM(DESEMBOLSO) DESEMBOLSO,
       SUM(ENTREGA) ENTREGA,       
       SUM(DEPOSITO) DEPOSITO,              
       case when SUM(TOTAL)  > 0  
       then 0 else SUM((DESEMBOLSO + ENTREGA + DEPOSITO) - (SERVICIOS + ENVIOS + COBRANZAS_MH + CHQ_RECHAZADO)) end SALDO,
       
       case when SUM(TOTAL)  > 0 
           then SUM((DESEMBOLSO + ENTREGA) - (SERVICIOS + ENVIOS)) 
           else 0 end SALDO_REEMBOLSO
       
  FROM (
       SELECT COD_BANCO,               
              COD_RECAUDADORA,
              FECHA,
              DES_MONEDA,
              DES_TIPO_PAGO,
              SERVICIOS,
              ENVIOS, 
              COBRANZAS_MH,           
              CHQ_RECHAZADO,                    
              DESEMBOLSO,
              ENTREGA,
              DEPOSITO,
              MH, 
              SUM((DESEMBOLSO+ENTREGA) - (SERVICIOS + ENVIOS + COBRANZAS_MH + CHQ_RECHAZADO)) OVER(Partition By COD_RECAUDADORA) as TOTAL
       FROM        
      (--COBRO DE SERVICIOS 
        SELECT NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA) COD_BANCO,               
                TR.COD_RECAUDADORA,
                TRUNC(A.FECHA_cie) FECHA,
                M.DES_MONEDA,
                TP.DES_TIPO_PAGO,
                SUM(DECODE(S.ID_RUBRO_SERVICIO, 23, 0, DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0))) SERVICIOS,
                SUM(DECODE(S.ID_RUBRO_SERVICIO, 23, DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0), 0)) ENVIOS, 
                0 COBRANZAS_MH,           
                0 CHQ_RECHAZADO,                    
                /*SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,*/
                SUM(DECODE(S.ID_RUBRO_SERVICIO, 23, 0, DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0))) DESEMBOLSO,
                SUM(DECODE(S.ID_RUBRO_SERVICIO, 23, DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0), 0)) ENTREGA, 
                0 AS DEPOSITO,
                'N' AS MH
          FROM ADMIN.TRANSACCION     TR,                
                ADMIN.MONEDA          M,
                ADMIN.TIPO_PAGO       TP,
                ADMIN.APERTURA_CIERRE A,
                ADMIN.SERVICIO        S, 
                ADMIN.BANCO_DEPOSITO_SUB_RED BD
         WHERE TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
           AND TR.COD_MONEDA = M.COD_MONEDA
           AND TR.COD_EMPRESA = A.COD_EMPRESA
           AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
           AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
           AND TR.NRO_CAJA = A.NRO_CAJA
           AND TR.NRO_LOTE = A.NRO_LOTE
           AND TR.ID_TERMINAL = A.ID_TERMINAL
           AND TR.COD_EMPRESA = S.COD_EMPRESA
           AND TR.COD_EMISORA = S.COD_EMISORA
           AND TR.COD_SERVICIO = S.COD_SERVICIO
           AND TR.COD_EMPRESA = BD.COD_EMPRESA(+)
           AND TR.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
           AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
           AND a.fecha_cie >= TO_DATE('$fecha', 'DD/MM/YYYY')
           AND a.fecha_cie < TO_DATE('$fecha', 'DD/MM/YYYY') + 1
           AND TR.COD_BANCO_RECAUDADORA IN (97,98)
           AND TR.COD_TIPO_TRANSACCION = 3
           AND TR.ANULADO = 'N'           
           AND (TR.ENLINEA = 'N' OR (TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
           AND TR.COD_SERVICIO != 1123
           AND M.COD_MONEDA = 1
           AND TR.COD_RECAUDADORA = $rec
         GROUP BY NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA),                   
                   TR.COD_RECAUDADORA,
                   TRUNC(A.FECHA_cie),
                   M.DES_MONEDA,
                   TP.DES_TIPO_PAGO,
                   TR.COD_SERVICIO   
        UNION ALL
        --COBRANZAS DE HACIENDA
            select NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA) COD_BANCO,
                   P.COD_RECAUDADORA,                   
                   TRUNC(p.fechapago) AS FECHA,
                   'GUARANI',
                   decode(trim(p.cod_medio_pago), 'E','Efectivo','Cheque'),
                   0 SERVICIOS,
                   0 ENVIOS,
                   sum(p.importe) COBRANZAS_MH,
                   0 CHQ_RECHAZADO,                   
                   0 DESEMBOLSO,
                   0 ENTREGA, 
                   0 DEPOSITO,
                   'S' AS MH
            from admin.pagos_mh p, ADMIN.BANCO_DEPOSITO_SUB_RED BD
            where p.cod_empresa = bd.cod_empresa(+)
            and p.cod_recaudadora = bd.cod_recaudadora(+)
            and p.fechapago >= TO_DATE('$fecha', 'DD/MM/YYYY')
            and p.fechapago < TO_DATE('$fecha', 'DD/MM/YYYY') + 1
           and p.cod_banco_recaudadora = 98
           -- AND P.COD_RECAUDADORA = $rec
            group by NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA),  P.COD_RECAUDADORA,
                           TRUNC(p.fechapago), decode(trim(p.cod_medio_pago), 'E','Efectivo','Cheque')               
        UNION ALL       
        --DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)      
        SELECT T.COD_BANCO,               
               T.COD_RECAUDADORA,
               TRUNC(T.Fecha_Insercion) AS FECHA,
               M.DES_MONEDA,
               TP.DES_TIPO_PAGO,
                   0 SERVICIOS,
                   0 ENVIOS,
                   0 COBRANZAS_MH,
                   0 CHQ_RECHAZADO,
                   0 DESEMBOLSO,
                   0 ENTREGA,
               SUM(T.IMPORTE) DEPOSITO,
               T.PAGOS_MH AS MH
          FROM ADMIN.CONTROL_SOBREGIRO T,               
               ADMIN.MONEDA            M,
               ADMIN.TIPO_PAGO         TP
         WHERE T.COD_MONEDA = M.COD_MONEDA
           AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
           AND T.CONCILIADO = 'S'
           AND T.Fecha_Insercion >= admin.f_valida_fecha(TO_DATE('$fecha', 'DD/MM/YYYY') + 1)
           AND T.Fecha_Insercion < admin.f_valida_fecha(TO_DATE('$fecha', 'DD/MM/YYYY') + 1)+1
           AND M.COD_MONEDA = 1       
           AND T.TIPO_MOVIMIENTO = 'C'
           AND T.COD_RECAUDADORA = $rec
           HAVING SUM(T.IMPORTE) > 0
         GROUP BY T.COD_BANCO,
                  T.COD_RECAUDADORA,
                  TRUNC(T.Fecha_Insercion),
                  M.DES_MONEDA,
                  TP.DES_TIPO_PAGO,
                  T.PAGOS_MH
        UNION ALL       
        --DEPOSITOS DE REMESAS
    SELECT BR.COD_BANCO, 
    BR.COD_RECAUDADORA, 
    TRUNC(D.FECHA_HORA_DEPOSITO) AS FECHA,
    M.DES_MONEDA,
    TP.DES_TIPO_PAGO,
    0 SERVICIOS,
    0 ENVIOS,
    0 COBRANZAS_MH,
    0 CHQ_RECHAZADO,
    0 DESEMBOLSO,
    0 ENTREGA,
    SUM(D.IMPORTE) DEPOSITO,
    'N' AS MH
    FROM ADMIN.DEPOSITOS_CLEARING D, ADMIN.BANCO_RECAUDADORA BR, ADMIN.MONEDA M, ADMIN.TIPO_PAGO TP
    WHERE D.NRO_CUENTA = BR.NRO_CUENTA_DEBITO
    AND D.COD_MONEDA = M.COD_MONEDA
    AND D.COD_TIPO_PAGO = TP.COD_TIPO_PAGO    
    AND BR.COD_BANCO = 97
    AND BR.COD_MONEDA = 1
    AND D.ANULADO = 'N'
    AND D.FECHA_HORA_DEPOSITO >= ADMIN.F_VALIDA_FECHA(TO_DATE('$fecha', 'DD/MM/YYYY') + 1)
    AND D.FECHA_HORA_DEPOSITO < ADMIN.F_VALIDA_FECHA(TO_DATE('$fecha', 'DD/MM/YYYY') + 1)+1
    AND BR.COD_RECAUDADORA = $rec
    HAVING SUM(D.IMPORTE) > 0
    GROUP BY BR.COD_BANCO, 
    BR.COD_RECAUDADORA, 
    TRUNC(D.FECHA_HORA_DEPOSITO),
    M.DES_MONEDA,
    TP.DES_TIPO_PAGO                  
        UNION ALL       
        --CHEQUES RECHAZADOS
        SELECT T.COD_BANCO,               
               T.COD_RECAUDADORA,
               TRUNC(T.FECHA_OPERACION) AS FECHA,
               M.DES_MONEDA,
               TP.DES_TIPO_PAGO,               
               0 SERVICIOS,
               0 ENVIOS,
               0 COBRANZAS_MH,
               SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) CHQ_RECHAZADO,               
               0 DESEMBOLSO,
               0 ENTREGA,               
               0 DEPOSITO,
               'N' MH
          FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
               ADMIN.MONEDA               M,
               ADMIN.TIPO_PAGO            TP
         WHERE T.COD_MONEDA = M.COD_MONEDA             
           AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
           AND T.FECHA_OPERACION >= TO_DATE('$fecha', 'DD/MM/YYYY')
           AND T.FECHA_OPERACION < TO_DATE('$fecha', 'DD/MM/YYYY') + 1
           and t.cod_empresa = '01'
           AND M.COD_MONEDA = 1
           AND T.COD_RECAUDADORA = $rec
         GROUP BY T.COD_BANCO,                  
                  T.COD_RECAUDADORA,
                  TRUNC(T.FECHA_OPERACION),
                  M.DES_MONEDA,
                  TP.DES_TIPO_PAGO,
                  T.PAGOS_MH)) C,   ADMIN.BANCO B, ADMIN.ENTIDAD E
WHERE C.COD_BANCO = B.COD_BANCO
AND C.COD_RECAUDADORA = E.COD_ENTIDAD
 GROUP BY C.COD_BANCO,
          DES_BANCO,
          COD_RECAUDADORA,
          DES_ENTIDAD,
          FECHA,
          UPPER(DES_MONEDA),
          DES_TIPO_PAGO
 ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA, DES_TIPO_PAGO 
";
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $cabecera = "<div id='rep' align='center'>
		<table border='0'>
			<tr>
				<td><a href='ticket/impresion.php' target='_blank'><img src='img/imprimir.jpg' alt='IMPRIMIR' border='0'></a></td>
				<td><a href='ticket/xls.php' target='_blank'><img src='img/excel.jpg' alt='EXCEL' border='0'></a></td>				
				<td><a href='ticket/pdf.php' target='_blank'><img src='img/pdf.jpg' width='40' height='41' alt='PDF' border='0' /></a></td>								
			</tr>
		</table>
	</div>";
    $adic = '
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>COD_BANCO</th>
							<th>BANCO</th>
							<th>COD_RECAUDADORA</th>
							<th>RECAUDADORA</th>
							<th>FECHA</th>
							<th>MONEDA</th>
							<th>TIPO PAGO</th>
							<th>SERVICIOS</th>
							<th>ENVIOS</th>
							<th>COBRANZAS MH</th>
							<th>CHEQUE RECHAZADO</th>
							<th>DESEMBOLSO</th>
							<th>ENTREGA</th>
							<th>DEPOSITO</th>
							<th>SALDO</th>
							<th>SALDO REEMBOLSO</th>
						</tr>
					</thead>
					<tbody>';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic .= '<tr>';
        $adic .= '<td>&nbsp; ' . $fila['COD_BANCO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['DES_BANCO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['COD_RECAUDADORA'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['DES_ENTIDAD'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['FECHA'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['MONEDA'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['DES_TIPO_PAGO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['SERVICIOS'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['ENVIOS'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['COBRANZAS_MH'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['CHQ_RECHAZADO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['DESEMBOLSO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['ENTREGA'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['DEPOSITO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['SALDO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['SALDO_REEMBOLSO'] . '</td>';

        $adic .= '</tr>';
        $est = '1';
        $msg = 'OK';
    }
    $adic .='</tbody>
				</table>';
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . aplicarTiny($cabecera.$adic);

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadoraDetalle', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function getData($log, $idXLog, $id, $usuario) {
    include("db.inc.php");



    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getData', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select * from admin.usuario where usuario = '$usuario'";

    $log->write($idXLog, 0, 'commonFunctions.php/getData', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getData', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getData', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic = $fila['NOMBRE'] . ';' . $fila['APELLIDO'] . ';' . $fila['CLAVE_ACCESO'] . ';' . $fila['ACTIVO'];
        $est = '1';
        $msg = 'OK';
    }
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/getData', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function reporteUsuarios($id, $log, $idXLog, $codEmpresa) {
    include("db.inc.php");

    $_SESSION['repName'] = 'UsuariosDisponibles_' . date("d-m-Y");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteXArqueo', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select p.usuario, p.email, (select s.des_perfil_usuario from admin.perfil_usuario s where s.cod_perfil = p.cod_perfil) as perfil 
				from admin.usuario p where p.cod_empresa = '$codEmpresa'";

    $log->write($idXLog, 0, 'commonFunctions.php/reporteUsuarios', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteUsuarios', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteUsuarios', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $adic = '<br>
			<div id="rep" align="center">
				<ul><li><b>Listado de USUARIOS registrados.</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>Usuario</th>
							<th>Email</th>
							<th>Perfil</th>
						</tr>
					</thead>
					<tbody>';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic .= '<tr>';
        $adic .= '<td>&nbsp; ' . $fila['USUARIO'] . '</td>';
        $adic .= '<td>&nbsp; ' . $fila['EMAIL'] . '</td>';
        $adic .= '<td>' . $fila['PERFIL'] . '</td>';
        $adic .= '</tr>';
        $est = '1';
        $msg = 'OK';
    }
    $adic .='</tbody>
				</table>';
    $_SESSION['datosImprimir'] = $adic;
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteUsuarios', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite adic por ser HTML');
    return $respuesta;
}

function enviarNotificacion($id, $log, $idXLog, $nroNot, $usuario, $nombre, $apellido, $estado) { // Envio de notificaciones de Envio/Pago exitoso.
    $resulGetNoficaciones = getNotificacion($id, $log, $idXLog, $nroNot); /* Se genera el array de destinatarios. */

    //list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);

    if ($est == 1) {

//		list($subject, $desNotificacion, $fromNotificacion) = ("\;", $msg);

        $vars = explode("|", $msg);
        $subject = $vars[0];
        $desNotificacion = $vars[1];
        $fromNotificacion = $vars[2];
        unset($vars);


        $to_array = explode(';', $adic); //almacena en una matriz, todos los destinatarios (estaban separados por ',')

        $contenido = '<html><body>';
        $contenido .= '<h2>' . $desNotificacion . '</h2>';
        $contenido .= '<hr />';
        $contenido .= '<p><b>Nombre: </b>' . $nombre;
        $contenido .= '<p><b>Apellido: </b>' . $apellido;
        $contenido .= '<p><b>Usuario: </b>' . $usuario;
        if ($estado == 'S' or $estado == 'N') {
            $contenido .= '<p><b>Campo actualizado-->Estado: </b>' . $estado . '</p>';
        } else {
            $contenido .= '<p><b>Campo actualizado--></b>Clave Acceso: Ver en BD.</p>';
        }
        $contenido .= '<p><b>Autorizado por: </b>' . $_SESSION['usuario'];
        $contenido .= '<p><b>Empresa: </b>' . $_SESSION['codEmpresa'];
        $contenido .= '<p><b>Entidad: </b>' . $_SESSION['codEntidad'];
        $contenido .= '<p>Notificacion enviada el ' . date("d M Y" . " - " . "H:m:s") . '</p>';
        $contenido .= '<hr />';
        $contenido .= '</body></html>';

        foreach ($to_array as $c => $v) {//Se recorre el array de destinatarios y se envía un mail para cada uno.
            $bcc = '<EMAIL>';
            $to = $v;
            if (mail($to, $subject, $contenido, "From: " . $fromNotificacion . "\nReply-To:" . $fromNotificacion . "\nBcc: $bcc\nContent-Type: text/html; charset=iso-8859-1\nContent-Transfer-Encoding: 8bit")) {
                $log->write($idXLog, 0, 'commonFunctions.php/enviarNotificacion', 'Enviado con exito a: ' . $to);
            } else {
                $log->write($idXLog, 2, 'commonFunctions.php/enviarNotificacion', 'No se pudo enviar a: ' . $to);
            }
        }
    } else {
        $log->write($idXLog, 2, 'commonFunctions.php/enviarNotificacion', 'ERROR: ' . $msg);
    }
}

function getNotificacion($id, $log, $idXLog, $nro) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 2, 'commonFunctions.php/maxCodBarrio', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT N.ID_NOTIFICACION, N.DES_NOTIFICACION, U.TO_NOTIFICACION, ";
    $query .= "N.FROM_NOTIFICACION, N.SUBJECT ";
    $query .= "FROM admin.NOTIFICACIONES N, admin.USUARIO_X_NOTIFICACIONES U ";
    $query .= "WHERE N.ID_NOTIFICACION = U.ID_NOTIFICACION ";
    $query .= "AND N.ID_NOTIFICACION = " . $nro;

    $log->write($idXLog, 0, 'commonFunctions.php/getNotificacion', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 2, 'commonFunctions.php/getNotificacion', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 2, 'commonFunctions.php/getNotificacion', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $resul = '';
    $to_array = '';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $to_array .= $fila['TO_NOTIFICACION'] . ';';
        $adic = $to_array;
        $est = '1';
        $msg = $fila['SUBJECT'] . ';' . $fila['DES_NOTIFICACION'] . ';' . $fila['FROM_NOTIFICACION'];
    }
    oci_close($ora_conn);
    $adic = substr($adic, 0, strlen($adic) - 1);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/getNotificacion', 'respuesta: ' . $respuesta);
    return $respuesta;
}

function generarComboAnho($log, $idXLog, $id) {
    $inicial = 2008;
    $actual = date("Y");
    $adic = '<option value="' . $actual . '">' . $actual . '</option>';
    while ($inicial < $actual) {
        $adic .= '<option value="' . $inicial . '">' . $inicial . '</option>';
        $inicial++;
    }
    $est = '1';
    $msg = 'OK';
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteFechaCobroLote', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|' . $adic);
    return $respuesta;
}

function generarComboMes($log, $idXLog, $id, $anho) {
    $anhoActual = date("Y");
    $mesActual = date("m");
    if ($anho == 0)
        $anho = $anhoActual;
    $meses = array(
        1 => "Enero", 2 => "Febrero", 3 => "Marzo", 4 => "Abril",
        5 => "Mayo", 6 => "Junio", 7 => "Julio", 8 => "Agosto",
        9 => "Septiembre", 10 => "Octubre", 11 => "Noviembre", 12 => "Diciembre");

    $adic = '';
    if ($anho <> $anhoActual) {
        for ($i = 1; $i <= 12; $i++) {
            $adic .= '<option value="' . $i . '">' . $meses[$i] . '</option>';
        }
    } else {
        $adic .= '<option value="' . $mesActual . '">' . $meses[(int) $mesActual] . '</option>';
        for ($i = 1; $i <= $mesActual; $i++) {
            if ($i <> $mesActual)
                $adic .= '<option value="' . $i . '">' . $meses[$i] . '</option>';
        }
    }
    $est = '1';
    $msg = 'OK';
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteFechaCobroLote', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|' . $adic);
    return $respuesta;
}

function subRedesResumenSaldo($log, $idXLog, $fecha_inicio, $fecha_fin, $rec, $des_rec, $tipoFecha, $moneda) {
    $id = 13;
    global $log, $idXLog;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, $query);
    $filtroFecha = '';
    if ($moneda > 1) {
        $dec = 2;
    } else {
        $dec = 0;
    }
    if ($tipoFecha == 'A') {
        $filtroFecha = ' a.fecha_ape ';
    } else if ($tipoFecha == 'C') {
        $filtroFecha = ' a.fecha_cie ';
    }
    if (trim($_POST['rec']) == '*') {
        $_SESSION['repName'] = 'Resumen Saldo de todas las recaudadoras ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = " ";
    } else {
        $_SESSION['repName'] = 'Resumen Saldo de la recaudadora ' . $des_rec . ' ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = " and r.cod_empresa='01' and r.cod_recaudadora = " . $rec . " ";
        $recaudadora2 = " and t.cod_empresa='01'  and t.cod_recaudadora = " . $rec . " ";
    }
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
    $query = "SELECT COD_BANCO,
				   DES_BANCO,
				   COD_RECAUDADORA,
				   DES_ENTIDAD,
				   FECHA,
				   DES_MONEDA,
				   DES_TIPO_PAGO,
				   SUM(DEBITO) DEBITO,
				   SUM(DESEMBOLSO) DESEMBOLSO,
				   SUM(DEPOSITO) DEPOSITO,
				   SUM((DESEMBOLSO + DEPOSITO) - DEBITO) AS SALDO,
				   MH
			  FROM (
					--COBRO DE SERVICIOS - EXCLUYE BPOS
					SELECT B1.COD_BANCO,
							B1.DES_BANCO,
							R.COD_RECAUDADORA,
							E.DES_ENTIDAD,
							TO_CHAR($filtroFecha, 'DD/MM/YYYY') FECHA,
							M.DES_MONEDA,
							TP.DES_TIPO_PAGO,
							SUM(DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0)) DEBITO,
							SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,
							0 AS DEPOSITO,
							'N' AS MH
					  FROM ADMIN.TRANSACCION     TR,
							ADMIN.BANCO           B1,
							ADMIN.ENTIDAD         E,
							ADMIN.MONEDA          M,
							ADMIN.TIPO_PAGO       TP,
							ADMIN.RECAUDADORA     R,
							ADMIN.APERTURA_CIERRE A,
							ADMIN.TERMINAL        TE,
							ADMIN.SERVICIO        S
					 WHERE NVL(TR.COD_BANCO_RECAUDADORA, 98) = B1.COD_BANCO
					   AND TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND TR.COD_EMPRESA = R.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = R.COD_RECAUDADORA
					   AND E.COD_EMPRESA = R.COD_EMPRESA
					   AND E.COD_ENTIDAD = R.COD_RECAUDADORA
					   AND TR.COD_MONEDA = M.COD_MONEDA
					   AND TR.COD_EMPRESA = A.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
					   AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
					   AND TR.NRO_CAJA = A.NRO_CAJA
					   AND TR.NRO_LOTE = A.NRO_LOTE
					   AND TR.ID_TERMINAL = A.ID_TERMINAL
					   AND TR.ID_TERMINAL = TE.ID_TERMINAL
					   AND TR.COD_EMPRESA = S.COD_EMPRESA
					   AND TR.COD_EMISORA = S.COD_EMISORA
					   AND TR.COD_SERVICIO = S.COD_SERVICIO
					   AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
					   AND NOT (TE.COD_TIPO_POS = 1 AND TE.ID_MODELO_POS = 6) -- EXCLUYE BPOS
					   AND $filtroFecha >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND $filtroFecha < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   $recaudadora
					   AND B1.COD_BANCO = 98
					   AND TR.COD_TIPO_TRANSACCION = 3
					   AND (TR.ENLINEA = 'N' OR
							(TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
					   AND TR.ANULADO = 'N'
					   AND TR.COD_SERVICIO != 1123
					   AND M.COD_MONEDA = " . $moneda . "
					 GROUP BY B1.COD_BANCO,
							   B1.DES_BANCO,
							   R.COD_RECAUDADORA,
							   E.DES_ENTIDAD,
							   TO_CHAR($filtroFecha, 'DD/MM/YYYY'),
							   M.DES_MONEDA,
							   TP.DES_TIPO_PAGO,
							   TR.COD_SERVICIO
					
					UNION ALL
					
					--COBRO DE SERVICIOS - INCLUYE SOLO BPOS
					SELECT B1.COD_BANCO,
						   B1.DES_BANCO,
						   R.COD_RECAUDADORA,
						   E.DES_ENTIDAD,
						   TO_CHAR(TR.FECPAGO, 'DD/MM/YYYY') FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   SUM(DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0)) DEBITO,
						   SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,
						   0 AS DEPOSITO,
						   'N' AS MH

					  FROM ADMIN.TRANSACCION     TR,
						   ADMIN.BANCO           B1,
						   ADMIN.ENTIDAD         E,
						   ADMIN.MONEDA          M,
						   ADMIN.TIPO_PAGO       TP,
						   ADMIN.RECAUDADORA     R,
						   ADMIN.APERTURA_CIERRE A,
						   ADMIN.TERMINAL        TE,
						   ADMIN.SERVICIO        S
					 WHERE NVL(TR.COD_BANCO_RECAUDADORA, 98) = B1.COD_BANCO
					   AND TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND TR.COD_EMPRESA = R.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = R.COD_RECAUDADORA
					   AND E.COD_EMPRESA = R.COD_EMPRESA
					   AND E.COD_ENTIDAD = R.COD_RECAUDADORA
					   AND TR.COD_MONEDA = M.COD_MONEDA
					   AND TR.COD_EMPRESA = A.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
					   AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
					   AND TR.NRO_CAJA = A.NRO_CAJA
					   AND TR.NRO_LOTE = A.NRO_LOTE
					   AND TR.ID_TERMINAL = A.ID_TERMINAL
					   AND TR.ID_TERMINAL = TE.ID_TERMINAL
					   AND TR.COD_EMPRESA = S.COD_EMPRESA
					   AND TR.COD_EMISORA = S.COD_EMISORA
					   AND TR.COD_SERVICIO = S.COD_SERVICIO
					   AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
					   AND (TE.COD_TIPO_POS = 1 AND TE.ID_MODELO_POS = 6) -- SOLO BPOS
					   AND TR.FECPAGO >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND TR.FECPAGO < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   " . $recaudadora . "
					   AND B1.COD_BANCO = 98
					   AND TR.COD_TIPO_TRANSACCION = 3
					   AND (TR.ENLINEA = 'N' OR
							(TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
					   AND TR.ANULADO = 'N'
					   AND TR.COD_SERVICIO != 1123
					   AND M.COD_MONEDA = " . $moneda . "
					 GROUP BY B1.COD_BANCO,
							  B1.DES_BANCO,
							  R.COD_RECAUDADORA,
							  E.DES_ENTIDAD,
							  TO_CHAR(TR.FECPAGO, 'DD/MM/YYYY'),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  TR.COD_SERVICIO
					
					UNION ALL
					
					--COBRANZAS DE HACIENDA
					SELECT T.COD_BANCO,
						   B.DES_BANCO,
						   T.COD_RECAUDADORA,
						   E.DES_ENTIDAD,
						   TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY') AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   T.PAGOS_MH AS MH
					  FROM ADMIN.CONTROL_SOBREGIRO T,
						   ADMIN.ENTIDAD           E,
						   ADMIN.BANCO             B,
						   ADMIN.MONEDA            M,
						   ADMIN.TIPO_PAGO         TP,
						   ADMIN.RECAUDADORA       R
					 WHERE T.COD_BANCO = B.COD_BANCO
					   AND T.COD_EMPRESA = E.COD_EMPRESA
					   AND T.COD_RECAUDADORA = E.COD_ENTIDAD
					   AND T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.COD_EMPRESA = R.COD_EMPRESA
					   AND T.COD_RECAUDADORA = R.COD_RECAUDADORA
					   AND T.PAGOS_MH = 'S'
					   AND T.CONCILIADO = 'S'
					   AND M.COD_MONEDA = " . $moneda . "
					   AND T.FECHA_OPERACION >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND T.FECHA_OPERACION < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					  " . $recaudadora . "
					 GROUP BY T.COD_BANCO,
							  B.DES_BANCO,
							  T.COD_RECAUDADORA,
							  E.DES_ENTIDAD,
							  TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY'),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH
					UNION ALL
					
					--DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)       
					SELECT T.COD_BANCO,
						   B.DES_BANCO,
						   T.COD_RECAUDADORA,
						   E.DES_ENTIDAD,
						   TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY') AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   0 DEBITO,
						   0 DESEMBOLSO,
						   SUM(DECODE(T.TIPO_MOVIMIENTO, 'C', T.IMPORTE, 0)) DEPOSITO,
						   T.PAGOS_MH AS MH
					  FROM ADMIN.CONTROL_SOBREGIRO T,
						   ADMIN.ENTIDAD           E,
						   ADMIN.BANCO             B,
						   ADMIN.MONEDA            M,
						   ADMIN.TIPO_PAGO         TP,
						   ADMIN.RECAUDADORA       R
					 WHERE T.COD_BANCO = B.COD_BANCO
					   AND T.COD_EMPRESA = E.COD_EMPRESA
					   AND T.COD_RECAUDADORA = E.COD_ENTIDAD
					   AND T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.COD_EMPRESA = R.COD_EMPRESA
					   AND T.COD_RECAUDADORA = R.COD_RECAUDADORA
					   AND T.CONCILIADO = 'S'
					   AND T.FECHA_OPERACION >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND T.FECHA_OPERACION < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   $recaudadora
					   AND M.COD_MONEDA =  " . $moneda . "
					
					 GROUP BY T.COD_BANCO,
							  B.DES_BANCO,
							  T.COD_RECAUDADORA,
							  E.DES_ENTIDAD,
							  TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY'),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH
					UNION ALL
					
					--CHEQUES RECHAZADOS
					SELECT T.COD_BANCO,
						   B.DES_BANCO,
						   T.COD_RECAUDADORA,
						   E.DES_ENTIDAD,
						   TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY') AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   'CHQ.RECHAZADO' MH
					  FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
						   ADMIN.BANCO                B,
						   ADMIN.ENTIDAD              E,
						   ADMIN.MONEDA               M,
						   ADMIN.TIPO_PAGO            TP
					 WHERE T.COD_BANCO = B.COD_BANCO
					   AND T.COD_EMPRESA = E.COD_EMPRESA
					   AND T.COD_RECAUDADORA = E.COD_ENTIDAD
					   AND T.COD_MONEDA = M.COD_MONEDA
						  
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.FECHA_OPERACION >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND T.FECHA_OPERACION < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   " . $recaudadora2 . "
					   AND M.COD_MONEDA = " . $moneda . "
					 GROUP BY T.COD_BANCO,
							  B.DES_BANCO,
							  T.COD_RECAUDADORA,
							  E.DES_ENTIDAD,
							  TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY'),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH
					
					)
			 GROUP BY COD_BANCO,
					  DES_BANCO,
					  COD_RECAUDADORA,
					  DES_ENTIDAD,
					  FECHA,
					  DES_MONEDA,
					  DES_TIPO_PAGO,
					  MH
			 ORDER BY DES_ENTIDAD, FECHA, MH, DES_TIPO_PAGO";

    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" class="tablesorter">
					<thead>
						<tr>
							<th>Banco</th>
                            <th>Cod. Entidad</th>
							<th>Entidad</th>
							<th>Fecha</th>
                            <th>Nro.Cuenta</th>
							<th>Moneda</th>							
							<th>Pago</th>
							<th>Debito</th>
							<th>Desembolso</th>
							<th>Deposito</th>							
							<th>Saldo</th>
                            <th>MH</th>
						</tr>
					</thead>
					';

    $imp = "		<table cellspacing='1' class='tablesorter'>
					  <thead>
						<tr>
							<th>Banco</th>
                            <th>Cod. Entidad</th>
							<th>Entidad</th>
							<th>Fecha</th>
                            <th>Nro.Cuenta</th>
							<th>Moneda</th>							
							<th>Pago</th>
							<th>Debito</th>
							<th>Desembolso</th>
							<th>Deposito</th>							
							<th>Saldo</th>
                            <th>MH</th>
						</tr>
					</thead>";
    $tDebito = 0;
    $tCredito = 0;
    $tDeposito = 0;
    $tDesembolso = 0;
    $tSaldo = 0;
    $adic = '<tbody>';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $tDebito = $tDebito + $fila['DEBITO'];
        $tDesembolso = $tDesembolso + $fila['DESEMBOLSO'];
        $tDeposito = $tDeposito + $fila['DEPOSITO'];
        $tSaldo = $tSaldo + $fila['SALDO'];
        $nroCuenta = calculoNroCuentaSubRed($fila['COD_RECAUDADORA']);

        $adic .= "<tr>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_BANCO'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['COD_RECAUDADORA'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_ENTIDAD'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['FECHA'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $nroCuenta . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_MONEDA'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_TIPO_PAGO'] . "</td>";
        $adic .= "<td width='100px' align='right'>" . numberFormater($fila['DEBITO'], $dec) . "</td>";
        $adic .= "<td width='100px' align='right'>" . numberFormater($fila['DESEMBOLSO'], $dec) . "</td>";
        $adic .= "<td width='100px' align='right'>" . numberFormater($fila['DEPOSITO'], $dec) . "</td>";
        $adic .= "<td width='100px' " . $styleTD . " align='right'>" . numberFormater($fila['SALDO'], $dec) . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['MH'] . "</td>";
        $adic .= "</tr>";
    }

    $adic .= "</tbody><tr>";
    $adic .= "<th width='100px' align='left' colspan='7'><strong>Total</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tDebito, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tDesembolso, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tDeposito, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tSaldo, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'> </th>";
    $adic .= "</tr>";


    $_SESSION['datosImprimir'] = $imp . $adic . "</table>";
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function subRedesMovimientosRecaudadoras($log, $idXLog, $fecha1_inicio, $fecha1_fin, $fecha2_inicio, $fecha2_fin, $rec, $des_rec, $conciliado, $moneda) {
    $id = 14;
    if (trim($rec) == '*') {
        $_SESSION['repName'] = 'Movimiento Detallado de todas las recaudadoras ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = "";
    } else {
        $_SESSION['repName'] = 'Movimiento Detallado de la recaudadora ' . $des_rec . ' ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = " and t.cod_recaudadora = " . $rec;
    }
    if ($moneda > 1) {
        $dec = 2;
    } else {
        $dec = 0;
    }
    if (trim($conciliado) == '*') {
        $conciliado = "";
    } else {
        $conciliado = " and t.conciliado = '$conciliado' ";
    }
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesMovimientosRecaudadoras', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
    $query = "
		select t.cod_banco, b.des_banco, t.cod_recaudadora, e.des_entidad,
		t.fecha_operacion, m.des_moneda, tp.des_tipo_pago,
		decode(t.tipo_movimiento, 'D', t.importe, 0) DEBITO,
		decode(t.tipo_movimiento, 'C', t.importe, 0) CREDITO,
		T.FECHA_INSERCION, t.conciliado
		from admin.control_sobregiro t, admin.entidad e, admin.banco b, admin.moneda m, admin.tipo_pago tp,
		admin.recaudadora r
		where t.cod_banco = b.cod_banco
		and t.fecha_operacion >= nvl(to_date('$fecha1_inicio', 'DD/MM/YYYY'), t.fecha_operacion) 
		and t.fecha_operacion < nvl(to_date('$fecha1_fin', 'DD/MM/YYYY'), t.fecha_operacion) + 1 
		and t.FECHA_INSERCION >= nvl(to_date('$fecha2_inicio', 'DD/MM/YYYY'), t.FECHA_INSERCION) 
		and t.FECHA_INSERCION < nvl(to_date('$fecha2_fin', 'DD/MM/YYYY'), t.FECHA_INSERCION) + 1 
		$recaudadora $conciliado  		
		and t.cod_empresa = e.cod_empresa
		and t.cod_recaudadora = e.cod_entidad
		and t.cod_moneda = m.cod_moneda
		and t.cod_tipo_pago = tp.cod_tipo_pago
		and t.cod_empresa = r.cod_empresa
		and t.cod_recaudadora = r.cod_recaudadora
                and t.cod_moneda=" . $moneda . "
		ORDER BY t.fecha_operacion, t.cod_recaudadora, t.cod_moneda, 
		t.cod_tipo_pago, t.cod_operacion
	";
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesMovimientosRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesMovimientosRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" class="tablesorter">
					<thead>
						<tr>
							<th>Banco</th>
                                                        <th>Cod.Entidad</th>
							<th>Entidad</th>
							<th>Fecha Proceso</th>
							<th>Fecha Ingreso</th>																					
							<th>Moneda</th>							
							<th>Pago</th>
							<th>Conciliado</th>																												
							<th>Debito</th>
							<th>Credito</th>							
						</tr>
					</thead>
					';

    $imp = "			<tr>
							<td align='center'><strong>Banco</strong></td>
                                                        <td align='center'><strong>Cod. Entidad</strong></td>
							<td align='center'><strong>Entidad</strong></td>
							<td align='center'><strong>Fecha Proceso</strong></td>
							<td align='center'><strong>Fecha Ingreso</strong></td>									
							<td align='center'><strong>Moneda</strong></td>							
							<td align='center'><strong>Pago</strong></td>							
							<td align='center'><strong>Conciliado</strong></td>																				
							<td align='center'><strong>Debito</strong></td>
							<td align='center'><strong>Credito</strong></td>							
						</tr>
	";
    $tDebito = 0;
    $tCredito = 0;
    $tSaldo = 0;
    $adic = '<tbody>';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $tDebito = $tDebito + $fila['DEBITO'];
        $tCredito = $tCredito + $fila['CREDITO'];
        $tSaldo = $tSaldo + $fila['SALDO'];

        $adic .= "<tr>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_BANCO'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['COD_RECAUDADORA'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_ENTIDAD'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['FECHA_OPERACION'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['FECHA_INSERCION'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_MONEDA'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DES_TIPO_PAGO'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['CONCILIADO'] . "</td>";
        $adic .= "<td width='100px' align='right'>" . numberFormater($fila['DEBITO'], $dec) . "</td>";
        $adic .= "<td width='100px' align='right'>" . numberFormater($fila['CREDITO'], $dec) . "</td>";
        $adic .= "</tr>";
    }

    $adic .= "</tbody><tr>";
    $adic .= "<th width='100px' align='left' colspan='8'><strong>Total</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tDebito, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($tCredito, $dec) . "</strong></th>";
    $adic .= "</tr>";


    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/subRedesMovimientosRecaudadoras', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function rankingRecaudadoras($log, $idXLog, $fecha_inicio, $fecha_fin, $top, $codEmpresa) {
    if ($top == '') {
        $top = 10;
    }
    $_SESSION['repName'] = 'Ranking de transacciones por recaudadoras';
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/rankingRecaudadoras@coneccionInicial', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $inicioF4 = microtime(true);
    $query = "
select v.PROCESADOR, v.COD_RECAUDADORA cod_entidad, e.des_entidad 
descripcion, sum(v.q_trx_cob) cant_trx,
sum(v.importe_cob) importe, sum(v.com_boca_cob) comision_rec, 
sum(com_banco_cob) comision_banco, sum(v.com_pronet_cob) 
comision_procesador, sum(com_aval_cob) comision_aval
from admin.VM_CONSOLIDADO_3_CIERRE v, admin.entidad e
where v.COD_RECAUDADORA = e.cod_entidad
and v.cod_empresa = $codEmpresa
and v.FECHA_PROCESO >= to_date('$fecha_inicio', 'DD/MM/YYYY')
and v.FECHA_PROCESO < to_date('$fecha_fin', 'DD/MM/YYYY')
and UPPER(v.red) = 'SERVICIOS'
group by v.PROCESADOR, v.COD_RECAUDADORA, e.des_entidad";
    /* union all
      select 'Stream' Procesador, g.identificador cod_entidad, g.desc_grupo
      descripcion, count(*) cant_trx, sum(monto) importe,
      sum(o.comision_rec) comision_rec, sum(o.comision_banco) comision_banco,
      sum(o.comision_stream) comision_procesador,
      sum(o.comision_aval) comision_aval
      from vm_operaciones@dblink_stream o, recaudadores@dblink_stream r, grupos@dblink_stream g
      where o.id_recaudador = r.id_recaudador
      and r.id_grupo = g.identificador
      and o.fec_procesamiento >= to_date('$fecha_inicio', 'DD/MM/YYYY')
      and o.fec_procesamiento < to_date('$fecha_fin', 'DD/MM/YYYY')
      and o.tipo_trans in (15, 5)
      and o.Id_Facturador != 24
      group by g.identificador, g.desc_grupo
      order by cant_trx desc
      "; */
    $log->write($idXLog, 0, 'commonFunctions.php/rankingRecaudadoras', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/rankingRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/rankingRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>Procesador</th>
							<th>Entidad</th>
							<th>CantTrx</th>
							<th>Importe</th>							
							<th>Recaudadora</th>
							<th>Banco</th>
							<th>Procesadora</th>							
							<th>Aval</th>														
						</tr>
					</thead>
					';

    $imp = "			<tr>
							<td align='center'><strong>Procesador</strong></td>
							<td align='center'><strong>Entidad</strong></td>
							<td align='center'><strong>CantTrx</strong></td>
							<td align='center'><strong>Importe</strong></td>							
							<td align='center'><strong>Recaudadora</strong></td>							
							<td align='center'><strong>Banco</strong></td>
							<td align='center'><strong>Procesadora</strong></td>							
							<td align='center'><strong>Aval</strong></td>														
						</tr>
	";

    $xml = " <graph 
				xaxisname='Entidades ' 
				yaxisname='Importe' 
				hovercapbg='DEDEBE'
				hovercapborder='889E6D' 
				rotateNames='1' 
				numdivlines='9' 
				divLineColor='CCCCCC'
				divLineAlpha='80' 
				showValues='0' 
				showNames='0'
				decimalPrecision='0' 
				thousandSeparator='.' 
				formatNumberScale = '0' 
				showAlternateHGridColor='1' 
				AlternateHGridAlpha='30'
				AlternateHGridColor='CCCCCC' 
				caption='Top $top " . $_SESSION['repName'] . "' 
			>\n";
    $adic = '<tbody>';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $t_cant = $t_cant + $fila['CANT_TRX'];
        $t_importe = $t_importe + $fila['IMPORTE'];
        $t_comrec = $t_comrec + $fila['COMISION_REC'];
        $t_compronet = $t_compronet + $fila['COMISION_PROCESADOR'];
        $t_combanco = $t_combanco + $fila['COMISION_BANCO'];
        $t_aval = $t_aval + $fila['COMISION_AVAL'];

        $adic .= "<tr>";
        $adic .= "<td width='100px' align='center'>" . $fila['PROCESADOR'] . "</td>";
        $adic .= "<td width='100px' align='center'>" . $fila['DESCRIPCION'] . "</td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['CANT_TRX'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['IMPORTE'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMISION_REC'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMISION_BANCO'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMISION_PROCESADOR'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMISION_AVAL'], 0) . "</span></td>";
        $adic .= "</tr>";
        if ($filaActual < $top) {
            $xml .= "<set name='" . $fila['DESCRIPCION'] . "' value='" . $fila['CANT_TRX'] . "' color='AFD8F8'/>" . "\n";
        }
        $filaActual++;
    }
    $xml .= "</graph>";

    $adic .= "</tbody>";
    $adic .= "<tr>";
    $adic .= "<th colspan='2' align='center'><strong>Totales</strong></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_cant, 0) . "</span></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_importe, 0) . "</span></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_comrec, 0) . "</span></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_combanco, 0) . "</span></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_pronet, 0) . "</span></th>";
    $adic .= "<th align='right'><span style='float:right'>" . numberFormater($t_aval, 0) . "</span></th>";
    $adic .= "</tr>";


    $_SESSION['repGrap'] = $xml;
    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/histograma', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');




    return $respuesta;
}

function histograma($log, $idXLog, $id, $fecha_inicio, $fecha_fin, $corte, $recaudadora, $emisora, $desde, $hasta, $codEmpresa) {
    $_SESSION['repGrap'] = '';
    $recaudadora = explode("|", $recaudadora);
    $emisora = explode("|", $emisora);
    $_SESSION['repName'] = 'Histograma de Comisiones ';
    if ($corte == 'dd-mm') {
        $_SESSION['repName'] .= 'Diario ';
    } else {
        $_SESSION['repName'] .= 'Mesual ';
    }
    $_SESSION['repName'] .= 'periodo ' . $fecha_inicio . ' a ' . $fecha_fin . ' Emisora: ' . $emisora[1] . ' Recaudadora: ' . $recaudadora[1];
    $emi = '';
    if ($emisora[0] != '') {
        $emi = " AND t.cod_emisora = " . $emisora[0] . " ";
    }
    $rec = '';
    if ($recaudadora[0] != '') {
        $rec = " AND t.COD_RECAUDADORA = " . $recaudadora[0] . " ";
    }
    $des = '';
    if ($desde != '') {
        $des = " and t.IMPORTE > $desde ";
        $_SESSION['repName'] .= " desde: " . numberFormater($desde, 0);
    }
    $has = '';
    if ($hasta != '') {
        $has = " and t.IMPORTE <= $hasta ";
        $_SESSION['repName'] .= " hasta: " . numberFormater($hasta, 0);
    }



    $log->write($idXLog, 0, 'commonFunctions.php/histograma', $fecha_inicio . ' ' . $fecha_fin . ' ' . $corte . ' ' . $recaudadora . ' ' . $emisora . ' ' . $desde . ' ' . $hasta);

    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/histograma', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
    $query = "
select TO_CHAR(mes, '$corte') mes, sum(cant) cant_trx, sum(importe) importe, sum(comrec) comrec, sum(compronet) compronet, sum(combanco) combanco
from
(      SELECT   'reing' cta, 
               t.FECHA_PROCESO mes,
               count(*) cant, 
               sum(t.importe) importe, 
               sum(t.com_rec) comrec,
               sum(t.com_proc) compronet, 
               sum(t.com_banco) combanco
FROM admin.vw_trans_clearing t 
WHERE t.FECHA_PROCESO >= to_date('$fecha_inicio', 'DD/MM/YYYY')
       AND t.FECHA_PROCESO <= to_date('$fecha_fin', 'DD/MM/YYYY')
	   $rec $emi $des $has 
       AND t.cod_empresa = $codEmpresa
       AND t.cod_tipo_transaccion = 3 
group by 'reing', t.FECHA_PROCESO
) 
group by mes 
order by mes
";

    $log->write($idXLog, 0, 'commonFunctions.php/histograma', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/histograma', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/histograma', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>Fecha</th>
							<th>Transacciones</th>
							<th>Importe</th>
							<th>Recaudadora</th>							
							<th>Pronet</th>
							<th>Banco</th>
						</tr>
					</thead>
					';

    $imp = "			<tr>
							<td align='center'><strong>Fecha</strong></td>
							<td align='center'><strong>Transacciones</strong></td>
							<td align='center'><strong>Importe</strong></td>
							<td align='center'><strong>Recaudadora</strong></td>							
							<td align='center'><strong>Pronet</strong></td>
							<td align='center'><strong>Banco</strong></td>
						</tr>
	";

    $rep = " <graph 
				xaxisname='Comisiones' 
				yaxisname='Importe' 
				hovercapbg='DEDEBE'
				hovercapborder='889E6D' 
				rotateNames='0' 
				numdivlines='9' 
				divLineColor='CCCCCC'
				divLineAlpha='80' 
				showValues='0' 
				decimalPrecision='0' 
				thousandSeparator='.' 
				formatNumberScale = '0' 
				showAlternateHGridColor='1' 
				AlternateHGridAlpha='30'
				AlternateHGridColor='CCCCCC' 
				caption='" . $_SESSION['repName'] . "' 
			>\n";

    $t_cant = 0;
    $t_importe = 0;
    $t_comrec = 0;
    $t_compronet = 0;
    $t_combanco = 0;
    $categorias = "";
    $comRecaudadora = "";
    $comPronet = "";
    $comBanco = "";
    $adic .= "<tbody>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $t_cant = $t_cant + $fila['CANT_TRX'];
        $t_importe = $t_importe + $fila['IMPORTE'];
        $t_comrec = $t_comrec + $fila['COMREC'];
        $t_compronet = $t_compronet + $fila['COMPRONET'];
        $t_combanco = $t_combanco + $fila['COMBANCO'];

        $adic .= "<tr>";
        $adic .= "<td width='80px'>" . $fila['MES'] . "</td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['CANT_TRX'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['IMPORTE'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMREC'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMPRONET'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['COMBANCO'], 0) . "</span></td>";
        $adic .= "</tr>";

        $categorias .= "<category name='" . $fila['MES'] . "' hoverText='" . $fila['MES'] . "'/>" . "\n";
        $comRecaudadora .= "<set value='" . $fila['COMREC'] . "' />\n";
        $comPronet .= "<set value='" . $fila['COMPRONET'] . "' />\n";
        $comBanco .= "<set value='" . $fila['COMBANCO'] . "' />\n";
    }

    $adic .= "</tbody>";
    $adic .= "<tr>";
    $adic .= "<th><strong>Totales</strong></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right'><strong>" . numberFormater($t_cant, 0) . "</strong></span></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right'><strong>" . numberFormater($t_importe, 0) . "</strong></span></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right'><strong>" . numberFormater($t_comrec, 0) . "</strong></span></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right'><strong>" . numberFormater($t_compronet, 0) . "</strong></span></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right'><strong>" . numberFormater($t_combanco, 0) . "</strong></span></th>";
    $adic .= "</tr>";


    $categorias = "\n" . "<categories font='Comisiones' fontSize='11' fontColor='000000'>$categorias</categories>" . "\n";
    $comRecaudadora = "\n" . "<dataset seriesname='Recaudadora' color='FDC12E'>$comRecaudadora</dataset>" . "\n";
    $comPronet = "\n" . "<dataset seriesname='Pronet' color='56B9F9'>$comPronet</dataset>" . "\n";
    $comBanco = "\n" . "<dataset seriesname='Banco' color='C9198D' >$comBanco</dataset>" . "\n";
    $rep .= $categorias;
    $rep .= $comRecaudadora;
    $rep .= $comPronet;
    $rep .= $comBanco;
    $rep .= "\n" . "</graph>" . "\n";

    $_SESSION['repGrap'] = $rep;
    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/histograma', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function consolidadoRecaudadoras($log, $idXLog, $id, $fecha1_inicio, $fecha1_fin, $fecha2_inicio, $fecha2_fin, $recaudadora, $des_rec, $codEmpresa) {


    $_SESSION['repName'] = 'Consolidado Recaudadoras' . date("d-m-Y");
    if ($recaudadora != '') {
        $recaudadora = "recaudadora = $recaudadora";
        $_SESSION['repName'] .= ' (' . $des_rec . ')';
    }

    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoRecaudadoras', $fecha_inicio . ' ' . $fecha_fin . ' ' . $recaudadora . ' ' . $des_rec);
    // Gerenacion de reportes por rango de fecha de cobro y lote, en transaccion (reingenieria.)
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoRecaudadoras', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
    $query = "
select
        cta,   
        emisora, 
        servicio,
        banco,
        fecha_transaccion,
        fecha_pago,
        sum(cant) cant_trx,
		sum(efectivo) efectivo, 
		sum(cheque) cheque, 
        sum(importe) as importe,
        sum(comrec) as comrec,
        sum(compronet) as compronet,
        sum(combanco) as combanco,
        suc
from ("; /* SELECT 'impuacum' cta,
      (select denominacion from rpresta@dblink_corte s where s.id_prestadora = B.id_prestadora) as emisora,
      (select s.descrip from impuserv@dblink_corte s where s.id_prestadora = B.id_prestadora and s.codigo_impuesto = B.codigo_impuesto) as servicio,
      (select s.denominacion from BANCOS@dblink_corte s where s.id_banco = B.cod_banco) as banco,
      to_char(b.fecha_hora_transmision, 'dd-mm-yyyy') fecha_transaccion,
      to_char(b.fecha_pago, 'dd-mm-yyyy') fecha_pago,
      count(*) cant,
      sum(case when(tipo_pago = 'C') then b.importe else 0 end) as efectivo,
      sum(case when(tipo_pago <> 'C') then b.importe else 0 end) as cheque,
      sum(b.importe) importe,
      sum(b.com_recaudadora + b.iva_recaudadora) comrec,
      sum(b.com_pronet + b.iva_pronet) compronet,
      sum(b.com_bclearing + b.iva_bclearing) combanco,
      (select des_sucursal_mh from admin.sucursal_mh where cod_sucursal = cod_sucursal_mh) suc
      FROM impuacum@dblink_corte B
      WHERE b.fecha_hora_transmision >= nvl(to_date('$fecha1_inicio', 'DD/MM/YYYY'), b.fecha_hora_transmision)
      AND b.fecha_hora_transmision <= nvl(to_date('$fecha1_fin', 'DD/MM/YYYY'), b.fecha_hora_transmision)

      AND b.fecha_pago >= nvl(to_date('$fecha2_inicio', 'DD/MM/YYYY'), b.fecha_pago)
      AND b.fecha_pago < nvl(to_date('$fecha2_fin', 'DD/MM/YYYY'), b.fecha_pago) +1


      AND b.id_recaudadora = (select cod_rec_ant from admin.recaudadora where cod_$recaudadora)
      AND b.flag_anulacion <> '*'
      group by id_prestadora, codigo_impuesto, cod_banco, to_char(b.fecha_hora_transmision, 'dd-mm-yyyy'), to_char(b.fecha_pago, 'dd-mm-yyyy'), cod_sucursal
      union all
      SELECT  'transhist' cta,
      (select denominacion from rpresta@dblink_corte s where s.id_prestadora = B.id_prestadora) as emisora,
      (select s.descrip from impuserv@dblink_corte s where s.id_prestadora = B.id_prestadora and s.codigo_impuesto = B.codigo_impuesto) as servicio,
      (select s.denominacion from BANCOS@dblink_corte s where s.id_banco = B.cod_banco) as banco,
      to_char(b.fecha_proceso, 'dd-mm-yyyy') fecha_transaccion,
      to_char(b.fecha_trans, 'dd-mm-yyyy') fecha_pago,
      count(*) cant,
      sum(case when(tipo_pago = 'C') then b.importe else 0 end) as efectivo,
      sum(case when(tipo_pago <> 'C') then b.importe else 0 end) as cheque,
      sum(b.importe) importe,
      sum(b.com_recaudadora + b.iva_recaudadora) comrec,
      sum(b.com_pronet + b.iva_pronet) compronet,
      sum(b.com_bclearing + b.iva_bclearing) combanco,
      (select des_sucursal_mh from admin.sucursal_mh where cod_sucursal = cod_sucursal_mh) suc
      FROM transhist@dblink_corte B
      WHERE b.fecha_proceso >= nvl(to_date('$fecha1_inicio', 'DD/MM/YYYY'), b.fecha_proceso)
      AND b.fecha_proceso < nvl(to_date('$fecha1_fin', 'DD/MM/YYYY'), b.fecha_proceso)+1

      AND b.fecha_trans >= nvl(to_date('$fecha2_inicio', 'DD/MM/YYYY'), b.fecha_trans)
      AND b.fecha_trans < nvl(to_date('$fecha2_fin', 'DD/MM/YYYY'), b.fecha_trans)+1

      AND b.id_recaudadora = (select cod_rec_ant from admin.recaudadora where cod_$recaudadora)
      AND b.estado = 'C'
      and b.confirmado = 'C'
      group by id_prestadora, codigo_impuesto, cod_banco, to_char(b.fecha_proceso, 'dd-mm-yyyy'), to_char(b.fecha_trans, 'dd-mm-yyyy'), cod_sucursal
      union all */
    $query .="
      SELECT   'reing' cta,
              (select s.abreviatura from admin.emisora s where s.cod_emisora = t.cod_emisora) as emisora, 
              (select s.des_servicio from admin.servicio s where s.cod_emisora = t.cod_emisora and s.cod_servicio = t.cod_servicio) as servicio, 
 			  (select s.des_banco from admin.banco s where s.cod_banco = t.cod_banco_emisor) as banco, 
               TO_CHAR(t.FECHA_PROCESO,'dd-mm-yyyy') fecha_transaccion,
               TO_CHAR(t.FECPAGO,'dd-mm-yyyy') fecha_pago,
               count(*) cant,
               sum(case when(cod_tipo_pago = 1) then t.importe else 0 end) as efectivo,
               sum(case when(cod_tipo_pago <> 1) then t.importe else 0 end) as cheque,                                                
               sum(t.importe) importe,
               sum(t.com_rec) comrec,
               sum(t.com_proc)compronet,
               sum(t.com_banco)combanco,
             (select des_sucursal from admin.sucursal where cod_sucursal = cod_sucursal_rec) suc			   
FROM admin.vw_trans_clearing t
WHERE t.cod_empresa = $codEmpresa 
AND t.FECHA_PROCESO >= nvl(to_date('$fecha1_inicio', 'DD/MM/YYYY'), t.FECHA_PROCESO)
AND t.FECHA_PROCESO < nvl(to_date('$fecha1_fin', 'DD/MM/YYYY'), t.FECHA_PROCESO)+1

AND t.FECPAGO >= nvl(to_date('$fecha2_inicio', 'DD/MM/YYYY'), t.FECPAGO)
AND t.FECPAGO < nvl(to_date('$fecha2_fin', 'DD/MM/YYYY'), t.FECPAGO)+1


       AND t.cod_$recaudadora
       AND t.cod_tipo_transaccion = 3
group by cod_emisora, cod_servicio, cod_banco_emisor, TO_CHAR(t.FECHA_PROCESO,'dd-mm-yyyy'), TO_CHAR(t.FECPAGO,'dd-mm-yyyy'), cod_sucursal_rec
)
group by cta, emisora, servicio, banco, fecha_transaccion, fecha_pago, suc
order by fecha_transaccion
";


    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoRecaudadoras', 'Query2: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoRecaudadoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>Reporte de Comisiones Consolidado Recaudadora ' . $des_rec . '.</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/pdf.php" target="_blank"><img width="40px" height="41px" src="img/pdf.jpg" border="0"></a>				
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>Cuenta</th>
							<th>Emisora</th>							
							<th>Servicio</th>
							<th>Banco</th>
							<th>Sucursal</th>														
							<th>Proceso</th>
							<th>Pago</th>
							<th>Transacciones</th>
							<th>Efectivo</th>
							<th>Cheque</th>							
							<th>Importe</th>
							<th>Comision Recaudadora</th>							
							<th>Comision Pronet</th>
							<th>Comision Banco</th>
						</tr>
					</thead>
					';

    $imp = "
						<tr>
							<td align='center'><strong>Cuenta</strong></td>
							<td align='center'><strong>Emisora</strong></td>							
							<td align='center'><strong>Servicio</strong></td>
							<td align='center'><strong>Banco</strong></td>
							<td align='center'><strong>Sucursal</strong></td>														
							<td align='center'><strong>Proceso</strong></td>
							<td align='center'><strong>Pago</strong></td>
							<td align='center'><strong>Trxs</strong></td>
							<td align='center'><strong>Efectivo</strong></td>
							<td align='center'><strong>Cheque</strong></td>							
							<td align='center'><strong>Importe</strong></td>
							<td align='center'><strong>Recaudadora</strong></td>							
							<td align='center'><strong>Pronet</strong></td>
							<td align='center'><strong>Banco</strong></td>
						</tr>	
	";
    $adic = '<tbody>';
    $t_cant = 0;
    $t_importe = 0;
    $t_comrec = 0;
    $t_compronet = 0;
    $t_combanco = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $t_cant = $t_cant + $fila['CANT_TRX'];
        $t_importe = $t_importe + $fila['IMPORTE'];
        $t_efectivo = $t_efectivo + $fila['EFECTIVO'];
        $t_cheque = $t_cheque + $fila['CHEQUE'];
        $t_comrec = $t_comrec + $fila['COMREC'];
        $t_compronet = $t_compronet + $fila['COMPRONET'];
        $t_combanco = $t_combanco + $fila['COMBANCO'];

        $adic .= "<tr>";
        $adic .= "<td width='80px'>" . $fila['CTA'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['EMISORA'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['SERVICIO'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['BANCO'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['SUC'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['FECHA_TRANSACCION'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['FECHA_PAGO'] . "</td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['CANT_TRX'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['EFECTIVO'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['CHEQUE'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['IMPORTE'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['COMREC'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['COMPRONET'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['COMBANCO'], 0) . "</span></td>";
        $adic .= "</tr>";
    }
    $adic .= '</tbody>';
    $adic .= "<tr>";
    $adic .= "<th colspan='7'><strong>Totales</strong></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right;'><strong>" . numberFormater($t_cant, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_efectivo, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_cheque, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_importe, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_comrec, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_compronet, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_combanco, 0) . "</strong></span></th>";
    $adic .= "</tr>";




    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . "</table>";
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoRecaudadoras', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku se demoro ' . $totalF4 . ' segundos');


    return $respuesta;
}

function consolidadoEmisoras($log, $idXLog, $id, $fecha_inicio, $fecha_fin, $emisora, $des_em, $codEmpresa) {
    $_SESSION['repName'] = 'Consolidado Emisora ' . date("d-m-Y");
    if ($des_em != '') {
        $_SESSION['repName'] .= $des_em;
    }

    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoEmisoras', $fecha_inicio . ' ' . $fecha_fin . ' ' . $emisora . ' ' . $des_em);
    // Gerenacion de reportes por rango de fecha de cobro y lote, en transaccion (reingenieria.)
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoEmisoras', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
///elimine todo lo que sea externo a la BD Reingenieira 
    $query = "
select cta, entidad, servicio, fecha_transaccion, sum(cant) cant_trx,  sum(efectivo) efectivo, sum(cheque) cheque, sum(importe) bruto, 
sum(importe-neto) comision, sum(neto) neto, credito fecha_credito, banco, tasa
from (";
    /*
      SELECT 'pronet' cta,
      e.denominacion entidad,
      s.descrip servicio,
      ban.denominacion as banco,
      to_char(b.fecha_hora_transmision, 'dd-mm-yyyy') fecha_transaccion,
      trunc(b.fecha_credito_presta) credito,
      count(*) cant,
      sum(case when(tipo_pago = 'C') then b.importe else 0 end) as efectivo,
      sum(case when(tipo_pago <> 'C') then b.importe else 0 end) as cheque,
      sum(b.importe) importe,
      sum(b.com_recaudadora*1.1+b.com_pronet*1.1+b.com_bclearing*1.1) combanco,
      sum(b.importe-((b.com_recaudadora*1.1+b.com_pronet*1.1+b.com_bclearing*1.1))) neto,
      t.tasa_cambiaria tasa
      FROM corte.impuacum@dblink_corte B, corte.tasas@dblink_corte T, bancos@dblink_corte ban, impuserv@dblink_corte s, rpresta@dblink_corte e
      WHERE trunc(b.fecha_hora_transmision) = trunc(t.fecha_cambiaria)
      AND t.tipo_moneda = 1
      AND to_date(to_char(b.fecha_hora_transmision, 'DD/MM/YYYY'), 'DD/MM/YYYY') >= to_date('$fecha_inicio', 'DD/MM/YYYY')
      AND to_date(to_char(b.fecha_hora_transmision, 'DD/MM/YYYY'), 'DD/MM/YYYY') <= to_date('$fecha_fin', 'DD/MM/YYYY')
      AND b.id_prestadora in (select cod_emi_ant from admin.emisora where cod_emisora = $emisora)
      AND b.flag_anulacion <> '*'
      AND b.cod_banco = ban.id_banco
      and s.id_prestadora = B.id_prestadora
      and s.codigo_impuesto = B.codigo_impuesto
      and B.id_prestadora = e.id_prestadora
      group by 'pronet', e.denominacion, s.descrip, ban.denominacion, to_char(b.fecha_hora_transmision, 'dd-mm-yyyy'), trunc(b.fecha_credito_presta), t.tasa_cambiaria

      union all
      SELECT 'pronet' cta,
      e.denominacion entidad,
      s.descrip servicio,
      ban.denominacion as banco,
      to_char(b.fecha_proceso, 'dd-mm-yyyy') fecha_transaccion,
      trunc(b.fecha_credito_presta) credito,
      count(*) cant,
      sum(case when(tipo_pago = 'C') then b.importe else 0 end) as efectivo,
      sum(case when(tipo_pago <> 'C') then b.importe else 0 end) as cheque,
      sum(b.importe) importe,
      sum(b.com_recaudadora*1.1+b.com_pronet*1.1+b.com_bclearing*1.1) comision,
      sum(b.importe-((b.com_recaudadora*1.1+b.com_pronet*1.1+b.com_bclearing*1.1))) neto,
      t.tasa_cambiaria tasa
      FROM corte.transhist@dblink_corte B, corte.tasas@dblink_corte T, bancos@dblink_corte ban, impuserv@dblink_corte s, rpresta@dblink_corte e
      WHERE trunc(b.fecha_proceso) = trunc(t.fecha_cambiaria)
      AND t.tipo_moneda = 1
      AND to_date(to_char(b.fecha_proceso, 'DD/MM/YYYY'), 'DD/MM/YYYY') >= to_date('$fecha_inicio', 'DD/MM/YYYY')
      AND to_date(to_char(b.fecha_proceso, 'DD/MM/YYYY'), 'DD/MM/YYYY') <= to_date('$fecha_fin', 'DD/MM/YYYY')
      AND b.id_prestadora in (select cod_emi_ant from admin.emisora where cod_emisora = $emisora)
      AND b.estado = 'C'
      AND b.cod_banco = ban.id_banco
      and s.id_prestadora = B.id_prestadora
      and s.codigo_impuesto = B.codigo_impuesto
      and B.id_prestadora = e.id_prestadora
      group by 'pronet', e.denominacion, s.descrip, ban.denominacion, to_char(b.fecha_proceso, 'dd-mm-yyyy'), trunc(b.fecha_credito_presta), t.tasa_cambiaria

      union all
     */

    $query.=" SELECT t.procesador as cta,
t.emisora as entidad, 
t.desc_servicio as servicio, 
t.banco, 
to_char(t.FECHA_PROCESO, 'dd-mm-yyyy') fecha_transaccion,
trunc(t.fecha_credito_emisor) as credito, 
sum(t.q_trx_cob) as cant, 
sum( case when(t.cod_tipo_pago = 1) then t.importe_cob else 0 end) as efectivo,
sum(case when(t.cod_tipo_pago = 2) then t.importe_cob else 0 end) as cheque,
sum(t.importe_cob) as importe,
sum(t.com_boca_cob+t.com_pronet_cob+t.com_banco_cob) as combanco, 
sum(t.importe_cob - (t.com_boca_cob+t.com_pronet_cob+t.com_banco_cob+t.com_aval_cob)) as neto, 
fc_get_cotiz_fch(t.FECHA_PROCESO, 2, 1) as tasa
from admin.vm_consolidado_3 t
where to_date(to_char(t.FECHA_PROCESO, 'DD/MM/YYYY'), 'DD/MM/YYYY') >= to_date('$fecha_inicio', 'DD/MM/YYYY')
AND to_date(to_char(t.FECHA_PROCESO, 'DD/MM/YYYY'), 'DD/MM/YYYY') <= to_date('$fecha_fin', 'DD/MM/YYYY')
AND t.cod_emisora = $emisora
AND t.cod_empresa = $codEmpresa
GROUP BY t.procesador, t.emisora, t.desc_servicio, t.banco, to_char(t.FECHA_PROCESO, 'dd-mm-yyyy'), trunc(t.fecha_credito_emisor),
fc_get_cotiz_fch(t.FECHA_PROCESO, 2, 1)

)a group by cta, entidad, servicio, fecha_transaccion, credito, banco, tasa
order by 1, 2, 3, 4, 6
";

    /*

      //tmp desabilite
      union all
      SELECT  'stream' cta,
      e.descripcion entidad,
      s.descripcion servicio,
      'CITIBANK N.A.' as banco,
      to_char(z.FEC_PROCESAMIENTO, 'dd-mm-yyyy') fecha_transaccion,
      trunc(z.fec_valor) credito,
      count(*) cant,
      sum(case when(tipo_pago = 'C') then z.monto else 0 end) as efectivo,
      sum(case when(tipo_pago <> 'C') then z.monto else 0 end) as cheque,
      sum(z.MONTO) importe,
      sum(z.COMISION_REC+z.COMISION_STREAM+z.COMISION_BANCO) combanco,
      sum(z.monto_facturador) neto, c.venta tasa
      FROM inf.vw_operaciones@dblink_stream z, inf.cotizaciones@dblink_stream c, inf.servicios@dblink_stream s, facturadores@dblink_stream e
      WHERE  c.fec_cambio = z.fec_procesamiento
      AND c.moneda_orig = 1
      AND c.moneda_dest = 0
      AND z.FEC_PROCESAMIENTO >= '$fecha_inicio'
      AND z.FEC_PROCESAMIENTO <= '$fecha_fin'
      AND z.ID_FACTURADOR in ($emisora)
      And z.TIPO_TRANS  in (13, 14, 15)
      And z.anulado is null
      and z.id_facturador = s.id_facturador
      and z.id_servicio = s.id_servicio
      and z.id_facturador = e.id_facturador
      group by  e.descripcion, s.descripcion, to_char(z.FEC_PROCESAMIENTO, 'dd-mm-yyyy'), trunc(z.fec_valor), c.venta



      SELECT 'pronet' cta,
      e.abreviatura entidad,
      s.des_servicio  servicio,
      ban.des_banco as banco,
      to_char(t.FECHA_PROCESO, 'dd-mm-yyyy') fecha_transaccion,
      trunc(t.FECHA_CREDITO_DISPONIBLE) credito,
      count(*) cant,
      sum( case when(cod_tipo_pago = 1) then t.importe else 0 end) as efectivo,
      sum(case when(cod_tipo_pago = 2) then t.importe else 0 end) as cheque,
      sum(t.importe) importe,
      sum(t.com_rec+t.com_proc+t.com_banco)combanco,
      sum(t.importe-t.total_descuento) neto,
      c.valor_venta tasa
      FROM admin.vw_trans_clearing t, admin.tasa_cambiaria c, admin.banco ban, admin.servicio s, admin.emisora e
      WHERE  trunc(c.fecha) = trunc(t.fecha_proceso)
      AND c.cod_moneda_de = 2
      AND c.cod_moneda_a = 1
      AND t.FECHA_PROCESO >= '$fecha_inicio'
      AND t.FECHA_PROCESO <= '$fecha_fin'
      AND t.cod_emisora = $emisora
      AND t.cod_tipo_transaccion = 3
      AND t.cod_banco_emisor = ban.cod_banco
      and s.cod_servicio = t.cod_servicio
      and s.cod_emisora = t.cod_emisora
      and t.cod_emisora = e.cod_emisora
      GROUP BY 'pronet', e.abreviatura, s.des_servicio, ban.des_banco, c.valor_venta,
      TO_CHAR(t.FECHA_PROCESO,'dd-mm-yyyy'), trunc(t.FECHA_CREDITO_DISPONIBLE)

     */

    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoRecaudadoras', 'Query: ' . $query);
    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoEmisoras', $user . " " . $password . " " . $host . " " . $db);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoEmisoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/consolidadoEmisoras', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>Reporte de Comisiones Consolidado Emisora ' . $des_em . '.</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/pdf.php" target="_blank"><img width="40px" height="41px" src="img/pdf.jpg" border="0"></a>				
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>Cuenta</th>
							<th>Emisora</th>							
							<th>Servicio</th>
							<th>Banco</th>
							<th>Tasa</th>							
							<th>Fecha Trx</th>
							<th>Fecha Credito</th>							
							<th>Cant Trxs</th>
							<th>Efectivo</th>							
							<th>Cheque</th>														
							<th>Bruto</th>							
							<th>Comision</th>
							<th>Neto</th>
						</tr>
					</thead>
					';

    $imp = "
						<tr>
							<td align='center'><strong>Cuenta</strong></td>
							<td align='center'><strong>Emisora</strong></td>							
							<td align='center'><strong>Servicio</strong></td>
							<td align='center'><strong>Banco</strong></td>
							<td align='center'><strong>Tasa</strong></td>							
							<td align='center'><strong>Fecha Trx</strong></td>
							<td align='center'><strong>Fecha Credito</strong></td>						
							<td align='center'><strong>Cant Trxs</strong></td>
							<td align='center'><strong>Efectivo</strong></td>							
							<td align='center'><strong>Cheque</strong></td>														
							<td align='center'><strong>Bruto</strong></td>							
							<td align='center'><strong>Comision</strong></td>
							<td align='center'><strong>Neto</strong></td>
						</tr>	
	";
    $adic = '<tbody>';
    $t_cant = 0;
    $t_bruto = 0;
    $t_comision = 0;
    $t_neto = 0;
    $t_efectivo = 0;
    $t_cheque = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $t_cant = $t_cant + $fila['CANT_TRX'];
        $t_bruto = $t_bruto + $fila['BRUTO'];
        $t_comision = $t_comision + $fila['COMISION'];
        $t_neto = $t_neto + $fila['NETO'];
        $t_efectivo = $t_efectivo + $fila['EFECTIVO'];
        $t_cheque = $t_cheque + $fila['CHEQUE'];

        $adic .= "<tr>";
        $adic .= "<td width='80px'>" . $fila['CTA'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['ENTIDAD'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['SERVICIO'] . "</td>";
        $adic .= "<td width='80px'>" . $fila['BANCO'] . "</td>";
        $adic .= "<td width='80px'>" . numberFormater($fila['TASA'], 0) . "</td>";
        $adic .= "<td width='80px'>" . $fila['FECHA_TRANSACCION'] . "</td>";
        $adic .= "<td width='100px'>" . $fila['FECHA_CREDITO'] . "</td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['CANT_TRX'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['EFECTIVO'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['CHEQUE'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['BRUTO'], 0) . "</span></td>";
        $adic .= "<td width='80px' align='right'><span style='float:right'>" . numberFormater($fila['COMISION'], 0) . "</span></td>";
        $adic .= "<td width='100px' align='right'><span style='float:right'>" . numberFormater($fila['NETO'], 0) . "</span></td>";
        $adic .= "</tr>";
    }

    $adic .= "</tbody>";
    $adic .= "<tr>";
    $adic .= "<th colspan='7'><strong>Totales</strong></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right;'><strong>" . numberFormater($t_cant, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_efectivo, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_cheque, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_bruto, 0) . "</strong></span></th>";
    $adic .= "<th width='80px' align='right'><span style='float:right;'><strong>" . numberFormater($t_comision, 0) . "</strong></span></th>";
    $adic .= "<th width='100px' align='right'><span style='float:right;'><strong>" . numberFormater($t_neto, 0) . "</strong></span></th>";
    $adic .= "</tr>";



    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . "</table>";
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoEmisoras', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku se demoro ' . $totalF4 . ' segundos');


    return $respuesta;
}

function queryLibre($log, $idXLog, $id, $query) {


    /* Parseo la cadena enviada pa ver que no traiga sentencias insert, update, delete */
    $query = trim(strtoupper($query));
    if (ereg("INSERT ", $query)) {
        $est = 2;
        $msg = " ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    if (ereg("UPDATE ", $query)) {

        $est = 2;
        $msg = " ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    if (ereg("DELETE ", $query)) {

        $est = 2;
        $msg = "ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    if (ereg("CREATE ", $query)) {
        $est = 2;
        $msg = " ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    if (ereg("ALTER ", $query)) {

        $est = 2;
        $msg = " ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    if (ereg("DROP ", $query)) {

        $est = 2;
        $msg = " ERROR LA SENTENCIA QUE ESTA INTENTANDO EJECUTAR NO ES PERMITIDA..!!! se informa al SAC de la mala utilizacion de su usuario";
        $msg .= "<br /> Su usuario sera bloqueado, debera contactar al sac para volver a utilizar la aplicacion";
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />EL QUERY QUE INTENTO EJECUTAR ES<br />" . $query . "</span><br />";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        session_destroy();
        return $respuesta;
    }
    $query = str_replace(';', '', $query);
    $msg = "QUERY EJECUTADO: <br />" . $query . "<br />";
    /* Parseo la cadena enviada pa ver que no traiga sentencias insert, update, delete */

    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/queryLibre', 'ERROR: ' . $msg . '<br /> Query: ' . $query);
        $est = 0;
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />QUERY<br />" . $query . "</span>";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);




    $log->write($idXLog, 0, 'commonFunctions.php/queryLibre', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/queryLibre', 'ERROR: ' . $msg . "<br /> Query Ejecutado fue: <br />" . $query . "</span>");
        $est = 0;
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />QUERY<br />" . $query . "</span>";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/queryLibre', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $msg = "<br /><span style='color:#F00'>" . $msg . "<br />QUERY<br />" . $query . "</span>";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    $ncols = oci_num_fields($id_sentencia);

    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>QUERY LIBRE</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/pdf.php" target="_blank"><img width="40px" height="41px" src="img/pdf.jpg" border="0"></a>				
				<table cellspacing="1" align="center" class="tablesorter">
					<thead><tr>';


    $imp .= "<tr>";
    for ($i = 1; $i <= $ncols; $i++) {
        $column_name = oci_field_name($id_sentencia, $i);
        $html .= "<th>$column_name</th>";
        $imp .= "<td>$column_name</td>";
    }
    $html .= "</tr></thead><tbody>";
    $imp .= "</tr>";


    while ($fila = oci_fetch_array($id_sentencia, OCI_BOTH)) {
        $adic .= "<tr>";
        for ($i = 0; $i < $ncols; $i++) {
            if (is_numeric($fila[$i])) {
                $adic .= "<td align='right'><span style='float:right;'>" . numberFormater($fila[$i], 0) . "</span></td>";
            } else {
                $adic .= "<td align='left'>" . str_replace('|', '', $fila[$i]) . "</td>";
            }
        }
        $adic .= "</tr>";
    }





    $_SESSION['datosImprimir'] = $imp . $adic;
    $adic = $html . $adic . "</tbody></table></div>";
    $_SESSION['repName'] = '';
//	$adic = $html.$adic."</tbody></table>";
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/consolidadoEmisoras', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku se demoro ' . $totalF4 . ' segundos');


    return $respuesta;

    $est = 1;
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function reporteAnuladas($log, $idXLog, $id, $fechaPago) {
    // Gerenacion de reportes por rango de fecha de cobro y lote, en transaccion (reingenieria.)
    include("db.inc.php");

    $_SESSION['repName'] = 'ReporteAnuladas_' . date("d-m-Y");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $total_1 = 0;
    $total_2 = 0;
    $total_3 = 0;
    $total_4 = 0;

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteAnuladas', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select des_banco,
       nvl(dir_sucursal, 's/datos') dir_sucursal,
       id_terminal,
       nro_lote,
       nro_transaccion,
       fecha_pago,
       fecha_credito_presta,
       des_emisor,
       des_servicio,
       obi,
       monto_efe,
       monto_chq,
       com_rec_mas_iva,
       com_pronet_mas_iva
  from ("; /* select b.des_banco as des_banco,
      s.dir_suc_ent as dir_sucursal,
      to_char(v.ID_TERMINAL) as id_terminal,
      to_char(v.NRO_LOTE) as nro_lote,
      to_char(v.NROTRX) as nro_transaccion,
      to_char(v.FECPAGO, 'dd/mm/yyyy') as fecha_pago,
      to_char(v.fecha_credito_disponible, 'dd/mm/yyyy') as fecha_credito_presta,
      en.des_entidad as des_emisor,
      se.des_servicio as des_servicio,
      v.REFERENCIA as obi,
      lpad(decode(v.cod_tipo_pago, 1, v.importe), 12, '0') as monto_efe,
      lpad(decode(v.cod_tipo_pago, 2, v.importe), 12, '0') as monto_chq,
      lpad(v.com_rec, 12, '0') as com_rec_mas_iva,
      lpad(v.com_proc, 12, '0') as com_pronet_mas_iva
      from vw_trans_anuladas@dblink_dbpronet    v,
      banco@dblink_dbpronet                b,
      entidad@dblink_dbpronet              en,
      entidad@dblink_dbpronet              r,
      recaudadora@dblink_dbpronet          re,
      sucursal_recaudadora@dblink_dbpronet s,
      servicio@dblink_dbpronet             se
      where re.cod_recaudadora = ".$_SESSION["codEntidad"]."
      and r.cod_entidad = re.cod_recaudadora
      and v.COD_BANCO_CLEARING = b.cod_banco
      and v.COD_RECAUDADORA = r.cod_entidad
      and v.COD_RECAUDADORA = s.cod_recaudadora
      and v.COD_SUCURSAL_REC = s.cod_sucursal_rec
      and v.COD_EMISORA = en.cod_entidad
      and v.COD_EMISORA = se.cod_emisora
      and v.COD_SERVICIO = se.cod_servicio
      and v.fecha_proceso >= to_date('".$fechaPago."', 'dd/mm/yyyy')
      and v.fecha_proceso < to_date('".$fechaPago."', 'dd/mm/yyyy') + 1
      and v.cod_tipo_transaccion = 3
      Union all */
    $query .= "SELECT b.denominacion as des_banco,
               s.dir_sucursal,
               i.id_terminal,
               to_char(nro_lote) nro_lote,
               to_char(nro_transaccion) nro_transaccion,
               to_char(i.fecha_pago, 'dd/mm/yyyy') fecha_pago,
               to_char(i.fecha_credito_presta, 'dd/mm/yyyy') fecha_credito_presta,
               p.denominacion as des_emisor,
               m.descrip as des_servicio,
               i.obi,
               lpad(decode(g.cod_grupo_tipo_pago, '00', nvl(importe, 0), 0),
                    12,
                    '0') monto_efe,
               lpad(decode(g.cod_grupo_tipo_pago, '00', 0, nvl(importe, 0)),
                    12,
                    '0') monto_chq,
               lpad(i.com_recaudadora + i.iva_recaudadora, 12, '0') com_rec_mas_iva,
               lpad(i.com_pronet + i.iva_pronet, 12, '0') com_pronet_mas_iva
          FROM (SELECT cod_banco,
                       es_cobro_propio,
                       fecha_credito_presta,
                       obi,
                       no_trans nro_transaccion,
                       no_batch nro_lote,
                       id_terminal,
                       importe,
                       id_prestadora,
                       id_recaudadora,
                       codigo_impuesto,
                       cod_sucursal,
                       tipo_pago,
                       fecha_trans as fecha_pago,
                       com_recaudadora,
                       iva_recaudadora,
                       com_bclearing,
                       iva_bclearing,
                       com_pronet,
                       iva_pronet
                  FROM corte.transhist
                 WHERE fecha_proceso = to_date('" . $fechaPago . "', 'dd/mm/yyyy')
                  AND id_recaudadora = " . $_SESSION["codRecAnt"] . "
                   AND fecha_credito_presta is not null
                   AND ((importe > 0) OR
                       (com_recaudadora + iva_recaudadora) > 0)
                   AND estado = 'A'
                   AND confirmado = 'C'
                UNION
                SELECT cod_banco,
                       es_cobro_propio,
                       fecha_credito_presta,
                       obi,
                       nro_transaccion,
                       nro_batch as nro_lote,
                       id_terminal,
                       importe,
                       id_prestadora,
                       id_recaudadora,
                       codigo_impuesto,
                       cod_sucursal,
                       tipo_pago,
                       fecha_pago,
                       com_recaudadora,
                       iva_recaudadora,
                       com_bclearing,
                       iva_bclearing,
                       com_pronet,
                       iva_pronet
                  FROM corte.impuacum
                 WHERE fecha_hora_transmision =
                       to_date('" . $fechaPago . "', 'dd/mm/yyyy')
                   AND id_recaudadora = " . $_SESSION["codRecAnt"] . "
                   AND fecha_credito_presta is not null
                   AND ((importe > 0) OR
                       (com_recaudadora + iva_recaudadora) > 0)
                   AND flag_anulacion = '*') i,
               corte.bancos b,
               corte.sucursales s,
               corte.tipo_pago t,
               corte.rpresta p,
               corte.impuserv m,
               corte.grupos_tipo_pago g
         WHERE i.tipo_pago = t.tipo_pago
           AND t.cod_grupo_tipo_pago = g.cod_grupo_tipo_pago
           AND i.cod_banco = b.id_banco
           AND i.id_recaudadora = s.id_recaudadora
           AND i.cod_sucursal = s.cod_sucursal
           AND i.id_prestadora = p.id_prestadora
           AND i.codigo_impuesto = m.codigo_impuesto
           AND i.es_cobro_propio = 'N')
 group by des_banco,
          nvl(dir_sucursal, 's/datos'),
          id_terminal,
          nro_lote,
          nro_transaccion,
          fecha_pago,
          fecha_credito_presta,
          des_emisor, 
          des_servicio,
          obi,
          monto_efe,
          monto_chq,
          com_rec_mas_iva,
          com_pronet_mas_iva
 order by des_banco, dir_sucursal, id_terminal, nro_lote, nro_transaccion";


    $log->write($idXLog, 0, 'commonFunctions.php/reporteAnuladas', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteAnuladas', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteResumenTransacciones', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic .= '<tr>';
        $total_1 += $fila['MONTO_EFE'];
        $total_2 += $fila['MONTO_CHQ'];
        $total_3 += $fila['COM_REC_MAS_IVA'];
        $adic .= '<td class="mostrar">' . $fila['DES_BANCO'] . '</td>';
        $adic .= '<td class="mostrar">' . $fila['FECHA_PAGO'] . '</td>';
        $adic .= '<td class="mostrar">' . $fila['FECHA_CREDITO_PRESTA'] . '</td>';
        $adic .= '<td class="mostrar">' . $fila['COD_SUCURSAL'] . '</td>';
        $adic .= '<td class="mostrar">' . $fila['DIR_SUCURSAL'] . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($fila['CANTIDAD'], 0) . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($fila['MONTO_EFE'], 0) . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($fila['MONTO_CHQ'], 0) . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($fila['COM_REC_MAS_IVA'], 0) . '</td>';
        $adic .= '</tr>';
        $est = '1';
        $msg = 'OK';
    }

    $adic_ini = '<div id="rep" align="center">
				<ul><li><b>Reporte res&uacute;men de transacciones del ' . $fechaPago . '</b></li></ul>
				<ul><li><b>Recaudadora: ' . $_SESSION["desEntidad"] . '</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
					<table cellspacing="1" align="center" border="1">
						<thead>
							<tr>
								<th class="new_th">Banco</th>
								<th class="new_th">Fecha Pago</th>
								<th class="new_th">Fecha Valor</th>
								<th class="new_th">Sucursal</th>
								<th class="new_th">Descripcion</th>
								<th class="new_th">Q trx.</th>
								<th class="new_th">Efectivo</th>
								<th class="new_th">Cheque</th>
								<th class="new_th">Comision(Iva incluido)</th>
							</tr>
						</thead>
						<tbody>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '</tr>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"><b>TOTALES: </b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_1, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_2, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_3, 0) . '</b></td>';
    $adic .= '</tr>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteAnuladas', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function reporteTransaccionesDGR($log, $idXLog, $id, $fechaPago) {
    // Gerenacion de reportes por rango de fecha de cobro y lote, en transaccion (reingenieria.)
    include("db.inc.php");

    $_SESSION['repName'] = 'TransaccionesDGR_' . date("d-m-Y");

    $pg_conn = @pg_connect("host= $host_3 port=5432 password= $password_3 user= $user_3 dbname= $db_3");
    $est = '2';
    $adic = '';
    $total_1 = 0;
    $total_2 = 0;
    $total_3 = 0;

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$pg_conn) {
        $msg = $php_errormsg;
        $log->write($idXLog, 3, 'commonFunctions.php/reporteTransaccionesDGR', 'ERROR en la conexion: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select 'ABN' as des_banco,
       coalesce(m.dir_sucursal_mh, 's/datos') as dir_sucursal,
       c.idterm as id_terminal,
       0 as nro_lote,
       p.nro_orden_pago as nro_transaccion,
       to_char(p.fechapago, 'dd/mm/yyyy') as fecha_pago,
       ' ' as fecha_credito_presta,
       'DGR' as des_emisor,
       'Pagos DGR' as des_servicio,
       p.nro_orden_pago as obi,
       case
         when p.cod_medio_pago = 'E' and not p.es_chq_cl then
          coalesce(p.importe, 0)
         else
          0
       end as monto_efe,
       case
         when p.cod_medio_pago = 'C' or p.es_chq_cl then
          coalesce(p.importe, 0)
         else
          0
       end as monto_chq,
       0 as com_rec_mas_iva,
       0 as com_pronet_mas_iva
  from dba.pagos p, dba.sucursal_mh m, dba.caja_entidad c, dba.entidades e
 where p.cod_sucursal_mh = m.cod_sucursal_mh
   and p.cod_entidad = c.cod_entidad
   and p.cod_suc_ent = c.cod_suc_ent
   and p.nro_caja = c.nro_caja
   and p.cod_entidad = e.cod_entidad
   and p.fecha_operacion_cle =to_date('" . $fechaPago . "', 'dd/mm/yyyy')
   AND e.cod_rec_ant = " . $_SESSION["codRecAnt"] . "
 ORDER BY des_banco,
          coalesce(m.dir_sucursal_mh, 's/datos'),
          c.idterm,
          to_char(p.nro_orden_pago, 'FM99999999999')";


    $log->write($idXLog, 0, 'commonFunctions.php/reporteTransaccionesDGR', 'Query: ' . $query);

    $r = @pg_exec($pg_conn, $query);

    if (!$r) {
        $msg = pg_ErrorMessage($pg_conn);
        $log->write($idXLog, 3, 'commonFunctions.php/reporteTransaccionesDGR', 'ERROR en el execute: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $linea = 0;
    $cant_filas = pg_numrows($r);
    $cont = 1;
    while ($linea < $cant_filas) {
        $registro = @pg_fetch_array($r, $linea);
        $adic .= '<tr>';
        $total_1 += $registro['monto_efe'];
        $total_2 += $registro['monto_chq'];
        $total_3 += $registro['com_rec_mas_iva'];
        $adic .= '<td class="mostrar">' . $cont . '</td>';
        $adic .= '<td class="mostrar">' . $registro['des_banco'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['dir_sucursal'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['id_terminal'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['nro_lote'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['nro_transaccion'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['fecha_pago'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['fecha_credito_presta'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['des_emisor'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['des_servicio'] . '</td>';
        $adic .= '<td class="mostrar">' . $registro['obi'] . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($registro['monto_efe'], 0) . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($registro['monto_chq'], 0) . '</td>';
        $adic .= '<td class="mostrar">' . numberFormater($registro['com_rec_mas_iva'], 0) . '</td>';
        $adic .= '</tr>';
        $est = '1';
        $msg = 'OK';
        $cont++;
        $linea++;
    }



    $adic_ini = '<div id="rep" align="center">
				<ul><li><b>Reporte detallado de transacciones DGR del ' . $fechaPago . '</b></li></ul>
				<ul><li><b>Recaudadora: ' . $_SESSION["desEntidad"] . '</b></li></ul>
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
					<table cellspacing="1" align="center" border="1">
						<thead>
							<tr>
								<th class="new_th">Nro.</th>
								<th class="new_th">Banco</th>
								<th class="new_th">Sucursal</th>
								<th class="new_th">Pos</th>
								<th class="new_th">Lote</th>
								<th class="new_th">Trx.</th>
								<th class="new_th">F.Cobro</th>
								<th class="new_th">F.Valor</th>
								<th class="new_th">Emisor</th>
								<th class="new_th">Servicio</th>
								<th class="new_th">Ref.</th>
								<th class="new_th">Ef.</th>
								<th class="new_th">Chq.</th>
								<th class="new_th">Com.Total</th>
							</tr>
						</thead>
						<tbody>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '</tr>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"><b>TOTALES: </b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_1, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_2, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_3, 0) . '</b></td>';
    $adic .= '</tr>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    pg_close($conexion);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteTransaccionesDGR', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function getMultaXAnulacion($log, $idXLog, $id, $desde, $hasta) {

    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getMultaXAnulacion', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT NVL(SUM(CANT), 0) as CANTIDAD, NVL(SUM(COMISION), 0) as IMPORTE
  FROM (SELECT COUNT(*) CANT, SUM(NVL(T.MULTA_REC, 0)) COMISION
          FROM admin.VW_TRANS_CLEARING T
         WHERE T.FECHA_PROCESO BETWEEN to_date('" . $desde . "', 'dd/mm/yy') AND to_date('" . $hasta . "', 'dd/mm/yy')
           AND T.COD_EMPRESA  = '01'
           AND T.COD_RECAUDADORA = " . $_SESSION["codEntidad"] . "
           AND T.COD_BANCO_EMISOR = 6
           AND T.COD_TIPO_TRANSACCION = 4
           AND T.COD_EMISORA != 24
           AND T.COD_EMISORA != T.COD_RECAUDADORA
           AND NVL(T.MULTA_REC, 0) > 0 HAVING
         SUM(NVL(T.MULTA_REC, 0)) > 0"; /*
      UNION ALL
      SELECT COUNT(*) CANT, SUM(NVL(O.COMISION_STREAM, 0)) COMISION
      FROM INF.OPERACIONES_PRONET@DBLINK_STREAM O,
      INF.GRUPOS_PRONET@DBLINK_STREAM      G
      WHERE O.FEC_PROCESAMIENTO BETWEEN to_date('".$desde."', 'dd/mm/yy') AND to_date('".$hasta."', 'dd/mm/yy')
      AND O.ID_GRUPO = G.ID_GRUPO_ST
      AND NVL(G.ID_GRUPO_NEW, G.ID_GRUPO_ST) = ".$_SESSION["codEntidad"]."
      AND NVL(O.FBD, 'N') != 'E'
      AND NVL(O.ANULADO, 'Z') != 'A'
      AND O.TIPO_TRANS = 4
      AND O.ID_FACTURADOR != 24
      AND (G.ID_GRUPO_ST NOT IN
      (SELECT ID_GRUPO
      FROM GRUPO_FACTURADOR@DBLINK_STREAM
      WHERE ID_FACTURADOR = O.ID_FACTURADOR
      AND ID_GRUPO = O.ID_GRUPO))
      AND NVL(O.COMISION_STREAM, 0) > 0 HAVING
      SUM(NVL(O.COMISION_STREAM, 0)) > 0)"; */

    $log->write($idXLog, 0, 'commonFunctions.php/getMultaXAnulacion', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getMultaXAnulacion', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getMultaXAnulacion', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic = $fila['CANTIDAD'] . ';' . $fila['IMPORTE'];
        $est = '1';
        $msg = 'OK';
    }
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);



    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/getMultaXAnulacion', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function getDebitosCreditos($log, $idXLog, $id, $desde, $hasta) {

    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getDebitosCreditos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT DECODE(AD.TIPO_MOVIMIENTO, 'D', AD.IMPORTE, 0) as debito,
       DECODE(AD.TIPO_MOVIMIENTO, 'D', 0, AD.IMPORTE) as credito
  FROM INF.AJUSTE_DETALLE@DBLINK_STREAM AD,
       INF.AJUSTE@DBLINK_STREAM         A,
       INF.GRUPOS_PRONET@DBLINK_STREAM  G
 WHERE A.FECHA BETWEEN to_date('" . $desde . "', 'dd/mm/yy') AND to_date('" . $hasta . "', 'dd/mm/yy')
   AND AD.IDENTIFICADOR_CUENTA = G.ID_GRUPO_ST
   AND AD.ID_AJUSTE = A.ID_AJUSTE
   AND AD.COD_TIPO_CUENTA = 3
   AND AD.ID_CLASE_CUENTA = 1
   AND NVL(G.ID_GRUPO_NEW, G.ID_GRUPO_ST) = " . $_SESSION["codEntidad"] . "
   AND A.ID_ENTIDAD_CAB = 4";

    $log->write($idXLog, 0, 'commonFunctions.php/getDebitosCreditos', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getDebitosCreditos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/getDebitosCreditos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $debito += $fila['DEBITO'];
        $credito += $fila['CREDITO'];
        $est = '1';
        $msg = 'OK';
    }

    $debito = $debito * -1;

    $adic = $debito . ';' . $credito;

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);



    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/getDebitosCreditos', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function comboRecaudadorasSubRED($log, $idXLog, $id) {
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select b.cod_recaudadora, e.des_entidad 
from admin.banco_recaudadora b, admin.entidad e
where b.cod_banco = 98
and b.prioridad = 1 
and b.cod_recaudadora = e.cod_entidad
group by b.cod_recaudadora, e.des_entidad
order by 2";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasSubRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >..:: Recaudador ::..</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }



    oci_close($ora_conn);
    echo $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    /*  $vars = explode("|", $resulGetNoficaciones);
      $id = $vars[0];
      $est = $vars[1];
      $msg = $vars[2];
      $adic = $vars[3];
      unset($vars);
     */


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/getDebitosCreditos', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

//////nuevo
function reporteDepositoCorresponsaliaFechaCobro($log, $idXLog, $fechaCobro, $codRecaudador, $id, $dias_juliano, $tipoRep, $codEmpresa) {
    include("db.inc.php");
    // FECHA CIERRE
    //11-08-2011 JO --> se modifica el criterio de  fecha,  tomando apartir de  ahora la fecha_apertura.
    $tipoFecha = '';
    $contador = 0;
    $cabecera = '';
    if ($codRecaudador == '*') {
        $codRecaudador = $_SESSION['codEntidad'];
    }
    if ($tipoRep == 'A') {
        $tipoFecha = ' a.fecha_ape ';
    } else if ($tipoRep == 'C') {
        $tipoFecha = ' a.fecha_cie ';
    }


    $_SESSION['repName'] = 'DepositoCobranzasCorresponsaliadel' . $fechaCobro;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT COD_SUC,
				   SUCURSAL,
				   SUM(DECODE(MONEDA, 1, DECODE(TP, 1, DEBITO, 0), 0)) GS_EFECTIVO_D,
				   SUM(DECODE(MONEDA, 1, DECODE(TP, 1, CREDITO, 0), 0)) GS_EFECTIVO_C,
				   SUM(DECODE(MONEDA, 1, DECODE(TP, 2, DEBITO, 0), 0)) GS_CHEQUE_D,
				   SUM(DECODE(MONEDA, 1, DECODE(TP, 2, CREDITO, 0), 0)) GS_CHEQUE_C,    
				   SUM(DECODE(MONEDA, 2, DECODE(TP, 1, DEBITO, 0), 0)) USD_EFECTIVO_D,
				   SUM(DECODE(MONEDA, 2, DECODE(TP, 1, CREDITO, 0), 0)) USD_EFECTIVO_C,
				   SUM(DECODE(MONEDA, 2, DECODE(TP, 2, DEBITO, 0), 0)) USD_CHEQUE_D,
				   SUM(DECODE(MONEDA, 2, DECODE(TP, 2, CREDITO, 0), 0)) USD_CHEQUE_C
			
			  FROM (SELECT S.COD_SUCURSAL_REC COD_SUC,
						   S.DES_SUCURSAL_REC SUCURSAL,
						   T.COD_TIPO_PAGO    TP,
						   T.COD_MONEDA       MONEDA,
						   SUM(DECODE(SE.TIPO_MOV, 'C', T.IMPORTE, 0)) DEBITO,
						   SUM(DECODE(SE.TIPO_MOV, 'D', T.IMPORTE, 0)) CREDITO
					
					  FROM ADMIN.APERTURA_CIERRE      A,
						   ADMIN.TRANSACCION          T,
						   ADMIN.SUCURSAL_RECAUDADORA S,
						   ADMIN.TERMINAL             TE,
						   ADMIN.SERVICIO             SE
					 WHERE A.COD_EMPRESA = T.COD_EMPRESA
					   AND A.COD_RECAUDADORA = T.COD_RECAUDADORA
					   AND A.COD_SUCURSAL_REC = T.COD_SUCURSAL_REC
					   AND A.NRO_CAJA = T.NRO_CAJA
					   AND A.NRO_LOTE = T.NRO_LOTE
					   AND A.COD_RECAUDADORA = S.COD_RECAUDADORA
					   AND A.COD_SUCURSAL_REC = S.COD_SUCURSAL_REC
					   AND T.ID_TERMINAL = TE.ID_TERMINAL
					   AND T.COD_EMPRESA = SE.COD_EMPRESA
					   AND T.COD_EMISORA = SE.COD_EMISORA
					   AND T.COD_SERVICIO = SE.COD_SERVICIO
					   AND T.COD_EMISORA <> T.COD_RECAUDADORA --EXCLUIR COBROS PROPIOS
					   AND NOT (TE.COD_TIPO_POS = 1 AND TE.ID_MODELO_POS = 6) -- EXCLUYE BPOS
					   AND A.COD_EMPRESA = '" . $codEmpresa . "'
					   AND A.COD_RECAUDADORA = '" . $codRecaudador . "'
					   AND A.FECHA_APE >= TO_DATE('" . $fechaCobro . "', 'DD/MM/YYYY')
					   AND A.FECHA_APE < TO_DATE('" . $fechaCobro . "', 'DD/MM/YYYY') + 1
					   AND T.ANULADO = 'N'
					   AND T.COD_TIPO_TRANSACCION = 3
					   AND (T.ENLINEA = 'N' OR (T.ENLINEA = 'S' AND COD_ESTADO = 'A'))
					
					 GROUP BY S.COD_SUCURSAL_REC,
							  S.DES_SUCURSAL_REC,
							  T.COD_TIPO_PAGO,
							  T.COD_MONEDA
					UNION ALL
					SELECT S.COD_SUCURSAL_REC COD_SUC,
						   S.DES_SUCURSAL_REC SUCURSAL,
						   T.COD_TIPO_PAGO    TP,
						   T.COD_MONEDA       MONEDA,   
						   SUM(DECODE(SE.TIPO_MOV, 'C', T.IMPORTE, 0)) DEBITO,
						   SUM(DECODE(SE.TIPO_MOV, 'D', T.IMPORTE, 0)) CREDITO
					  FROM ADMIN.APERTURA_CIERRE      A,
						   ADMIN.TRANSACCION          T,
						   ADMIN.SUCURSAL_RECAUDADORA S,
						   ADMIN.TERMINAL             TE,
						   ADMIN.SERVICIO             SE
					 WHERE A.COD_EMPRESA = T.COD_EMPRESA
					   AND A.COD_RECAUDADORA = T.COD_RECAUDADORA
					   AND A.COD_SUCURSAL_REC = T.COD_SUCURSAL_REC
					   AND A.NRO_CAJA = T.NRO_CAJA
					   AND A.NRO_LOTE = T.NRO_LOTE
					   AND A.COD_RECAUDADORA = S.COD_RECAUDADORA
					   AND A.COD_SUCURSAL_REC = S.COD_SUCURSAL_REC
					   AND T.ID_TERMINAL = TE.ID_TERMINAL
					   AND T.COD_EMPRESA = SE.COD_EMPRESA
					   AND T.COD_EMISORA = SE.COD_EMISORA
					   AND T.COD_SERVICIO = SE.COD_SERVICIO
					   AND T.COD_EMISORA <> T.COD_RECAUDADORA --EXCLUIR COBROS PROPIOS
					   AND (TE.COD_TIPO_POS = 1 AND TE.ID_MODELO_POS = 6) -- SOLO BPOS
					   AND A.COD_EMPRESA = '" . $codEmpresa . "'
					   AND A.COD_RECAUDADORA = '" . $codRecaudador . "'
					   AND T.FECPAGO >= TO_DATE('" . $fechaCobro . "', 'DD/MM/YYYY')
					   AND T.FECPAGO < TO_DATE('" . $fechaCobro . "', 'DD/MM/YYYY') + 1
					   AND T.ANULADO = 'N'
					   AND T.COD_TIPO_TRANSACCION = 3
					   AND (T.ENLINEA = 'N' OR (T.ENLINEA = 'S' AND COD_ESTADO = 'A'))
					 GROUP BY S.COD_SUCURSAL_REC,
							  S.DES_SUCURSAL_REC,
							  T.COD_TIPO_PAGO,
							  T.COD_MONEDA)
			 GROUP BY COD_SUC, SUCURSAL
			 ORDER BY 1, 2";
    $log->write($idXLog, 0, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    $miTotGsEfeDeb = 0;
    $miTotGsEfeCre = 0;

    $miTotGsCheDeb = 0;
    $miTotGsCheCre = 0;

    $miTotUsEfeDeb = 0;
    $miTotUsEfeCre = 0;

    $miTotUsCheDeb = 0;
    $miTotUsCheCre = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contador++;
        $est = 1;
        $msg = 'OK';

        $miTotGsEfeDeb = $miTotGsEfeDeb + $fila[2];
        $miTotGsEfeCre = $miTotGsEfeCre + $fila[3];

        $miTotGsCheDeb = $miTotGsCheDeb + $fila[4];
        $miTotGsCheCre = $miTotGsCheCre + $fila[5];

        $miTotUsEfeDeb = $miTotUsEfeDeb + $fila[6];
        $miTotUsEfeCre = $miTotUsEfeCre + $fila[7];

        $miTotUsCheDeb = $miTotUsCheDeb + $fila[8];
        $miTotUsCheCre = $miTotUsCheCre + $fila[9];


        $chqUs = 0;
        $efeUs = 0;
        $efeGs = 0;
        $chqGs = 0;

        $adic .=' <tr>
					<td align="left">' . $fila['SUCURSAL'] . '</td>
					<td align="rigth">' . numberFormater($fila[2], 0) . '</td>
					<td align="center">' . numberFormater($fila[3], 0) . '</td>
					<td align="center">' . numberFormater($fila[4], 0) . '</td>
					<td align="center">' . numberFormater($fila[5], 0) . '</td>
					<td align="center">' . numberFormater($fila[6], 0) . '</td>
					<td align="center">' . numberFormater($fila[7], 0) . '</td>
					<td align="center">' . numberFormater($fila[8], 0) . '</td>
					<td align="center">' . numberFormater($fila[9], 0) . '</td>
				  </tr>';
    }
    if ($contador > 0) {
        $nroCuenta = calculoNroCuentaSubRed($codRecaudador);
        $cabecera.='</br><br>Numero de Cuenta:<b>' . $nroCuenta . '</b></br></br>';
        $cabecera.='<table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablesorter" style="border:1px #000 solid; border-collapse:collapse">
					 <thead>
					  <tr>
						<th align="center">&nbsp;</th>
						<th colspan="2" align="center">Guaranies Efectivo</th>
						<th colspan="2" align="center"> Guaranies Cheque</th>
						<th colspan="2" align="center">Dolares Efectivo</th>
						<th colspan="2" align="center">Dolares Cheque</th>
					  </tr>
					  <tr>
						<th width="10%" align="center">Sucursal</th>
						<th width="10%" align="center">Debito</th>
						<th width="10%" align="center">Credito</th>
						<th width="10%" align="center">Debito</th>
						<th width="10%" align="center">Credito</th>
						<th width="10%" align="center">Debito</th>
						<th width="10%" align="center">Credito</th>
						<th width="10%" align="center">Debito</th>
						<th width="10%" align="center">Credito</th>
					  </tr>
					 
					 </thead><tbody>';
        $adic.='<tr style=" text-align: left; font-weight: bold; background-color: #E6E6E6 ">
				  <td align="center">Total</td>
				  <td align="center">' . numberFormater($miTotGsEfeDeb, 0) . '</td>
				  <td align="center">' . numberFormater($miTotGsEfeCre, 0) . '</td>
				  <td align="center">' . numberFormater($miTotGsCheDeb, 0) . '</td>
				  <td align="center">' . numberFormater($miTotGsCheCre, 0) . '</td>
				  <td align="center">' . numberFormater($miTotUsEfeDeb, 0) . '</td>
				  <td align="center">' . numberFormater($miTotUsEfeCre, 0) . '</td>
				  <td align="center">' . numberFormater($miTotUsCheDeb, 0) . '</td>
				  <td align="center">' . numberFormater($miTotUsCheCre, 0) . '</td>
			    </tr>';


        $adic .='</tbody>
                 </table>';

        $imprimir = $cabecera . $adic;

        // $msg=$imprimir;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $imprimir . '|';
        $log->write($idXLog, 0, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
        return $respuesta;
    } else {

        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        $log->write($idXLog, 0, 'commonFunctions.php/reporteDepositoCorresponsaliaFechaCobro', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
        return $respuesta;
    }
}

function calculoNroCuentaSubRed($codRecaudador) {
    // include("db.inc.php");
    include("./db.inc.php");
    global $log, $idXLog, $ora_conn;
    $cuenta = '';
    $ubicacion = __FILE__ . '/' . __FUNCTION__ . '/' . __LINE__;
    $log->write($idXLog, 0, $ubicacion, "entrando en a funcioncion : $ubicacion");


    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, $ubicacion, 'ERROR: ' . $msg);
        $est = 0;
        $msg = 'Error en la ejecucion';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta . $query;
    }

    $query = "select admin.get_cta_sub_red($codRecaudador, '$codEmpresa') from dual";

    $log->write($idXLog, 0, $ubicacion, $query);
    $id_sentencia3 = @oci_parse($ora_conn, $query);

    if (!$id_sentencia3) {
        $e = @oci_error($ora_conn);
        $msj = 'Error al parsear ' . $e['message'];
        $log->write($idXLog, 3, $ubicacion, 'ERROR: ' . $e['message']);
        $est = 0;
        $respuesta = $est . "|ERROR COMMIT [" . $msj . "]";
        return $respuesta;
    }

    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, $ubicacion . $secuencia, 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $msg = 'Error en la ejecucion';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, $ubicacion . $secuencia, 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $msg = 'Error en la ejecucion';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $cuenta = $fila[0];
    }

    return $cuenta;
}

///////fin-nuevo


function comboTipoDoc($log, $idXLog, $id) {
    include("db.inc.php");


    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboTipoDoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select td.cod_tp_documento, td.des_tipo_documento from admin.tipo_documento td";

    $log->write($idXLog, 0, 'commonFunctions.php/comboTipoDoc', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboTipoDoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboTipoDoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function repTransXdoc($log, $idXLog, $id, $nroDoc, $tipoDoc) {
    include("db.inc.php");


    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;
    $detalle = "";
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/repTransXdoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select p.nro_documento,
       upper(p.nombres),
       upper(p.apellidos),
       p.direccion_part,
       p.telefono_fijo,
       p.telefono_movil,
       p.fecha_nacimiento,
       e.cod_emisora,
       em.des_entidad||' - '|| s.des_servicio,
       e.cod_servicio,
       mo.siglas,
       sum(e.cantidad),
       sum(e.importe),
       min(e.fecha_pago),
       max(e.fecha_pago),
       e.cod_moneda 
  from admin.emisora_persona e,
       admin.persona p,
       admin.servicio s,
       admin.entidad em ,
       admin.moneda mo,
       admin.cuenta_cliente_tc c
 where s.cod_empresa = e.cod_empresa

       and e.cod_emisora = s.cod_emisora
       and e.cod_servicio = s.cod_servicio
       and e.nro_documento = p.nro_documento
       and e.cod_empresa=em.cod_empresa
       and e.cod_emisora=em.cod_entidad
       and e.cod_tp_documento = p.cod_tp_documento
        and p.cod_persona=c.cod_persona(+)
       and e.cod_moneda=mo.cod_moneda
       and e.nro_documento='" . $nroDoc . "'
       and e.cod_tp_documento=" . $tipoDoc . "
           group by p.nro_documento,
       upper(p.nombres),
       upper(p.apellidos),
       p.direccion_part,
       p.telefono_fijo,
       p.telefono_movil,
       p.fecha_nacimiento,
       e.cod_emisora,
       e.cod_servicio,
       em.des_entidad||' - '|| s.des_servicio,
       mo.siglas,
       e.cod_moneda,
       c.nro_cuenta";

    $log->write($idXLog, 0, 'commonFunctions.php/repTransXdoc', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboTipoDoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboTipoDoc', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $cabecera = '';
    $contador1 = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contador1++;
        if ($fila[16] == '') {
            $cliente = 'No posee cuenta  EfiCash';
        } else {
            $cliente = 'Es Cliente  EfiCash';
        }
        $nombre = $fila[1] . $fila[2];
        $cabecera = "<table><tbody>
                        <tr>
                            <td colspan='2'>Datos del Usuario</td>
                        </tr>
                            <tr><td><b>Nombre:</b></td><td>" . $fila[1] . "</td></tr>
                            <tr><td><b>Apellido:</b></td><td>" . $fila[2] . "</td></tr>
                            <tr><td><b>Diereccion:</b></td><td>" . $fila[3] . "</td></tr>
                            <tr><td><b>Linea Baja:</b></td><td>" . $fila[4] . "</td></tr>
                            <tr><td><b>Celular:</b></td><td>" . $fila[5] . "</td></tr>
                            <tr><td><b>Fecha Nac.:</b></td><td>" . $fila[6] . "</td></tr>
                            <tr><td><b>Cliente Eficash.:</b></td><td><b>" . $cliente . "</b></td>    </tr>";
        $detalle.="<tr><td>" . $fila[8] . '</td>';
        $detalle.="<td>" . $fila[9] . '</td>';
        $detalle.="<td>" . $fila[10] . '</td>';
        $detalle.="<td>" . numberFormater($fila[11], 0) . '</td>';
        $detalle.="<td>" . numberFormater($fila[12], 0) . '</td>';
        $detalle.="<td>" . $fila[13] . '</td>';
        $detalle.="<td>" . $fila[14] . '</td></tr>';






        $est = '1';
        $msg = 'OK';
    }
    if ($contador1 == 0) {
        $est = '1';
        $msg = 'OK';
        $adic = '<font color="red"><b>Usuario no Existe</b></font> Verifique que haya ingresado un Tipo o Documento Valido!!!';
        $log->write($idXLog, 1, 'commonFunctions.php/repTransXdoc', 'GeneraCabeceraUsuario: ' . $msg);
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $cabecera.='</tbody></table>';
    }

    $exportar = '<a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" title="Exportar  Resultado a formato PDF"  width="50" height="50" alt="PDF" border="0"></a>';
    $detalle = '<table cellspacing="1" border=1 align="center" style="font-size:12px;border-collapse: collapse">
					<thead><tr><td colspan="7">
                                                        <h2>Servicios Pagados en la RED</h2>
                                                    </td>
                                                </tr>
						<tr>
							<th>Entidad-Servicio&nbsp;</th>
							<th>Cod. Servicio</th>
							<th>Moneda&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>Cantidad&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>Importe&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>1ra .Fec.  Pago&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>Ult. Fec. Pago&nbsp;&nbsp;&nbsp;&nbsp;</th>
						</tr>
					</thead>
					<tbody>' . $detalle . '
                                        <tr> <td colspan="7">
                                                        <h2>Antedecentes</h2>
                                             </td>
                                        </tr>        <tr>
							<th colspan="5">Causa&nbsp;</th>
							<th>Fecha</th>
							<th>Estado del Proceso&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>

						</tr>
                                                <tr>
							<td colspan="5">No registra Antecedentes</td>
							<td>-</td>
							<td>-</td>

						</tr>
                                         </tbody></table>';
    $contador = 0;
    $_SESSION['repName'] = 'Reporte' . $nombre;
    $_SESSION['datosImprimir'] = $cabecera . $detalle;
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $exportar . $cabecera . $detalle;
    return $respuesta;
}

function comboRecaudadorasRED($log, $idXLog) {
    include("db.inc.php");
    $id = 19;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select e.cod_entidad, e.des_entidad, e.nombre_fantasia from admin.entidad e
where (e.tipo_entidad!=13 and e.tipo_entidad!=5) and e.tipo_ent_red in ('R', 'RE') order by e.des_entidad";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >..:: Recaudador ::..</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "  - (" . $fila[2] . ")</option>";
        $est = '1';
        $msg = 'OK';
    }


    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Generado: ' . $msg);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function comboRecaudadoras($log, $idXLog) {
    include("db.inc.php");
    $id = 32;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select e.cod_entidad, e.des_entidad, e.nombre_fantasia from admin.entidad e
where  e.tipo_ent_red in ('R', 'RE') order by e.des_entidad";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >..:: Recaudador ::..</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "  - (" . $fila[2] . ")</option>";
        $est = '1';
        $msg = 'OK';
    }


    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Generado: ' . $msg);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function listaSucursalRecaudadorasRED($log, $idXLog, $codRecaudadora) {
    include("db.inc.php");
    $id = 19;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select s.cod_recaudadora, s.cod_sucursal_rec,s.des_sucursal_rec, s.dir_suc_ent, s.nro_telefono, s.latitud, s.longitud
from admin.sucursal_recaudadora s where s.cod_empresa=01 and s.cod_recaudadora=" . $codRecaudadora . " order by 1 ";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.='<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
                                                        <th>Accion</th>
							<th>Sucursal</th>
							<th>Direccion</th>
							<th>Telefono</th>
                                                        <th>Latitud</th>
                                                        <th>Longitud</th>
                                                       
						</tr>
					</thead>
					<tbody>';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $coodenadas = '"' . $fila[0] . '@' . $fila[1] . '"';
        $adic.="<tr><td><a href='#' onclick='javascript:asignarCoordendas(" . $coodenadas . ");'>";
        $adic.='Asignar</a></td>
                            <td>' . $fila[2] . '</td>
                            <td>' . $fila[3] . '</td>
                            <td>' . $fila[4] . '</td>
                            <td>' . $fila[5] . '</td>
                            <td>' . $fila[6] . '</td>
                       </tr>';

        $est = '1';
        $msg = 'OK';
    }
    $adic.="</tbody></table>";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Generaro: ' . $msg . '- Se omite  html xqIPUKU');
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function ActualizarCoordenadas($log, $idXLog, $codRecaudadora, $codSucursal, $latitud, $longitud) {

    $id = 21;
    $query = "update admin.sucursal_recaudadora s set s.latitud=" . $latitud . " , s.longitud=" . $longitud . "
where s.cod_recaudadora='" . $codRecaudadora . "'
and s.cod_sucursal_rec='" . $codSucursal . "'";
    $msg = '';
    $adic = '';

    $log->write($idXLog, 0, 'commonFunctions.php/ActualizarCoordenadas', 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/ActualizarCoordenadas', 'ERROR1: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/ActualizarCoordenadas', 'ERROR2: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);

    if ($committed) {
        $est = '1';
        $msg = 'OK';
        $adic = "Actualizado correctamente";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }
    oci_close($ora_conn);
}

function comboBancos($log, $idXLog, $id) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select cod_banco, upper(des_banco) from admin.banco b where b.activo='S' order by 2";

    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >..:: Bancos ::..</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }



    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    /* $vars = explode("|", $resulGetNoficaciones);
      $id = $vars[0];
      $est = $vars[1];
      $msg = $vars[2];
      $adic = $vars[3];
      unset($vars);
     */


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function comboMoneda($log, $idXLog, $id) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select m.cod_moneda, upper(m.des_moneda) from admin.moneda m order by 1";

    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    /*    $vars = explode("|", $resulGetNoficaciones);
      $id = $vars[0];
      $est = $vars[1];
      $msg = $vars[2];
      $adic = $vars[3];
      unset($vars);
     */


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function insertarChequeRechazado($id, $log, $idXLog, $idrecaudadora, $idBancoCheque, $importe, $moneda, $fechaOperacion, $idTipoPago, $observacion, $nroCheque, $motivos) {
    include("db.inc.php");
    $codOpe = codOperacionCtrCheqRechazado($log, $idXLog);
    $cod = explode("|", $codOpe);
    if ($cod[1] == 1) {
        $codigo = $cod[2];
    } else {
        $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx', 'ERROR1: ' . $codOpe);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }


    $adic = "";
    $query = "select t.cod_operacion, t.cod_recaudadora
from admin.CTRL_CHEQUES_SUB_RED t where t.nro_cheque= '$nroCheque' and t.cod_banco_cheque='" . $idBancoCheque . "'";

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $existe = 0;
    $recuadadora = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $existe = $fila['COD_OPERACION'];
        $recuadadora = $fila['COD_RECAUDADORA'];
    }

    if ($existe == 0) {



        $query = "insert into admin.ctrl_cheques_sub_red c
				(c.cod_banco,
				c.cod_empresa,
				c.cod_recaudadora,
				c.cod_moneda,
				c.cod_tipo_pago,
				c.conciliado,
				c.tipo_movimiento,
				c.fecha_insercion,
				c.fecha_operacion,
				c.pagos_mh,
				c.cod_operacion,
				c.cod_usuario_alta,
				c.nro_cheque,
				c.cod_banco_cheque,
				c.estado,
				c.observacion, c.importe) values ('98','01',
				'" . $idrecaudadora . "',
				'" . $moneda . "',
				'" . $idTipoPago . "',
				  'N',
				  'D',
				sysdate,
				to_date('" . $fechaOperacion . "','dd/mm/yyyy') ,
				  'N',
				  '$codigo',
				  '" . $_SESSION['codUsuario'] . "',
				  '" . $nroCheque . "',
				  '" . $idBancoCheque . "','P','" . $observacion . "','" . $importe . "')";
        $msg = '';
        $adic = '';
        $id = 24;

        $log->write($idXLog, 0, 'commonFunctions.php/insertarChequeRechazado', 'Query: ' . $query);
        include("db.inc.php");
        $id_sentencia = @oci_parse($ora_conn, $query);

        if (!$id_sentencia) {
            $e = oci_error($ora_conn);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazado', 'ERROR1: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
            return $respuesta;
        }

        $r = @oci_execute($id_sentencia, OCI_DEFAULT);

        if (!$r) {
            $e = oci_error($id_sentencia);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazado', 'ERROR2: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
            return $respuesta;
        }

        $committed = oci_commit($ora_conn);
        ///insertar motivos
     /*   
        $query1 = '';
        $motivoCheque = explode(",", $motivos);
        foreach ($motivoCheque as $motivoCheque) {
            $query1 = "insert into admin.controlcheque_motivo
                              (tid,
                              cod_operacion,
                              tid_motivo)
                            values
                              ((select (nvl((select max(TID) from admin.controlcheque_motivo), 0)+1) from dual),
                              '$codigo',
                              $motivoCheque
                              )";
            $log->write($idXLog, 0, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'Query: ' . $query1);



            include("db.inc.php");
            $id_sentencia1 = @oci_parse($ora_conn, $query1);

            if (!$id_sentencia1) {
                $e = oci_error($ora_conn);
                $msg = $e['message'];
                $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'ERROR1: ' . $msg);
                $est = 0;
                $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query1;
                return $respuesta;
            }

            $r = @oci_execute($id_sentencia1, OCI_DEFAULT);

            if (!$r) {
                $e = oci_error($id_sentencia1);
                $msg = $e['message'];
                $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'ERROR2: ' . $msg);
                $est = 0;
                $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
                return $respuesta;
            }

            $committed1 = oci_commit($ora_conn);

            if ($committed1) {
                
            } else {
                $msg = oci_error($id_sentencia);
                $est = 0;
                $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
                return $respuesta;
            }
            oci_close($ora_conn);
        }

*/
//comited del primet insercion
        if ($committed) {

            $est = '1';
            $msg = 'OK';
            $adic = "Insertado correctamente";
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
            return $respuesta;
        } else {
            $msg = oci_error($id_sentencia);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
            return $respuesta;
        }
        oci_close($ora_conn);
    } else {
      /*  $est = '1';
        $adic = '<h2 style="color:red;">La trasaccion ya fue cargada con Expediente <b>Nro.' . $existe . '</b> </h2>';
        // $adic.="<br/> <input value='Ver Nota' onclick='verNota($existe, $recuadadora)' /> <div id='dvnotas'></div>";
        $adic.="<br/> <input type='button' value='Ver Nota' onclick='verNota($existe, $idrecaudadora)' /> <div id='dvnotas'></div>";
        */
        return $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
       
    }
}

function genlistaAprobarChequesRech($log, $idXLog, $codOperacion) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;
    $id = 25;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select c.cod_operacion,
                       b.ActualizarCoordenadasdes_banco,
                       c.nro_cheque,
                       c.importe,
                       c.tipo_movimiento,
                       c.fecha_operacion,
                       to_char(c.fecha_insercion,'dd/mm/yyyy hh24:mi'),
                       u.usuario,
                       c.estado,
                       e.des_entidad ,
                       c.observacion 
                 from admin.ctrl_cheques_sub_red c, admin.banco b, admin.usuario u, admin.entidad e
                 where c.cod_banco_cheque = b.cod_banco
                   and c.cod_usuario_alta = u.cod_usuario
                   and c.cod_empresa = u.cod_empresa
                   and c.estado='P'
				   and c.cod_operacion='$codOperacion'
                   and c.cod_recaudadora=e.cod_entidad
                   and c.cod_empresa=e.cod_empresa
                 order by c.cod_operacion";

    $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.='<h2>..:: Confirmaci&oacute;n de Cheques Rechazados - proNET SUB RED ::..</h2>
                <table id="table" name="table" cellspacing="1" class="tablesorter">
                    <thead>
                        <tr><th>Aprobar&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Rechazar&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Entidad&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Banco&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Nro Cheque&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Importe&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Tipo Mov.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Fecha Operacion&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Fecha Solicitud&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Usuario Solicitante&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Estado&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Obs. &nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <tr>
                    </thead>
                    <tbody>';
    $contador = "";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $aprobar = '"' . $fila[0] . '","A"';
        $rechazar = '"' . $fila[0] . '","R"';
        $adic.="<tr><td><a href='#' onclick='javascript:accionOperacion(" . $aprobar . ");' title='Click para APROBAR este Cheque Rechazado'>";
        $adic.='Aprobar</a></td>';
        $adic.="<td><a href='#' onclick='javascript:accionOperacion(" . $rechazar . ");' title='Click para RECHAZAR este Cheque Rechazado'>";
        $adic.='Rechazar</a></td>';

        $adic.="  <td>" . $fila[9] . "</td>
                            <td>" . $fila[1] . "</td>
                            <td>" . $fila[2] . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[3], 0) . "</td>
                            <td>" . $fila[4] . "</td>
                            <td>" . $fila[5] . "</td>
                            <td>" . $fila[6] . "</td>
                            <td>" . $fila[7] . "</td>
                            <td>" . $fila[8] . "</td>
                            <td>" . $fila[10] . "</td>
                         </tr>";
        $est = '1';
        $msg = 'OK';
        $contador++;
    }
    $adic.="</tbody></table>";


    oci_close($ora_conn);
    if (!empty($contador)) {
    }else{
        $msg="No se encontro datos solicitados con nro de operacion ".$codOperacion;
        //$est='1';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    }/*
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );
    $resulGetNoficaciones = getNotificacion($id, $log, $idXLog, '');
        $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);
*/

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function actualizarChqRechazados($log, $idXLog, $codOperacion, $estado, $obsAutorizacion) {

    $id = 21;
    $query = "update admin.ctrl_cheques_sub_red c
              set  c.estado='" . $estado . "',  c.cod_usuario_aut = '" . $_SESSION['codUsuario'] . "', c.fecha_autorizacion=sysdate, c.observacion_autoriz='" . trim($obsAutorizacion) . "' where c.cod_operacion='" . $codOperacion . "'";
    $msg = '';
    $adic = '';

    $log->write($idXLog, 0, 'commonFunctions.php/actualizarChqRechazados', 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/actualizarChqRechazados', 'ERROR1: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/actualizarChqRechazados', 'ERROR2: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);

    if ($committed) {
        $est = '1';
        $msg = 'OK';
        $adic = "Registro Actualizado correctamente";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }
    oci_close($ora_conn);
}

function genListadoDesembolsoSucursalEficash($log, $idXLog, $fecha, $estado, $moneda) {



    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;
    $id = 25;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoDesembolsoSucursalEficash', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "  select trunc(m.fecha_movimiento),
                       c.nombre,
                       sum(decode(co.tipo_movimiento,'D',m.importe,0)) importe_deb,
                       sum(decode(co.tipo_movimiento,'C',m.importe,0)) importe_cred,
                       sum(decode(co.tipo_movimiento,'C',m.importe,0)) -  sum(decode(co.tipo_movimiento,'D',m.importe,0)) credito_neto,
                       mo.siglas
                  from admin.movimiento_tc_manual m,
                       admin.concepto_movimiento_tc co,
                       admin.movimiento_tc_manual_cab mc,
                       admin.cuenta_cliente_tc cc,
                       admin.persona p,
                       rrhh.empleado e,
                       waldbott_g_v8.ccostos c,
                       admin.moneda mo
                where  m.cod_empresa = co.cod_empresa
                       and m.cod_entidad = co.cod_entidad
                       and m.cod_concepto_mov_tc = co.cod_concepto_mov_tc
                       and m.cod_empresa=mc.cod_empresa
                       and m.cod_entidad=mc.cod_entidad
                       and m.cod_mov_cab=mc.cod_mov_cab
                       and mc.autorizado_sn='" . $estado . "'
                       and m.cod_empresa = cc.cod_empresa
                       and m.cod_entidad = cc.cod_entidad
                       and m.nro_cuenta = cc.nro_cuenta
                       and cc.cod_persona = p.cod_persona
                       and trim(p.nro_documento) = trim(to_char(e.nrodocto))
                       and e.ccosto=c.ccosto
                       and cc.cod_moneda = mo.cod_moneda
                       and m.cod_empresa='01'
                       and m.cod_entidad=367
                       and  m.fecha_movimiento>=to_date('" . $fecha . "','dd/mm/yyyy')
                       and  m.fecha_movimiento<to_date('" . $fecha . "','dd/mm/yyyy')+1
                       and m.cod_moneda=" . $moneda . "
                 group by trunc(m.fecha_movimiento),
                       c.nombre,
                       mo.siglas ";

    $log->write($idXLog, 0, 'commonFunctions.php/genListadoDesembolsoSucursalEficash', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoDesembolsoSucursalEficash', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoDesembolsoSucursalEficash', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.='<hr><h2>..:: Montos a Desembolsar por Sucursal - proNET ::..</h2>
                <table id="table" name="table" cellspacing="1" class="tablesorter">
                    <thead>
                        <tr><th>Fecha&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Local&nbsp;&nbsp;&nbsp;&nbsp;</th> 
                            <th>Imp. Debito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th> 
                            <th>Imp. Credito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Imp. Neto&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                             <th>Moneda&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <tr>
                    </thead>
                    <tbody>';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="  <td>" . $fila[0] . "</td>
                            <td>" . $fila[1] . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[2], 0) . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[3], 2) . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[4], 0) . "</td> 
                           <td>" . $fila[5] . "</td>         
                         </tr>";
        $est = '1';
        $msg = 'OK';
    }
    $adic.="</tbody></table>";


    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);



    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/genListadoDesembolsoSucursalEficash', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function genListadoCobroCajaCajeroFecha($log, $idXLog, $id, $fechaRep, $fechaRepF) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;
    $id = 25;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $log->write($idXLog, 0, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'fecha: ' . $fechaRep);
    $query = " select e.des_entidad,
       sr.des_sucursal_rec,
       upper(ue.nombre || ', ' || ue.apellido),
       ue.usuario,
       to_char(t.fecpago, 'dd/mm/yyyy') fechapago,
       t.nro_caja,
       t.nro_lote,
       m.des_moneda,
       sum(decode(s.tipo_mov, 'D', 1, 0)) cant_debito,
       sum(decode(s.tipo_mov, 'D', t.importe, 0)) debito,
       sum(decode(s.tipo_mov, 'C', 1, 0)) cant_credito,
       sum(decode(s.tipo_mov, 'C', t.importe, 0)) credito,
       'Servicio' , 
        decode(t.cod_tipo_pago,1,'Efectivo','Cheque')  , 
to_char(t.fecha_credito_disponible, 'dd/mm/yyyy') fecha_valor
, decode(t.cod_banco_clearing, 11, 'REGIONAL', b.des_banco)
  from  admin.transaccion          t,
       admin.entidad              e,
       admin.sucursal_recaudadora sr,
       admin.usuario_externo      ue,
       admin.moneda               m,
       admin.servicio             s,
       admin.tipo_pago            tp,
       admin.banco_emisora        be,
       admin.banco                b
      
 where t.cod_empresa = e.cod_empresa
   and t.cod_recaudadora = e.cod_entidad
   and t.cod_empresa = sr.cod_empresa
   and t.cod_recaudadora = sr.cod_recaudadora
   and t.cod_sucursal_rec = sr.cod_sucursal_rec
   and t.cod_empresa = ue.cod_empresa
   and t.cod_recaudadora = ue.cod_entidad
   and t.cod_usuario_externo = ue.cod_usuario
   and t.cod_moneda = m.cod_moneda
   and t.cod_empresa = s.cod_empresa
   and t.cod_emisora = s.cod_emisora
   and t.cod_servicio = s.cod_servicio
     and t.cod_empresa = be.cod_empresa
   and t.cod_emisora = be.cod_emisora
   and t.cod_banco_clearing = be.cod_banco
   and t.cod_moneda = be.cod_moneda
   and be.cod_banco = b.cod_banco
   and t.cod_empresa = '01'
   and t.cod_recaudadora = 367
   and t.fecpago >= to_date('" . $fechaRep . "', 'dd/mm/yyyy')    
   and t.fecpago < to_date('" . $fechaRepF . "', 'dd/mm/yyyy') + 1  
   and t.cod_tipo_transaccion = 3
   and t.anulado = 'N'
   and (t.enlinea = 'N' OR (t.enlinea = 'S' and t.cod_estado = 'A'))
   and t.cod_tipo_pago=tp.cod_tipo_pago
 group by e.des_entidad,
          sr.des_sucursal_rec,       
          ue.nombre,        
          ue.apellido,        
          ue.usuario,        
          trunc(t.fecpago),        
          trunc(t.fecha_credito_disponible),
          t.nro_caja,        
          t.nro_lote,        
          m.des_moneda,        
          tp.des_tipo_pago,        
          to_char(t.fecpago, 'dd/mm/yyyy'),        
          to_char(t.fecha_credito_disponible, 'dd/mm/yyyy'),        
          decode(t.cod_tipo_pago, 1, 'Efectivo', 'Cheque'), decode(t.cod_banco_clearing, 11, 'REGIONAL', b.des_banco)
union all 
select e.des_entidad,
     decode(sr.cod_sucursal_rec,13,'PRONET - PISO 1',14,'PRONET S.A - Suc. Villa Morra',sr.des_sucursal_rec), -- sr.des_sucursal_rec,
       upper(c.nom_cajero || ', ' || c.ape_cajero),
       c.nom_usuario,
      to_char(p.fechapago, 'dd/mm/yyyy') ,
       p.nro_caja,
       p.nro_sec_ape nro_lote,
       'Guarani' des_moneda,
       to_number('0'),
       to_number('0'),
       count(*) cantidad,
       sum(p.importe) volumen,
       'Pagos MH',  
decode(trim(p.cod_medio_pago), 'E', 'Efectivo', 'Cheque') , 
to_char(p.fecha_envio_mh, 'dd/mm/yyyy') fecha_valor,
       bs.nom_banco
  from admin.pagos_mh p,     
       admin.entidad e,     
       admin.sucursal_recaudadora sr,     
       admin.cajero_set c,
       admin.bancos_set bs
 where p.cod_empresa = e.cod_empresa
   and p.cod_recaudadora = e.cod_entidad
   and p.cod_empresa = sr.cod_empresa
   and p.cod_recaudadora = sr.cod_recaudadora
   and p.cod_suc_ent = sr.cod_sucursal_rec
    and p.cod_era = c.cod_era(+)
   and p.cod_sucursal_mh = c.cod_sucursal_mh(+)
   and p.cod_cajero = c.cod_cajero(+)
      and p.cod_banco_cle = bs.cod_banco 
   and p.fechapago >= to_date('" . $fechaRep . "', 'dd/mm/yyyy')    
   and p.fechapago < to_date('" . $fechaRepF . "', 'dd/mm/yyyy') + 1   
   and p.cod_recaudadora = 367
   and p.cod_empresa='01'
 group by e.des_entidad,  
          sr.cod_sucursal_rec,      
          sr.des_sucursal_rec,        
          c.nom_cajero,        
          c.ape_cajero,        
          c.nom_usuario,        
          p.nro_caja,        
          p.nro_sec_ape,        
          to_char(p.fechapago, 'dd/mm/yyyy'),        
          to_char(p.fecha_envio_mh, 'dd/mm/yyyy'),        
          decode(trim(p.cod_medio_pago), 'E', 'Efectivo', 'Cheque'), bs.nom_banco
 order by 5, 4,6, 7";

    $log->write($idXLog, 0, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.='
                <table id="table" name="table" cellspacing="1" class="tablesorter">
                    <thead>
                        <tr><th>Entidad&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Sucursal&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Cajero&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Usuario&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Fec. Pago&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Fec. Valor&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Caja&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Lote&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                             <th>Tipo Pago &nbsp;&nbsp;</th>
                            <th>Moneda&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Cant. Debito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Imp. Debito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Cant. Credito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Imp. Credito&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Tipo Cobro&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                             <th>Bco. Clearing&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <tr>
                    </thead>
                    <tbody>';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="  <td>" . $fila[0] . "</td>
                            <td>" . $fila[1] . "</td>
                            <td>" . $fila[2] . "</td> 
                            <td>" . $fila[3] . "</td>
                            <td>" . $fila[4] . "</td>
                            <td>" . $fila[14] . "</td>    
                            <td>" . $fila[5] . "</td>
                            <td>" . $fila[6] . "</td>
                            <td>" . $fila[13] . "</td>    
                            <td>" . $fila[7] . "</td>    
                            <td style=' text-align:right;'>" . numberFormater($fila[8], 0) . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[9], 0) . "</td>
                            <td style=' text-align:right;'>" . numberFormater($fila[10], 0) . "</td> 
                            <td style=' text-align:right;'>" . numberFormater($fila[11], 0) . "</td>  
                             <td>" . $fila[12] . "</td>
                             <td>" . $fila[15] . "</td>    
                         </tr>";
        $est = '1';
        $msg = 'OK';
    }
    $adic.="</tbody></table>";
    oci_close($ora_conn);

    $fechaRep = str_ireplace('/', $fechaRep);
    $fechaRepF = str_ireplace('/', $fechaRepF);
    $_SESSION['repName'] = 'Cobros_Suc_Caja_Cajero_' . $fechaRep . '_' . $fechaRepF . '';
    $_SESSION['datosImprimir'] = $adic;
    $adic = '<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
                                <hr><h2>..:: Montos cobrados Sucursal-Caja-Cajero - proNET S.A. ::..</h2>' . $adic;
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );
    /*
      $vars = explode("|", $resulGetNoficaciones );
      $id = $vars[0]; $est = $vars[1];  $msg = $vars[2];  $adic = $vars[3];
      unset($vars);

     */

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/genListadoCobroCajaCajeroFecha', 'respuesta: ' . $respuesta . 'Se omite el HTML  y se demoro ' . $totalF4 . ' segundos');

    return $respuesta;
}

function cmbMoneda($log, $idXLog, $id) {
    include 'db.inc.php';
    global $log, $idXLog, $ora_conn;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, '****** cmbMoneda *********');


    $contenido = '';
    $ora_conn = oci_connect($user, $password, $host);
    if (!$ora_conn) {
        $e = oci_error();
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la sentencia en la (02): " . $e['message']);
        return "0|Error en la conexion a BD(01): ";
    }
    $sql = "select m.cod_moneda, m.des_moneda, m.siglas from admin.moneda m order by 1";
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, 'Query:' . $sql);
    $id_sentencia = @oci_parse($ora_conn, $sql);
    // echo $consulta_sql;
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la sentencia en la (02): " . $e['message']);
        $respuesta = "0|Error en la sentencia en la (02): ";

        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la conexion a BD(03): " . $e['message']);
        $respuesta = "0|Error en la conexion a BD(03): ";

        return $respuesta;
    }
    $contenido = '';
    $contadorFilas = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contenido.='<option value="' . $fila[0] . '">' . $fila[1] . '-(' . $fila[2] . ')</option>';
        $contadorFilas++;
    }
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);

    if ($contadorFilas > 0) {

        $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, " ***********************Cmb Generado con Exito!************");

        return "1|" . $contenido;
    }
}

function genListaEntidadEficash($log, $idXLog, $id) {
    include 'db.inc.php';
    global $log, $idXLog, $ora_conn;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, '****** cmbMoneda *********');


    $contenido = '';
    $ora_conn = oci_connect($user, $password, $host);
    if (!$ora_conn) {
        $e = oci_error();
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la sentencia en la (02): " . $e['message']);
        return "0|Error en la conexion a BD(01): ";
    }
    $sql = "select e.cod_entidad, upper(e.des_entidad)
from admin.entidad e, admin.entidad_tc ee
where e.cod_empresa=ee.cod_empresa
and e.cod_entidad=ee.cod_entidad";
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, 'Query:' . $sql);
    $id_sentencia = @oci_parse($ora_conn, $sql);
    // echo $consulta_sql;
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la sentencia en la (02): " . $e['message']);
        $respuesta = "0|Error en la sentencia en la (02): ";

        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $log->write($idXLog, 3, getcwd() . ' ' . __FUNCTION__, "0|Error en la conexion a BD(03): " . $e['message']);
        $respuesta = "0|Error en la conexion a BD(03): ";

        return $respuesta;
    }
    $contenido = '';
    $contadorFilas = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contenido.='<option value="' . $fila[0] . '">' . $fila[1] . '</option>';
        $contadorFilas++;
    }
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);

    if ($contadorFilas > 0) {

        $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, " ***********************Cmb Generado con Exito!************");

        return "1|" . $contenido;
    }
}

function listTransEficash($log, $idXLog, $id, $fecha, $tipoFecha, $entidad, $moneda, $tipoReporte) {
    include("db.inc.php");
    $id = 19;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    if ($tipoFecha == 'P') {
        $tipoFecha = 'fecha_proceso ';
    } else {
        $tipoFecha = 'fecha_movimiento ';
    }
    if ($tipoReporte == 'D') {
        $tipo = " Detallado ";
        $cabecera = "  e.des_entidad, 
					   c.des_concepto_mov_tc, 
					   to_char(m.fecha_movimiento, 'dd/mm/yyyy hh24:mi'),
					   m.fecha_proceso,
					   nvl(decode(c.tipo_movimiento, 'D',m.importe),0),
					   nvl(decode(c.tipo_movimiento, 'C',m.importe),0)  ";
        $group = "";
    } else if ($tipoReporte == 'R') {
        $tipo = " Resumido ";
        $cabecera = "e.des_entidad, 
					 c.des_concepto_mov_tc, 
					 trunc(m.fecha_movimiento),
					 m.fecha_proceso,
					 sum(  nvl(decode(c.tipo_movimiento, 'D',m.importe),0)),
					 sum ( nvl(decode(c.tipo_movimiento, 'C',m.importe),0) ) ";
        $group = "group by  e.des_entidad, c.des_concepto_mov_tc,  trunc(m.fecha_movimiento), c.tipo_movimiento,  m.fecha_proceso ";
    }
    $query = "select $cabecera
			  from admin.entidad e,
				   admin.concepto_movimiento_tc c,
				   admin.movimiento_tc m
			 where m.cod_empresa = e.cod_empresa
				   and m.cod_entidad = e.cod_entidad
				   and m.cod_concepto_mov_tc = c.cod_concepto_mov_tc
				   -- and m.cod_empresa = c.cod_empresa
				   -- and m.cod_entidad = c.cod_entidad
				   and m.cod_empresa='01' 
				   and m.cod_entidad =$entidad
				   and m.$tipoFecha>=to_date('$fecha','dd/mm/yyyy')
				   and m.$tipoFecha<to_date('$fecha','dd/mm/yyyy')+1
				   and m.cod_moneda=$moneda
				   $group
				   order by 3 ";

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.='
            <h2>Reporte de Movimiento  ' . $tipo . ' de la Entidad ' . $fila[0] . ' de fecha ' . $fecha . '</h2>
                 
				
            <table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
                                                        <th>Entidad&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>Concepto&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>Fecha Mov.&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>Fecha Proc.&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>Debito&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                        <th>Credito&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                                       
						</tr>
					</thead>
					<tbody>';
    $totalDebito = 0;
    $totalCredito = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $totalDebito = $totalDebito + $fila[4];
        $totalCredito = $totalCredito + $fila[5];
        $adic.=' <tr>
                            <td>' . $fila[0] . '</td>
                            <td>' . $fila[1] . '</td>
                            <td>' . $fila[2] . '</td>
                            <td>' . $fila[3] . '</td>
                            <td style="text-align:right">' . $fila[4] . '</td> 
                            <td style="text-align:right">' . $fila[5] . '</td> 
                       </tr>';

        $est = '1';
        $msg = 'OK';
    }
    $adic.=' <tr>
                            <td colspan="4"  style="text-align:right"> <b>Total</b></td>
                            <td style="text-align:right"><b>' . $totalDebito . '</b></td> 
                            <td style="text-align:right"><b>' . $totalCredito . '</b></td> 
                       </tr>';
    $adic.="</tbody></table>";
    $_SESSION['repName'] = "Reporte de Movimiento $tipo $fila[0] del $fecha";
    $_SESSION['datosImprimir'] = $adic;

    $adic = '<hr><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>' . $adic;



    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Generaro: ' . $msg . '- Se omite  html xqIPUKU');
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

function comboEjecutivo($log, $idXLog) {
    include("db.inc.php");
    $id = 33;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select e.cod_ejecutivo, e.apellido||', '||e.nombre  from admin.ejecutivo_comercial e
order by 2
";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >Todos</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . " </option>";
        $est = '1';
        $msg = 'OK';
    }


    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasRED', 'Generado: ' . $msg);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    return $respuesta;
}

////////// Area Comercial ////////////////
function repMetaCuatrimestral($log, $idXLog, $id, $anhoProyectado, $cuatrimestre, $ejecutivo, $recaudadora) {
    include("db.inc.php");
    // FECHA CIERRE
    //11-08-2011 JO --> se modifica el criterio de  fecha,  tomando apartir de  ahora la fecha_apertura.
    $tipoFecha = '';
    $contador = 0;
    $cabecera = '';
    $condicion = '';
    if ($anhoProyectado != '') {
        $condicion.='  and cp.anho_proyeccion=' . $anhoProyectado;
    }
    if ($cuatrimestre != '*') {
        $condicion.=' and cp.nro_cuatrimestre=' . $cuatrimestre;
    }
    if ($ejecutivo != '*') {
        $condicion.=' and cp.cod_ejecutivo_rec=' . $ejecutivo;
    }
    if ($recaudadora != '*') {
        $condicion.=' and cp.cod_ejecutivo_rec=' . $recaudadora;
    }


    $_SESSION['repName'] = 'MetaCuatrimestralProyectado' . $anhoProyectado;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select ec.cod_ejecutivo,ec.nombre||' '||ec.apellido, cp.anho_proyeccion, cp.mes,
to_char(to_date(to_char(cp.mes),'mm'),'Month','NLS_DATE_LANGUAGE = SPANISH'), 
sum(cp.cant_trx_ejecutada) realizado,
sum(cp.cant_trx_proyectada) proyectado,
 sum(cp.cant_trx_ejecutada)-sum(cp.cant_trx_proyectada),  
 case  when sum(cp.cant_trx_ejecutada)>sum(cp.cant_trx_proyectada ) then 'Logrado' else 'No Logrado' end as Meta
from admin.calculo_proyeccion cp
join admin.ejecutivo_comercial ec on cp.cod_ejecutivo_rec = ec.cod_ejecutivo
join admin.entidad e on e.cod_entidad=cp.cod_recaudadora
 where to_number(to_char(e.fecha_alta,'yyyy')) < $anhoProyectado " . $condicion . "
group by ec.nombre||' '||ec.apellido, cp.anho_proyeccion, cp.mes,ec.cod_ejecutivo
order by 2,3,4";
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }


    $total_1 = 0;
    $total_2 = 0;
    $s_total_1 = 0;
    $s_total_2 = 0;
    $flag = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {

        $est = 1;
        $msg = 'OK';

        $total_1 += $fila[5];
        $total_2 += $fila[6];

        // Sub-Totales.
        if ($flag == 0) {
            //$codSucursal = $fila['SUC'];
            $codEjecutivo = $fila[0];

            $flag = 1;
        }
        if ($codEjecutivo == $fila[0]) {
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'codEje: ' . $fila[0]);

            $s_total_1 += $fila[5];
            $s_total_2 += $fila[6];
            // Tabla.
            $adic .= '<tr>';

            $adic .= '<td class="mostrar">' . $fila[1] . '</td>';
            $adic .= '<td class="mostrar">' . $fila[4] . '</td>';
            $adic .= '<td class="mostrar">' . numberFormater($fila[5], 0) . '</td>';
            $adic .= '<td class="mostrar">' . numberFormater($fila[6], 0) . '</td>';
            $adic .= '<td class="mostrar">' . $fila[8] . '</td>';
            $adic .= '</tr>';
        } else {
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'codEjeCambio: ' . $fila[0]);
            $porcentotal = ((($s_total_1 / $s_total_2) - 1) * 100);
            $adic .= '<tr>';
            $adic .= '<td class="mostrar"></td>';
            $adic .= '<td class="mostrar"><b>Sub-Total: </b></td>';
            $adic .= '<td class="mostrar"><b>' . numberFormater($s_total_1, 0) . '</b></td>';
            $adic .= '<td class="mostrar"><b>' . numberFormater($s_total_2, 0) . '</b></td>';
            $adic .= '<td class="mostrar"><b> ' . numberFormater($porcentotal, 1) . '%</b> </td>';
            $adic .= '</tr>';

            // Se limpian los sub-totales.
            $s_total_1 = 0;
            $s_total_2 = 0;

            $s_total_1 += $fila[5];
            $s_total_2 += $fila[6];


            // Tabla.
            $adic .= '<tr>';
            $adic .= '<td class="mostrar">' . $fila[1] . '</td>';
            $adic .= '<td class="mostrar">' . $fila[4] . '</td>';
            $adic .= '<td class="mostrar">' . numberFormater($fila[5], 0) . '</td>';
            $adic .= '<td class="mostrar">' . numberFormater($fila[6], 0) . '</td>';
            $adic .= '<td class="mostrar">' . $fila[8] . '</td>';
            $adic .= '</tr>';

            //$codSucursal = $fila['SUC'];
            $codEjecutivo = $fila[0];
        }
        $est = '1';
        $msg = 'OK';
    }
    $porcentotal = ((($s_total_1 / $s_total_2) - 1) * 100);
    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"><b>Sub-Total: </b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($s_total_1, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($s_total_2, 0) . '</b></td>';
    $adic .= '<td class="mostrar"> <b>' . numberFormater($porcentotal, 1) . '%</b> </td>';
    $adic .= '</tr>';

    $adic_ini = '<div id="rep" align="center">
				 
				<ul><li><b>Reporte de Meta Cuatrimestral - Proyectado: ' . $anhoProyectado . '</b></li></ul>
                                <ul><li><b> Cuatrimestral:' . $cuatrimestre . ' </b></li></ul>    
				<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
					<table cellspacing="1" align="center" border="1">
						<thead>
							<tr>
								<th class="new_th">Ejectuvo</th>
								<th class="new_th">Mes</th>
								<th class="new_th">Realizado</th>
								<th class="new_th">Proyectado</th>
								<th class="new_th">Estado</th> 
							</tr>
						</thead>
						<tbody>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '</tr>';

    $adic .= '<tr>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '<td class="mostrar"><b>TOTALES: </b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_1, 0) . '</b></td>';
    $adic .= '<td class="mostrar"><b>' . numberFormater($total_2, 0) . '</b></td>';
    $adic .= '<td class="mostrar"></td>';
    $adic .= '</tr>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/reporteServicioDiario', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

/* * *************** REMESAS ******************* */

function reporteRemesasVsDeposito($log, $idXLog, $id, $fechaDesde, $fechaHasta, $emisor) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ReporteRemesas: ' . $fechaDesde;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    if ($emisor != "*") {
        $condicion = " AND E.COD_ENTIDAD=" . $emisor;
    }
    $query = "SELECT E.DES_ENTIDAD,
				   TO_CHAR(FECPAGO, 'DD/MM/YYYY'),
				   SUM(ENVIO),
				   SUM(ENTREGA),
				   SUM(ENVIO)-SUM(ENTREGA) IMPORTE_A_DEPOSITAR,
				   TO_CHAR(FECHA_HORA_DEPOSITO, 'DD/MM/YYYY') FECHA_HORA_DEPOSITO,
				   SUM(IMPORTE_DEPOSITO)
			  FROM (SELECT BR.COD_RECAUDADORA,
						   SYSDATE FECPAGO,
						   0 ENVIO,
						   0 ENTREGA,
						   D.FECHA_HORA_DEPOSITO,
						   SUM(D.IMPORTE) IMPORTE_DEPOSITO
					  FROM ADMIN.DEPOSITOS_CLEARING D, ADMIN.BANCO_RECAUDADORA BR
					 WHERE D.NRO_CUENTA = BR.NRO_CUENTA_DEBITO
					   AND D.ANULADO = 'N'
					   AND D.FECHA_HORA_DEPOSITO >= TO_DATE('" . $fechaDesde . "','DD/MM/YYYY')
					   AND D.FECHA_HORA_DEPOSITO < TO_DATE('" . $fechaHasta . "','DD/MM/YYYY')+1
					 GROUP BY BR.COD_RECAUDADORA, D.FECHA_HORA_DEPOSITO
					UNION ALL
					SELECT T.COD_RECAUDADORA,
						   T.FECPAGO,
						   SUM(DECODE(S.TIPO_MOV,
									  'C',
									  T.IMPORTE,
									  0)) ENVIO,
						   SUM(DECODE(S.TIPO_MOV,
									  'D',
									  T.IMPORTE,
									  0)) ENTREGA,
						   SYSDATE FECHA_HORA_DEPOSITO,
						   0 IMPORTE_DEPOSITO
					  FROM ADMIN.TRANSACCION T, ADMIN.SERVICIO S
					 WHERE T.COD_EMPRESA = S.COD_EMPRESA
					   AND T.COD_EMISORA = S.COD_EMISORA
					   AND T.COD_SERVICIO = S.COD_SERVICIO
					   AND S.ID_RUBRO_SERVICIO = 23 /*Remesas*/
					   AND T.ANULADO = 'N'
					   
					   AND T.ESTADO_TRX = 'N'
					   
					   AND T.FECPAGO >= TO_DATE('" . $fechaDesde . "','DD/MM/YYYY')
					   AND T.FECPAGO < TO_DATE('" . $fechaHasta . "','DD/MM/YYYY')+1
					 GROUP BY T.COD_RECAUDADORA, T.FECPAGO) R,
				   ADMIN.ENTIDAD E
			 WHERE R.COD_RECAUDADORA = E.COD_ENTIDAD
			 $condicion
			 GROUP BY E.DES_ENTIDAD,
					  TO_CHAR(FECPAGO, 'DD/MM/YYYY'),
					  TO_CHAR(FECHA_HORA_DEPOSITO, 'DD/MM/YYYY')
		  	 ORDER BY 1,2";
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $adic = '<style>
			#rojo{
				/*background:#C00; */
				color:red;
				font-weight:bold;
			}
			#verde{
				background:#9C0; 
				color:#000;
			}
			</style>
			
			';
    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>ENTIDAD</th>
					<th>FECHA PAGO</th>
					<th>ENVIO</th>
					<th>ENTREGA</th>
					<th>IMPORTE A DEPOSITAR</th>
					<th>FECHA DEPOSITO</th>
					<th>IMPORTE DEPOSITO</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        if ($fila[4] <= $fila[6]) {
            $elId = 'id=""';
        } else {
            $elId = 'id="rojo"';
        }
        $adic .= '<td ' . $elId . '>' . $fila[0] . '</td>';
        $adic .= '<td ' . $elId . '>' . $fila[1] . '</td>';
        $adic .= '<td ' . $elId . '>' . numberFormater($fila[2], 0) . '</td>';
        $adic .= '<td ' . $elId . '>' . numberFormater($fila[3], 0) . '</td>';
        if ($fila[4] < 0) {
            $adic .= '<td ' . $elId . '>0</td>';
        } else {
            $adic .= '<td ' . $elId . '>' . numberFormater($fila[4], 0) . '</td>';
        }
        $adic .= '<td ' . $elId . '>' . $fila[5] . '</td>';
        $adic .= '<td ' . $elId . '>' . numberFormater($fila[6], 0) . '</td>';
        $adic .= '</tr>';
    }
    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/remesasVsDeposito', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function listadoEntidad($log, $idXLog, $id) {
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $query = "SELECT E.COD_ENTIDAD, E.DES_ENTIDAD
				    FROM ADMIN.ENTIDAD E
				   /*WHERE E.TIPO_ENT_RED IN('RE', 'E')*/
				   ORDER BY 2";

    $respuesta = '<OPTION VALUE="*">..:: Todas las Emisoras ::..</OPTION>';
    $id_sentencia = @oci_parse($ora_conn, $query);
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
    }
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $respuesta .= '<OPTION VALUE=' . $fila[0] . '> ' . $fila[1] . '</OPTION>';
    }
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);
    return $respuesta;
}

function reporteRemesasFechaProceso($log, $idXLog, $id, $fechaDesde, $fechaHasta, $emisor) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ReporteRemesas: ' . $fechaDesde;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    if ($emisor != "*") {
        $condicion = " AND ER.COD_ENTIDAD=" . $emisor;
    }
    $query = "SELECT T.FECPAGO,
				   T.FECHA_PROCESO,
				   ER.DES_ENTIDAD || ' ' || SR.DES_SUCURSAL_REC ENTIDAD_SUC,
				   EM.DES_ENTIDAD ENTIDAD,
				   S.DES_SERVICIO,
				   T.IMPORTE,
				   T.COMISION
			  FROM ADMIN.TRANSACCION          T,
				   ADMIN.SERVICIO             S,
				   ADMIN.ENTIDAD              ER,
				   ADMIN.ENTIDAD              EM,
				   ADMIN.SUCURSAL_RECAUDADORA SR
			 WHERE T.COD_RECAUDADORA = ER.COD_ENTIDAD
			   AND T.COD_EMISORA = EM.COD_ENTIDAD
			   AND T.COD_RECAUDADORA = SR.COD_RECAUDADORA
			   AND T.COD_SUCURSAL_REC = SR.COD_SUCURSAL_REC
			   AND T.COD_EMPRESA = S.COD_EMPRESA
			   AND T.COD_EMISORA = S.COD_EMISORA
			   AND T.COD_SERVICIO = S.COD_SERVICIO
			   AND T.FECHA_PROCESO >= TRUNC(TO_DATE('" . $fechaDesde . "', 'DD/MM/YYYY'))
			   AND T.FECHA_PROCESO < TRUNC(TO_DATE('" . $fechaHasta . "', 'DD/MM/YYYY')) + 1
			   AND T.COD_TIPO_TRANSACCION = 3
			   AND T.ANULADO = 'N'
			   AND (T.ENLINEA = 'N' OR (T.ENLINEA = 'S' AND COD_ESTADO = 'A'))
			   AND T.ESTADO_TRX != 'E'
			   AND S.ID_RUBRO_SERVICIO = 23 /*Rubro remesas*/
			   AND T.COD_MONEDA = 1
			   $condicion
			 ORDER BY 1";
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>FECHA PAGO</th>
					<th>FECHA PROCESO</th>
					<th>SUCURSAL</th>
					<th>ENTIDAD</th>
					<th>SERVICIO</th>
					<th>IMPORTE</th>
					<th>COMISION</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<td >' . $fila[0] . '</td>';
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . $fila[2] . '</td>';
        $adic .= '<td >' . $fila[3] . '</td>';
        $adic .= '<td >' . $fila[4] . '</td>';
        $adic .= '<td >' . $fila[5] . '</td>';
        $adic .= '<td >' . $fila[6] . '</td>';
        $adic .= '<td >' . $fila[7] . '</td>';
        $adic .= '</tr>';
    }
    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/remesasVsDeposito', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function reportePerfilDeClientes($log, $idXLog, $id, $fechaDesde, $fechaHasta, $nroDocumento) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ReportePerfilClientes: ' . $fechaDesde;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT e.DESC_FACTOR, e.PUNTAJE, e.PONDERACION, e.FACTOR, e.NRO_DOCUMENTO 
			  FROM REMESAS.PERFIL_CLIENTE E
			 WHERE E.FECHA_HORA_ENVIO >= TO_DATE('" . $fechaDesde . "','DD/MM/YYYY')
			   AND E.FECHA_HORA_ENVIO < TO_DATE('" . $fechaHasta . "','DD/MM/YYYY')+1
			   AND E.NRO_DOCUMENTO = '" . $nroDocumento . "'
                   group by e.DESC_FACTOR, e.PUNTAJE, e.PONDERACION, e.FACTOR, e.NRO_DOCUMENTO";

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>DESCRIPCION</th>
					<th>PUNTAJE</th>
					<th>PONDERACION</th>
					<th>FACTOR</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $fact = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<td >' . $fila[0] . '</td>';
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . $fila[2] . '</td>';
        $adic .= '<td >' . $fila[3] . '</td>';
        $adic .= '</tr>';
        $fact = $fact + $fila[3];
    }
    $adic.="</tbody></table> <br>
                  <b style='color:#000'>Total de Factores : " . $fact . "</b>";
    $adic.=' </br><hr></br><table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse; width:50%">
			<thead>
                                <tr ALIGN="CENTER">
					<th colspan="2"> ESCALA CONSIDERADA</th>
				</tr>			

                            <tr ALIGN="CENTER">
				 <th>PUNTAJE</th>
				 <th>RIESGO</th> 
                            </tr>
			</thead>
		   <tbody>
                        <tr ALIGN="CENTER"><td>  < 45	</td><td> Bajo</td></tr>
                        <tr ALIGN="CENTER"><td>45 a 60</td><td>	Medio</td></tr>
                        <tr ALIGN="CENTER"><td>> 60	</td><td>Alto</td></tr>
                  </tbody></table> ';
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center"><hr>
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/remesasVsDeposito', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function numberFormater($number, $decimal) {
    if (empty($decimal)) {
        $decimal = 0;
    }
    $number = str_ireplace(',', '.', $number);
    $number = number_format($number, $decimal, ',', '.');
    return $number;
}

/* ANULAR REMESAS */

function listadoRemesadora($log, $idXLog, $id) {
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $query = "SELECT * FROM REMESAS.MARCA_REMESA MR WHERE MR.ACTIVO='S'";

    $respuesta = '<OPTION VALUE="*">..:: Seleccione una Remesadora ::..</OPTION>';
    $id_sentencia = @oci_parse($ora_conn, $query);
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
    }
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        //$respuesta .= '<OPTION VALUE='.$fila[0].'|'.$fila[2].'> '.$fila[1].'</OPTION>';
        $respuesta .= '<OPTION VALUE=' . $fila[0] . '> ' . $fila[1] . '</OPTION>';
    }
    oci_free_statement($id_sentencia);
    oci_close($ora_conn);
    return $respuesta;
}

function listadoAnuladosEnvio($log, $idXLog, $id, $remesadora, $empresa) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ListadoRemesadora ' . $remesadora;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT E.ANHO_ENVIO,
				   E.FECHA_HORA_ENVIO,
				   RE.NOM_CLIENTE,
				   RE.APE_CLIENTE,
				   DE.NOM_CLIENTE,
				   DE.APE_CLIENTE,
				   E.COD_ENVIO,
				   D.IMPORTE_ENVIO
			  FROM REMESAS.ENVIO     E,
				   REMESAS.CLIENTES  RE,
				   REMESAS.DET_ENVIO D,
				   REMESAS.CLIENTES  DE
			 WHERE E.COD_EMPRESA = D.COD_EMPRESA
			   AND E.COD_ENTIDAD = D.COD_ENTIDAD
			   AND E.COD_SUCURSAL_ENTIDAD = D.COD_SUCURSAL_ENTIDAD
			   AND E.ANHO_ENVIO = D.ANHO_ENVIO
			   AND E.ANHO_ARQUEO = D.ANHO_ARQUEO
			   AND E.NRO_ARQUEO = D.NRO_ARQUEO
			   AND E.COD_ENVIO = D.COD_ENVIO
			   AND E.COD_CLIENTE_REMITENTE = RE.COD_CLIENTE
			   AND E.COD_CLIENTE_DESTINATARIO = DE.COD_CLIENTE
			   AND E.ENTREGADO = 'N'
			   AND E.ANULADO='N'
			   AND E.COD_MARCA_REMESA = " . $remesadora;


    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>FECHA ENVIO</th>
					<th>CLIENTE REMITENTE</th>
					<th>CLIENTE DESTINATARIO</th>
					<th>IMPORTE</th>
					<th>COD. ENVIO</th>
					<th>ACCION</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $incremento = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $incremento++;
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . strtoupper($fila[2] . ' ' . $fila[3]) . '</td>';
        $adic .= '<td >' . strtoupper($fila[4] . ' ' . $fila[5]) . '</td>';
        $adic .= '<td >' . $fila[7] . '</td>';
        $adic .= '<td >' . $fila[COD_ENVIO] . '</td>';
        $adic .= '<td ><div id="campo_' . $incremento . '"><input type="button" value=" Solicitar Anulacion " style="background-color:#030; color:#FFF; padding:3px;" onclick="pendienteAnulacionEnvio(' . $fila[COD_ENVIO] . ',' . $incremento . ')"></div></td>';
        $adic .= '</tr>';
    }
    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/ListadoRemesadora', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function pendienteAnulacionEnvio($log, $idXLog, $id, $cod_envio, $observacion) {
    $query = "UPDATE remesas.envio SET anulado='P', obs_anulacion='" . $observacion . "' WHERE cod_envio=" . $cod_envio . "";
    $msg = '';
    $adic = '';

    $log->write($idXLog, 0, 'commonFunctions.php/pendienteAnulacionEnvio', 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/pendienteAnulacionEnvio', 'ERROR1: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/pendienteAnulacionEnvio', 'ERROR2: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);

    if ($committed) {
        $est = '1';
        $msg = 'OK';
        $adic = "Actualizado correctamente";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }
    oci_close($ora_conn);
}

function listadoAnuladosEntrega($log, $idXLog, $id, $remesadora, $empresa) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ListadoRemesadoraEntrega' . $remesadora;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT E.ANHO_ENTREGA,
				   E.FECHA_HORA_ENTREGA,
				   RE.NOM_CLIENTE,
				   RE.APE_CLIENTE,
				   DE.NOM_CLIENTE,
				   DE.APE_CLIENTE,
				   D.IMPORTE_ENTREGA,
				   E.COD_ENTREGA
			  FROM REMESAS.ENTREGA     E,
				   REMESAS.DET_ENTREGA D,
				   REMESAS.ENVIO       EN,
				   REMESAS.CLIENTES    RE,
				   REMESAS.CLIENTES    DE
			 WHERE E.COD_EMPRESA = D.COD_EMPRESA
			   AND E.COD_ENTIDAD = D.COD_ENTIDAD
			   AND E.ANHO_ENTREGA = D.ANHO_ENTREGA
			   AND E.COD_SUCURSAL_ENTIDAD = D.COD_SUCURSAL_ENTIDAD
			   AND EN.COD_ENVIO = E.COD_ENVIO
			   AND E.COD_ENTREGA = D.COD_ENTREGA
			   /*CLIENTES*/
			   AND EN.COD_CLIENTE_REMITENTE = RE.COD_CLIENTE
			   AND EN.COD_CLIENTE_DESTINATARIO = DE.COD_CLIENTE
			   /*CONDICIONE Y PARAMETROS*/
			   AND EN.ENTREGADO = 'N'
			   AND EN.COD_MARCA_REMESA = " . $remesadora;


    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>FECHA ENTREGA</th>
					<th>CLIENTE REMITENTE</th>
					<th>CLIENTE DESTINATARIO</th>
					<th>IMPORTE</th>
					<th>COD. ENTREGA</th>
					<th>ACCION</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $incremento = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $incremento++;
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . strtoupper($fila[2] . ' ' . $fila[3]) . '</td>';
        $adic .= '<td >' . strtoupper($fila[4] . ' ' . $fila[5]) . '</td>';
        $adic .= '<td >' . $fila[7] . '</td>';
        $adic .= '<td >' . $fila[COD_ENTREGA] . '</td>';
        $adic .= '<td ><div id="campo_' . $incremento . '"><input type="button" value="    Anular Entrega    " style="background-color:#030; color:#FFF; padding:3px;" onclick="pendienteAnulacionEntrega(' . $fila[COD_ENTREGA] . ',' . $incremento . ')"></td>';
        $adic .= '</tr>';
    }
    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/ListadoRemesadora', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function pendienteAnulacionEntrega($log, $idXLog, $id, $cod_entrega, $observacion) {

    $query = "UPDATE remesas.entrega 
				 SET anulado='P', 
				 obs_anulacion='" . $observacion . "' 
			   WHERE cod_entrega=" . $cod_entrega . "";
    $msg = '';
    $adic = '';

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION___, 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION___, 'ERROR1: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION___, 'ERROR2: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);

    if ($committed) {
        $est = '1';
        $msg = 'OK';
        $adic = "Actualizado correctamente";
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }
    oci_close($ora_conn);
}

function listadoAutorizaAnulacionEnvio($log, $idXLog, $id, $remesadora) {
    include("db.inc.php");
    $_SESSION['repName'] = 'ListadoAnulacionEnvio ' . $remesadora;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "SELECT E.ANHO_ENVIO,
				   E.FECHA_HORA_ENVIO,
				   RE.NOM_CLIENTE,
				   RE.APE_CLIENTE,
				   DE.NOM_CLIENTE,
				   DE.APE_CLIENTE,
				   E.COD_ENVIO,
				   D.IMPORTE_ENVIO
			  FROM REMESAS.ENVIO     E,
				   REMESAS.CLIENTES  RE,
				   REMESAS.DET_ENVIO D,
				   REMESAS.CLIENTES  DE
			 WHERE E.COD_EMPRESA = D.COD_EMPRESA
			   AND E.COD_ENTIDAD = D.COD_ENTIDAD
			   AND E.COD_SUCURSAL_ENTIDAD = D.COD_SUCURSAL_ENTIDAD
			   AND E.ANHO_ENVIO = D.ANHO_ENVIO
			   AND E.ANHO_ARQUEO = D.ANHO_ARQUEO
			   AND E.NRO_ARQUEO = D.NRO_ARQUEO
			   AND E.COD_ENVIO = D.COD_ENVIO
			   AND E.COD_CLIENTE_REMITENTE = RE.COD_CLIENTE
			   AND E.COD_CLIENTE_DESTINATARIO = DE.COD_CLIENTE
			   AND E.ENTREGADO = 'N'
			   AND E.ANULADO = 'P'
			   AND E.COD_MARCA_REMESA = " . $remesadora;


    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>FECHA ENVIO</th>
					<th>CLIENTE REMITENTE</th>
					<th>CLIENTE DESTINATARIO</th>
					<th>IMPORTE</th>
					<th>COD. ENVIO</th>
					<th>ACCION</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $incremento = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $incremento++;
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . strtoupper($fila[2] . ' ' . $fila[3]) . '</td>';
        $adic .= '<td >' . strtoupper($fila[4] . ' ' . $fila[5]) . '</td>';
        $adic .= '<td >' . $fila[7] . '</td>';
        $adic .= '<td >' . $fila[COD_ENVIO] . '</td>';
        $adic .= '<td ><div id="campo_' . $incremento . '"><input type="button" value=" Anular " style="background-color:#030; color:#FFF; padding:3px;" onclick="autorizarAnulacionEntrega(' . $fila[COD_ENVIO] . ',' . $incremento . ')"></div></td>';
        $adic .= '</tr>';
    }
    $adic .= "</tbody></table>";
    if ($totalReg == 0) {
        $est = 3;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function autorizarAnulacion($log, $idXLog, $id, $codigo, $remesadora, $observacion, $tipo) {
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Remesadora: ' . $remesadora . ' Tipo: ' . $tipo . ', Codigo: ' . $codigo);
    if ($tipo == "ENVIO") {
        if ($remesadora == 3) {
            //CASO REMESADORA PRONET
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora PRONET, para anulacion envio.');
            echo $aux = actualizaAnulacionParam($log, $idXLog, $codigo, $observacion, 'S', 'remesas.envio', 'cod_envio');
        } else if ($remesadora == 2) {
            //CASO REMESADORA MORE
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora MORE, para anulacion envio.');
            //ENVIANDO EL MAIL
            echo $aux = envioMail("PARA", "ASUNTO", "MENSAJE", "<EMAIL>");
        } else if ($remesadora == 5) {
            //CASO REMESADORA PERSONAL
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora PERSONAL, para anulacion envio.');
            //DATOS DEL SOCKET
            /* PARAMETROS */
            $sk_codServicio = "01596";
            $sk_tipoTrx = "04";
            $sk_user = "595971938232";
            $sk_pass = "1234";
            $sk_codigo = $codigo;
            $sk_fecha = date("YmdHis");
            /* PADEAR PARAMETROS */
            $sk_codServicio = str_pad($sk_codServicio, 5, " ", STR_PAD_LEFT);
            $sk_tipoTrx = str_pad($sk_tipoTrx, 2, " ", STR_PAD_LEFT);
            $sk_user = str_pad($sk_user, 20, " ", STR_PAD_LEFT);
            $sk_pass = str_pad($sk_pass, 20, " ", STR_PAD_LEFT);
            $sk_codigo = str_pad($sk_codigo, 20, " ", STR_PAD_LEFT);
            $sk_fecha = str_pad($sk_fecha, 14, " ", STR_PAD_LEFT);
            /* CONCATENAR VALORES */
            $sk_mensaje = $sk_codServicio . $sk_tipoTrx . $sk_user . $sk_pass - $sk_codigo . $sk_fecha;
            /* ENVIANDO DATOS POR SOCKET */
            $aux = socketPrn("30000", "*************", $sk_mensaje);
            //0159604    0                                                                                    Reversa Exitosa.]
            /* RESULTADOS OBTENIDOS */
            $rsk_codServicio = substr($aux, 0, 5);
            $rsk_tipoTrx = substr($aux, 5, 2);
            $rsk_respuesta = substr($aux, 7, 5);
            $rsk_mensaje_respues = substr($aux, 12, -1);

            if ($rsk_respuesta == 0) {
                echo "1|1|" . $rsk_mensaje_respues;
            } else {
                echo "1|0|" . $rsk_mensaje_respues;
            }
        }
    } else if ($tipo == "ENTREGA") {
        if ($remesadora == 3) {
            //CASO REMESADORA PRONET
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora PRONET, para anulacion entrega.');
            echo $aux = actualizaAnulacionParam($log, $idXLog, $codigo, $observacion, 'S', 'remesas.entrega', 'cod_entrega');
        } else if ($remesadora == 2) {
            //CASO REMESADORA MORE
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora MORE, para anulacion entrega.');
            //ENVIANDO EL MAIL
            echo $aux = envioMail("PARA", "ASUNTO", "MENSAJE", "<EMAIL>");
        } else if ($remesadora == 5) {
            //CASO REMESADORA PERSONAL
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Entro en remesadora PERSONAL, para anulacion entrega.');
            //DATOS DEL SOCKET

            /* PARAMETROS */
            $sk_codServicio = "01596";
            $sk_tipoTrx = "04";
            $sk_user = "595971938232";
            $sk_pass = "1234";
            $sk_codigo = $codigo;
            $sk_fecha = date("YmdHis");

            /* PADEAR PARAMETROS */
            $sk_codServicio = str_pad($sk_codServicio, 5, " ", STR_PAD_LEFT);
            $sk_tipoTrx = str_pad($sk_tipoTrx, 2, " ", STR_PAD_LEFT);
            $sk_user = str_pad($sk_user, 20, " ", STR_PAD_LEFT);
            $sk_pass = str_pad($sk_pass, 20, " ", STR_PAD_LEFT);
            $sk_codigo = str_pad($sk_codigo, 20, " ", STR_PAD_LEFT);
            $sk_fecha = str_pad($sk_fecha, 14, " ", STR_PAD_LEFT);

            /* CONCATENAR VALORES */
            $sk_mensaje = $sk_codServicio . $sk_tipoTrx . $sk_user . $sk_pass - $sk_codigo . $sk_fecha;

            /* ENVIANDO DATOS POR SOCKET */
            $aux = socketPrn("30000", "*************", $sk_mensaje);
            //EJ:0159604    0                                                                                    Reversa Exitosa.]

            /* RESULTADOS OBTENIDOS */
            $rsk_codServicio = substr($aux, 0, 5);
            $rsk_tipoTrx = substr($aux, 5, 2);
            $rsk_respuesta = substr($aux, 7, 5);
            $rsk_mensaje_respues = substr($aux, 12, -1);

            if ($rsk_respuesta == 0) {
                echo "1|1|" . $rsk_mensaje_respues;
            } else {
                echo "1|0|" . $rsk_mensaje_respues;
            }
        }
    }
}

function actualizaAnulacionParam($log, $idXLog, $codigo, $observacion, $estado, $tabla, $campo) {
    $query = "UPDATE " . $tabla . " SET 
				anulado = '" . $estado . "', 
				obs_anulacion = obs_anulacion ||' <br />" . $observacion . "'
			  WHERE " . $campo . "=" . $codigo . "";
    $msg = '';
    $adic = '';

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR1: ' . $msg);
        $est = '0';
        $respuesta = "1|" . $est;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR2: ' . $msg);
        $est = '0';
        $respuesta = "1|" . $est;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);

    if ($committed) {
        $est = '1';
        $respuesta = "1|" . $est;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = '1';
        $respuesta = "1|" . $est;
        return $respuesta;
    }
    oci_close($ora_conn);
}

function envioMail($para, $asunto, $mensaje, $desde) {
    $headers = 'From:' . $desde . '\r\n' .
            'Reply-To: ' . $desde . "\r\n" .
            'X-Mailer: PHP/' . phpversion();
    $headers .= "MIME-Version: 1.0\n";
    $headers .= "Content-Type: text/html; charset=\"iso-8859-1\"\n";
    $headers .= "X-Priority: 1 (Higuest)\n";
    $headers .= "X-MSMail-Priority: High\n";
    $headers .= "Importance: High\n";
    //echo $subjetc;
    if (mail($para, $asunto, $mensaje, $headers)) {
        return "1|1";
    } else {
        return "1|0";
    }
}

function socketPrn($puerto, $host, $mensaje) {
    $puerto = $puerto;
    $host = $host;
    // CREANDO EL SOCKET: (IP PROTOCOL[IPV4], TYPE SOCKET[TCP], PROTOCOL[TCP]) 
    if (!$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP)) {
        $msg = "ERROR SOCKET";
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR2: ' . $msg);
        return "0";
    }
    if (!$conexion = socket_connect($socket, $host, $puerto)) {
        $msg = "ERROR DE CONEXION";
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR2: ' . $msg);
        return "0";
    } else {
        $msg = "SE CONECTO, LEYENDO MENSAJE..";
        $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'OK: ' . $msg);
        $buffer = $mensaje;
        $salida = ' ';
        socket_write($socket, $buffer);
        while ($salida = socket_read($socket, 2048)) {
            return $salida;
            $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'MENSAJE LEIDO: ' . $salida);
        }
    }
    socket_close($socket);
}

/* SUBRED */

function saldoRecaudadora($log, $idXLog, $fecha_inicio, $fecha_fin) {
    $id = 47;
    global $log, $idXLog, $ora_conn;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, $query);
    $_SESSION['repName'] = 'Resumen Saldo de todas las recaudadoras ' . $fecha_inicio . ' - ' . $fecha_fin;
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadora', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $inicioF4 = microtime(true);
    /**
      $query="SELECT COD_BANCO,
      DES_BANCO,
      COD_RECAUDADORA,
      DES_ENTIDAD,
      --SUBSTR(FECHA,7,4),
      UPPER(DES_MONEDA) MONEDA,
      --DES_TIPO_PAGO,
      SUM(DEBITO) DEBITO,
      SUM(DESEMBOLSO) DESEMBOLSO,
      SUM(DEPOSITO) DEPOSITO,
      SUM((DESEMBOLSO + DEPOSITO) - DEBITO) AS SALDO
      --,MH
      FROM (
      --COBRO DE SERVICIOS
      SELECT 98 COD_BANCO,
      'PRONET SUB RED' DES_BANCO,
      R.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(R.FEC_PROCESAMIENTO, 'DD/MM/YYYY') FECHA,
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO,
      SUM(R.IMPORTE) DEBITO,
      0 DESEMBOLSO,
      0 AS DEPOSITO,
      'N' AS MH
      FROM  ADMIN.RESUMEN_CLEARING R,
      ADMIN.ENTIDAD          E,
      ADMIN.MONEDA           M,
      ADMIN.TIPO_PAGO        TP
      WHERE R.COD_RECAUDADORA = E.COD_ENTIDAD
      AND R.COD_MONEDA = M.COD_MONEDA
      AND R.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
      AND R.COD_EMPRESA = '01'
      AND R.COD_BANCO = 6
      AND R.FEC_PROCESAMIENTO >=
      TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
      AND R.FEC_PROCESAMIENTO <
      TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
      AND R.COD_TIPO_PRODUCTO IN (1, 10)
      --AND R.COD_MONEDA = 1
      AND R.COD_TIPO_CUENTA = 3
      AND R.TIPO_MOV = 'D'
      AND R.COD_LEYENDA_BANCO = 162
      AND TRIM(R.CUENTA) = '100791'
      AND R.COD_RECAUDADORA NOT IN
      (1151, 733, 698, 1068, 610, 634, 448, 1147, 384, 1)
      ".$recaudadoraR."
      GROUP BY R.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(R.FEC_PROCESAMIENTO, 'DD/MM/YYYY'),
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO

      UNION ALL
      --COBRANZAS DE HACIENDA
      SELECT 98,
      'PRONET SUB RED',
      P.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(P.FECHAPAGO, 'DD/MM/YYYY') AS FECHA,
      'GUARANI',
      DECODE(TRIM(P.COD_MEDIO_PAGO), 'E', 'EFECTIVO', 'CHEQUE'),
      SUM(P.IMPORTE) DEBITO,
      0 DESEMBOLSO,
      0 DEPOSITO,
      'S' AS MH
      FROM ADMIN.PAGOS_MH P, ADMIN.ENTIDAD E
      WHERE P.COD_RECAUDADORA = E.COD_ENTIDAD
      AND P.FECHAPAGO >= '".$fecha_inicio."'
      AND P.FECHAPAGO < '".$fecha_fin."'
      AND P.CHQ_VERIF = 'T'
      AND P.CHQ_CONFIRMADO = 'T'
      AND P.COD_RECAUDADORA IN
      (SELECT DISTINCT (C.COD_RECAUDADORA)
      FROM ADMIN.CONTROL_SOBREGIRO C
      WHERE C.FECHA_OPERACION >=
      TO_DATE('".$fecha_inicio."', 'DD/MM/YYYY')
      AND C.FECHA_OPERACION <
      TO_DATE('".$fecha_fin."', 'DD/MM/YYYY') + 1
      AND C.PAGOS_MH = 'S'
      AND C.TIPO_MOVIMIENTO = 'D')
      GROUP BY P.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(P.FECHAPAGO, 'DD/MM/YYYY'),
      DECODE(TRIM(P.COD_MEDIO_PAGO), 'E', 'EFECTIVO', 'CHEQUE')

      UNION ALL

      --DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)
      SELECT T.COD_BANCO,
      B.DES_BANCO,
      T.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY') AS FECHA,
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO,
      0 DEBITO,
      0 DESEMBOLSO,
      SUM(DECODE(T.TIPO_MOVIMIENTO, 'C', T.IMPORTE, 0)) DEPOSITO,
      T.PAGOS_MH AS MH
      FROM ADMIN.CONTROL_SOBREGIRO T,
      ADMIN.ENTIDAD           E,
      ADMIN.BANCO             B,
      ADMIN.MONEDA            M,
      ADMIN.TIPO_PAGO         TP,
      ADMIN.RECAUDADORA       R
      WHERE T.COD_BANCO = B.COD_BANCO
      AND T.COD_EMPRESA = E.COD_EMPRESA
      AND T.COD_RECAUDADORA = E.COD_ENTIDAD
      AND T.COD_MONEDA = M.COD_MONEDA
      AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
      AND T.COD_EMPRESA = R.COD_EMPRESA
      AND T.COD_RECAUDADORA = R.COD_RECAUDADORA
      AND T.CONCILIADO = 'S'
      AND T.FECHA_OPERACION >=
      TO_DATE('".$fecha_inicio."', 'DD/MM/YYYY')
      AND T.FECHA_OPERACION <
      TO_DATE('".$fecha_fin."', 'DD/MM/YYYY') + 1
      AND R.COD_EMPRESA = '01'
      AND T.COD_RECAUDADORA NOT IN
      (1151, 733, 698, 1068, 610, 634, 448, 1147, 384, 1)
      --AND M.COD_MONEDA = 1
      ".$recaudadoraR."
      GROUP BY T.COD_BANCO,
      B.DES_BANCO,
      T.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY'),
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO,
      T.PAGOS_MH
      UNION ALL

      --CHEQUES RECHAZADOS
      SELECT T.COD_BANCO,
      B.DES_BANCO,
      T.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY') AS FECHA,
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO,
      SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) DEBITO,
      0 DESEMBOLSO,
      0 DEPOSITO,
      'CHQ.RECHAZADO' MH
      FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
      ADMIN.BANCO                B,
      ADMIN.ENTIDAD              E,
      ADMIN.MONEDA               M,
      ADMIN.TIPO_PAGO            TP
      WHERE T.COD_BANCO = B.COD_BANCO
      AND T.COD_EMPRESA = E.COD_EMPRESA
      AND T.COD_RECAUDADORA = E.COD_ENTIDAD
      AND T.COD_MONEDA = M.COD_MONEDA
      AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
      AND T.FECHA_OPERACION >=
      TO_DATE('".$fecha_inicio."', 'DD/MM/YYYY')
      AND T.FECHA_OPERACION <
      TO_DATE('".$fecha_fin."', 'DD/MM/YYYY') + 1
      AND T.COD_EMPRESA = '01'
      AND T.COD_RECAUDADORA NOT IN (1151, 733, 698, 1068, 610, 634, 448, 1147, 384, 1)
      --AND T.COD_RECAUDADORA = 940
      --AND M.COD_MONEDA = 1
      ".$recaudadoraT."
      GROUP BY T.COD_BANCO,
      B.DES_BANCO,
      T.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(T.FECHA_OPERACION, 'DD/MM/YYYY'),
      M.DES_MONEDA,
      TP.DES_TIPO_PAGO,
      T.PAGOS_MH)
      GROUP BY COD_BANCO,
      DES_BANCO,
      COD_RECAUDADORA,
      DES_ENTIDAD,
      --SUBSTR(FECHA,7,4),
      UPPER(DES_MONEDA) --,
      --DES_TIPO_PAGO,
      --MH
      ORDER BY SALDO DESC, DES_ENTIDAD
      --SUBSTR(FECHA,7,4),
      --MH--, DES_TIPO_PAGO";
     */
    $query = "SELECT C.COD_BANCO,
				   B.DES_BANCO,
				   C.COD_RECAUDADORA,
				   E.DES_ENTIDAD,
				   UPPER(DES_MONEDA) MONEDA,
				   SUM(DEBITO) DEBITO,
				   SUM(DESEMBOLSO) DESEMBOLSO,
				   SUM(DEPOSITO) DEPOSITO,
				   SUM((DESEMBOLSO + DEPOSITO) - DEBITO) AS SALDO
			  FROM (
					--COBRO DE SERVICIOS 
					SELECT TR.COD_BANCO_RECAUDADORA COD_BANCO,
							TR.COD_RECAUDADORA,
							TRUNC(A.FECHA_CIE) FECHA,
							M.DES_MONEDA,
							TP.DES_TIPO_PAGO,
							SUM(DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0)) DEBITO,
							SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,
							0 AS DEPOSITO,
							'N' AS MH
					  FROM ADMIN.TRANSACCION     TR,
							ADMIN.MONEDA          M,
							ADMIN.TIPO_PAGO       TP,
							ADMIN.APERTURA_CIERRE A,
							ADMIN.SERVICIO        S
					 WHERE TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND TR.COD_MONEDA = M.COD_MONEDA
					   AND TR.COD_EMPRESA = A.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
					   AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
					   AND TR.NRO_CAJA = A.NRO_CAJA
					   AND TR.NRO_LOTE = A.NRO_LOTE
					   AND TR.ID_TERMINAL = A.ID_TERMINAL
					   AND TR.COD_EMPRESA = S.COD_EMPRESA
					   AND TR.COD_EMISORA = S.COD_EMISORA
					   AND TR.COD_SERVICIO = S.COD_SERVICIO
					   AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
					   AND A.FECHA_CIE >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND A.FECHA_CIE < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   AND TR.COD_BANCO_RECAUDADORA = 98
					   AND TR.COD_TIPO_TRANSACCION = 3
					   AND TR.ANULADO = 'N'
					   AND (TR.ENLINEA = 'N' OR
						   (TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
					   AND TR.COD_SERVICIO != 1123
					   AND M.COD_MONEDA = 1
					 GROUP BY TR.COD_BANCO_RECAUDADORA,
							   TR.COD_RECAUDADORA,
							   TRUNC(A.FECHA_CIE),
							   M.DES_MONEDA,
							   TP.DES_TIPO_PAGO,
							   TR.COD_SERVICIO
					UNION ALL
					--COBRANZAS DE HACIENDA
					SELECT P.COD_BANCO_RECAUDADORA COD_BANCO,
						   P.COD_RECAUDADORA,
						   TRUNC(P.FECHAPAGO) AS FECHA,
						   'GUARANI',
						   DECODE(TRIM(P.COD_MEDIO_PAGO), 'E', 'EFECTIVO', 'CHEQUE'),
						   SUM(P.IMPORTE) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   'S' AS MH
					  FROM ADMIN.PAGOS_MH P
					 WHERE P.FECHAPAGO >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND P.FECHAPAGO < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   AND P.COD_BANCO_RECAUDADORA = 98
					 GROUP BY P.COD_BANCO_RECAUDADORA,
							  P.COD_RECAUDADORA,
							  TRUNC(P.FECHAPAGO),
							  DECODE(TRIM(P.COD_MEDIO_PAGO), 'E', 'EFECTIVO', 'CHEQUE')
					UNION ALL
					--DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)      
					SELECT T.COD_BANCO,
						   T.COD_RECAUDADORA,
						   TRUNC(T.FECHA_INSERCION) AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   0 DEBITO,
						   0 DESEMBOLSO,
						   SUM(T.IMPORTE) DEPOSITO,
						   T.PAGOS_MH AS MH
					  FROM ADMIN.CONTROL_SOBREGIRO T, ADMIN.MONEDA M, ADMIN.TIPO_PAGO TP
					 WHERE T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.CONCILIADO = 'S'
					   AND T.FECHA_INSERCION >=
						   ADMIN.F_VALIDA_FECHA(TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY') + 1)
					   AND T.FECHA_INSERCION <
						   ADMIN.F_VALIDA_FECHA(TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1) +1
					   AND M.COD_MONEDA = 1
					   AND T.TIPO_MOVIMIENTO = 'C'
					 GROUP BY T.COD_BANCO,
							  T.COD_RECAUDADORA,
							  TRUNC(T.FECHA_INSERCION),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH
					UNION ALL
					--CHEQUES RECHAZADOS
					SELECT T.COD_BANCO,
						   T.COD_RECAUDADORA,
						   TRUNC(T.FECHA_OPERACION) AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   'CHQ.RECHAZADO' MH
					  FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
						   ADMIN.MONEDA               M,
						   ADMIN.TIPO_PAGO            TP
					 WHERE T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.FECHA_OPERACION >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND T.FECHA_OPERACION < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   AND T.COD_EMPRESA = '01'
					   AND M.COD_MONEDA = 1
					 GROUP BY T.COD_BANCO,
							  T.COD_RECAUDADORA,
							  TRUNC(T.FECHA_OPERACION),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH) C,
				   ADMIN.BANCO B,
				   ADMIN.ENTIDAD E
			 WHERE C.COD_BANCO = B.COD_BANCO
			   AND C.COD_RECAUDADORA = E.COD_ENTIDAD
			 GROUP BY C.COD_BANCO, DES_BANCO, COD_RECAUDADORA, DES_ENTIDAD, UPPER(DES_MONEDA)
			 ORDER BY E.DES_ENTIDAD";

    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadora', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadora', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadora', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $dec = 0;
    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" class="tablesorter">
					<thead>
						<tr>
							<th>Banco</th>
							<th>Entidad</th>
							<th>Moneda</th>
                            <th>Debito</th>
							<th>Desembolso</th>							
							<th>Deposito</th>
							<th>Deposito</th>
						</tr>
					</thead>
					';

    $imp = "<table cellspacing='1' class='tablesorter'>
			  <thead>
				<tr>
					<th>Banco</th>
					<th>Entidad</th>
					<th>Moneda</th>
					<th>Debito</th>
					<th>Desembolso</th>							
					<th>Deposito</th>
					<th>Saldo</th>
				</tr>
			</thead>";
    $adic = '<tbody>';
    $SR_debito = 0;
    $SR_desembolso = 0;
    $SR_deposito = 0;
    $SR_saldo = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {

        $SR_debito += $fila['DEBITO'];
        $SR_desembolso += $fila['DESEMBOLSO'];
        $SR_deposito += $fila['DEPOSITO'];
        $SR_saldo += $fila['SALDO'];

        $adic .= "<tr>";
        $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
        $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
        $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
        $adic .= "  <td>" . $fila['DEBITO'] . "</td>";
        $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
        $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
        $adic .= "  <td>" . $fila['SALDO'] . "</td>";
        $adic .= "</tr>";
    }

    $adic .= "</tbody><tr>";
    $adic .= "<th width='100px' align='left' colspan='3'><strong>Total</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($SR_debito, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($SR_desembolso, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($SR_deposito, $dec) . "</strong></th>";
    $adic .= "<th width='100px' align='right'><strong>" . numberFormater($SR_saldo, $dec) . "</strong></th>";
    $adic .= "</tr>";

    $_SESSION['datosImprimir'] = $imp . $adic . "</table>";
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadora', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function saldoRecaudadoraDetalle($log, $idXLog, $fecha_inicio, $fecha_fin, $rec) {
    $id = 48;
    global $log, $idXLog, $ora_conn;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, $query);

    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraDetalle', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    if (trim($_POST['rec']) == '*') {
        $_SESSION['repName'] = 'Resumen Saldo de todas las recaudadoras ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = " ";
        $recaudadora2 = " ";
        $recaudadora3 = " ";
    } else {
        $_SESSION['repName'] = 'Resumen Saldo de la recaudadora ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadora = " and tr.cod_empresa='01' and tr.cod_recaudadora = " . $rec . " ";
        $recaudadora2 = " and p.cod_empresa='01' and p.cod_recaudadora = " . $rec . " ";
        $recaudadora3 = " and t.cod_empresa='01' and t.cod_recaudadora = " . $rec . " ";
    }
    $inicioF4 = microtime(true);

    $query = "SELECT C.COD_BANCO,
				   B.DES_BANCO,
				   C.COD_RECAUDADORA,
				   E.DES_ENTIDAD,
				   TO_CHAR(FECHA, 'DD/MM/YYYY') MONEDA,
				   UPPER(DES_MONEDA) FECHA,
				   DES_TIPO_PAGO,
				   SUM(DEBITO) DEBITO,
				   SUM(DESEMBOLSO) DESEMBOLSO,
				   SUM(DEPOSITO) DEPOSITO,
				   SUM((DESEMBOLSO + DEPOSITO) - DEBITO) AS SALDO,
				   MH,
				   Observacion
			  FROM (
					--COBRO DE SERVICIOS 
					SELECT NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA) COD_BANCO,
							TR.COD_RECAUDADORA,
							TRUNC(A.fecha_ape) FECHA,
							M.DES_MONEDA,
							TP.DES_TIPO_PAGO,
							SUM(DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0)) DEBITO,
							SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,
							0 AS DEPOSITO,
							'N' AS MH,
							case
							  when nvl(TO_CHAR(A.FECHA_CIE, 'DD'), 'N') = 'N' then
							   'Pendiente de Cierre'
							  else
							   ''
							end As Observacion
					  FROM ADMIN.TRANSACCION            TR,
							ADMIN.MONEDA                 M,
							ADMIN.TIPO_PAGO              TP,
							ADMIN.APERTURA_CIERRE        A,
							ADMIN.SERVICIO               S,
							ADMIN.BANCO_DEPOSITO_SUB_RED BD
					 WHERE TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND TR.COD_MONEDA = M.COD_MONEDA
					   AND TR.COD_EMPRESA = A.COD_EMPRESA
					   AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
					   AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
					   AND TR.NRO_CAJA = A.NRO_CAJA
					   AND TR.NRO_LOTE = A.NRO_LOTE
					   AND TR.ID_TERMINAL = A.ID_TERMINAL
					   AND TR.COD_EMPRESA = S.COD_EMPRESA
					   AND TR.COD_EMISORA = S.COD_EMISORA
					   AND TR.COD_SERVICIO = S.COD_SERVICIO
					   AND TR.COD_EMPRESA = BD.COD_EMPRESA(+)
					   AND TR.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
					   AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
					   AND a.fecha_ape >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND a.fecha_ape < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   AND TR.COD_BANCO_RECAUDADORA = 98
					   AND TR.COD_TIPO_TRANSACCION = 3
					   AND TR.ANULADO = 'N'
					   AND (TR.ENLINEA = 'N' OR
						   (TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
					   AND TR.COD_SERVICIO != 1123
					   AND M.COD_MONEDA = 1
					   " . $recaudadora . "
					 GROUP BY NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA),
							   TR.COD_RECAUDADORA,
							   TRUNC(A.Fecha_Ape),
							   M.DES_MONEDA,
							   TP.DES_TIPO_PAGO,
							   TR.COD_SERVICIO,
							   A.FECHA_CIE
					UNION ALL
					--COBRANZAS DE HACIENDA
					select NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA) COD_BANCO,
						   P.COD_RECAUDADORA,
						   TRUNC(p.fechapago) AS FECHA,
						   'GUARANI',
						   decode(trim(p.cod_medio_pago), 'E', 'Efectivo', 'Cheque'),
						   sum(p.importe) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   'S' AS MH,
						   '' As Observacion
					  from admin.pagos_mh p, ADMIN.BANCO_DEPOSITO_SUB_RED BD
					 where p.cod_empresa = bd.cod_empresa(+)
					   and p.cod_recaudadora = bd.cod_recaudadora(+)
					   and p.fechapago >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   and p.fechapago < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   and p.cod_banco_recaudadora = 98
					   " . $recaudadora2 . "
					 group by NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA),
							  P.COD_RECAUDADORA,
							  TRUNC(p.fechapago),
							  decode(trim(p.cod_medio_pago), 'E', 'Efectivo', 'Cheque')
					UNION ALL
					--DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)      
					SELECT T.COD_BANCO,
						   T.COD_RECAUDADORA,
						   TRUNC(T.Fecha_Insercion) AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   0 DEBITO,
						   0 DESEMBOLSO,
						   SUM(T.IMPORTE) DEPOSITO,
						   T.PAGOS_MH AS MH,
						   '' As Observacion
					  FROM ADMIN.CONTROL_SOBREGIRO T, ADMIN.MONEDA M, ADMIN.TIPO_PAGO TP
					 WHERE T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.CONCILIADO = 'S'
					   AND T.Fecha_Insercion >=
						   admin.f_valida_fecha(TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY') + 1)
					   AND T.Fecha_Insercion <
						   admin.f_valida_fecha(TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1) + 1
					   AND M.COD_MONEDA = 1
					   AND T.TIPO_MOVIMIENTO = 'C'
					   " . $recaudadora3 . "
					 GROUP BY T.COD_BANCO,
							  T.COD_RECAUDADORA,
							  TRUNC(T.Fecha_Insercion),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH
					UNION ALL
					--CHEQUES RECHAZADOS
					SELECT T.COD_BANCO,
						   T.COD_RECAUDADORA,
						   TRUNC(T.FECHA_OPERACION) AS FECHA,
						   M.DES_MONEDA,
						   TP.DES_TIPO_PAGO,
						   SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) DEBITO,
						   0 DESEMBOLSO,
						   0 DEPOSITO,
						   'CHQ.RECHAZADO' MH,
						   '' As Observacion
					  FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
						   ADMIN.MONEDA               M,
						   ADMIN.TIPO_PAGO            TP
					 WHERE T.COD_MONEDA = M.COD_MONEDA
					   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
					   AND T.FECHA_OPERACION >= TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
					   AND T.FECHA_OPERACION < TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
					   and t.cod_empresa = '01'
					   AND M.COD_MONEDA = 1
					   " . $recaudadora3 . "
					 GROUP BY T.COD_BANCO,
							  T.COD_RECAUDADORA,
							  TRUNC(T.FECHA_OPERACION),
							  M.DES_MONEDA,
							  TP.DES_TIPO_PAGO,
							  T.PAGOS_MH) C,
				   ADMIN.BANCO B,
				   ADMIN.ENTIDAD E
			 WHERE C.COD_BANCO = B.COD_BANCO
			   AND C.COD_RECAUDADORA = E.COD_ENTIDAD
			 GROUP BY C.COD_BANCO,
					  DES_BANCO,
					  COD_RECAUDADORA,
					  DES_ENTIDAD,
					  FECHA,
					  UPPER(DES_MONEDA),
					  DES_TIPO_PAGO,
					  MH,
					  Observacion
			 ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA, DES_TIPO_PAGO";

    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadoraDetalle', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraDetalle', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraDetalle', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $dec = 0;
    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" class="tablesorter">
					<thead>
						<tr>
							<th>Banco</th>
							<th>Entidad</th>
							<th>Moneda</th>
							<th>Fecha</th>
							<th>Tipo Pago</th>
                            <th>Debito</th>
							<th>Desembolso</th>							
							<th>Deposito</th>
							<th>Saldo</th>
							<th>MH</th>
							<th>Observacion</th>
						</tr>
					</thead>
					';

    $imp = "<table cellspacing='1' class='tablesorter'>
			  <thead>
				<tr>
					<th>Banco</th>
					<th>Entidad</th>
					<th>Moneda</th>
					<th>Fecha</th>
					<th>Tipo Pago</th>
					<th>Debito</th>
					<th>Desembolso</th>							
					<th>Deposito</th>
					<th>Saldo</th>
					<th>MH</th>
					<th>Observacion</th>
				</tr>
			</thead>";
    $adic = '<tbody>';
    $SR_debito = 0;
    $SR_desembolso = 0;
    $SR_deposito = 0;
    $SR_saldo = 0;
    $comparacion = "";
    $contador = 0;
    $entidadDesc = "";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contador++;

        if ($contador == 1) {
            $entidadDesc = strtoupper($fila['DES_BANCO'] . " - " . $fila['MONEDA'] . " - " . $fila['DES_TIPO_PAGO']);

            $adic .= "<tr>";
            $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
            $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
            $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
            $adic .= "  <td>" . $fila['FECHA'] . "</td>";
            $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
            $adic .= "  <td>" . $fila['DEBITO'] . "</td>";
            $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
            $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
            $adic .= "  <td>" . $fila['SALDO'] . "</td>";
            $adic .= "  <td>" . $fila['MH'] . "</td>";
            $adic .= "  <td>" . $fila['OBSERVACION'] . "</td>";
            $adic .= "</tr>";

            $SR_debito += $fila['DEBITO'];
            $SR_desembolso += $fila['DESEMBOLSO'];
            $SR_deposito += $fila['DEPOSITO'];
            $SR_saldo += $fila['SALDO'];
        } else {
            if (strtoupper($comparacion) != strtoupper($fila['COMPARACION'])) {

                $adic .= "<tr>";
                $adic .= " <th width='100px' align='left' colspan='5'><strong>TOTAL " . $entidadDesc . "</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_debito, $dec) . "</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_desembolso, $dec) . "</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_deposito, $dec) . "</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_saldo, $dec) . "</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>&nbsp;</strong></th>";
                $adic .= " <th width='100px' align='right'><strong>&nbsp;</strong></th>";
                $adic .= "</tr>";

                $SR_debito = 0;
                $SR_desembolso = 0;
                $SR_deposito = 0;
                $SR_saldo = 0;

                $SR_debito += $fila['DEBITO'];
                $SR_desembolso += $fila['DESEMBOLSO'];
                $SR_deposito += $fila['DEPOSITO'];
                $SR_saldo += $fila['SALDO'];

                $adic .= "<tr>";
                $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
                $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
                $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
                $adic .= "  <td>" . $fila['FECHA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
                $adic .= "  <td>" . $fila['DEBITO'] . "</td>";
                $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
                $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO'] . "</td>";
                $adic .= "  <td>" . $fila['MH'] . "</td>";
                $adic .= "  <td>" . $fila['OBSERVACION'] . "</td>";
                $adic .= "</tr>";
            } else {
                $entidadDesc = strtoupper($fila['DES_BANCO'] . " - " . $fila['MONEDA'] . " - " . $fila['DES_TIPO_PAGO']);

                $adic .= "<tr>";
                $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
                $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
                $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
                $adic .= "  <td>" . $fila['FECHA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
                $adic .= "  <td>" . $fila['DEBITO'] . "</td>";
                $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
                $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO'] . "</td>";
                $adic .= "  <td>" . $fila['MH'] . "</td>";
                $adic .= "  <td>" . $fila['OBSERVACION'] . "</td>";
                $adic .= "</tr>";

                $SR_debito += $fila['DEBITO'];
                $SR_desembolso += $fila['DESEMBOLSO'];
                $SR_deposito += $fila['DEPOSITO'];
                $SR_saldo += $fila['SALDO'];
            }
        }

        $comparacion = strtoupper($fila['COMPARACION']);
        $entidadDesc = strtoupper($fila['DES_BANCO'] . " - " . $fila['MONEDA'] . " - " . $fila['DES_TIPO_PAGO']);
    }

    $adic .= "<tr>";
    $adic .= " <th width='100px' align='left' colspan='5'><strong>TOTAL " . $entidadDesc . "</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_debito, $dec) . "</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_desembolso, $dec) . "</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_deposito, $dec) . "</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>" . numberFormater($SR_saldo, $dec) . "</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>&nbsp;</strong></th>";
    $adic .= " <th width='100px' align='right'><strong>&nbsp;</strong></th>";
    $adic .= "</tr>";

    $_SESSION['datosImprimir'] = $imp . $adic . "</table>";
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadoraDetalle', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function saldoRecaudadoraTipoServicio($log, $idXLog, $fecha_inicio, $fecha_fin, $rec) {
    $id = 49;
    global $log, $idXLog, $ora_conn;
    $log->write($idXLog, 0, getcwd() . ' ' . __FUNCTION__, $query);

    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';
    $msg = 'No se obtuvieron resultados para su consulta';
    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraTipoServicio', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    if (trim($_POST['rec']) == '*') {
        $_SESSION['repName'] = 'Resumen Saldo de todas las recaudadoras ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadoraTR = " ";
        $recaudadoraP = " ";
        $recaudadoraT = " ";
    } else {
        $_SESSION['repName'] = 'Resumen Saldo de la recaudadora ' . $fecha_inicio . ' ' . $fecha_fin;
        $recaudadoraTR = " and tr.cod_empresa='01' and tr.cod_recaudadora = " . $rec . " ";
        $recaudadoraP = " and p.cod_empresa='01' and p.cod_recaudadora = " . $rec . " ";
        $recaudadoraT = " and t.cod_empresa='01' and t.cod_recaudadora = " . $rec . " ";
    }
    $inicioF4 = microtime(true);

    $query = "SELECT C.COD_BANCO,
				   B.DES_BANCO,
				   C.COD_RECAUDADORA,
				   E.DES_ENTIDAD,
				   TO_CHAR(FECHA, 'DD/MM/YYYY') FECHA,
				   UPPER(DES_MONEDA) MONEDA,
				   DES_TIPO_PAGO,
				   SUM(SERVICIOS) SERVICIOS,
				   SUM(ENVIOS) ENVIOS,
				   SUM(COBRANZAS_MH) COBRANZAS_MH,
				   SUM(CHQ_RECHAZADO) CHQ_RECHAZADO,
				   SUM(DESEMBOLSO) DESEMBOLSO,
				   SUM(DEPOSITO) DEPOSITO,
				   
				   case
					 when SUM(TOTAL) > 0 then
					  0
					 else
					  SUM((DESEMBOLSO + DEPOSITO) -
						  (SERVICIOS + ENVIOS + COBRANZAS_MH + CHQ_RECHAZADO))
				   end SALDO,
				   
				   case
					 when SUM(TOTAL) > 0 then
					  SUM((DESEMBOLSO) - (SERVICIOS + ENVIOS))
					 else
					  0
				   end SALDO_REEMBOLSO
			
			  FROM (SELECT COD_BANCO,
						   COD_RECAUDADORA,
						   FECHA,
						   DES_MONEDA,
						   DES_TIPO_PAGO,
						   SERVICIOS,
						   ENVIOS,
						   COBRANZAS_MH,
						   CHQ_RECHAZADO,
						   DESEMBOLSO,
						   DEPOSITO,
						   MH,
						   SUM((DESEMBOLSO) -
							   (SERVICIOS + ENVIOS + COBRANZAS_MH + CHQ_RECHAZADO)) OVER(Partition By COD_RECAUDADORA, FECHA, DES_MONEDA, DES_TIPO_PAGO) as TOTAL
					  FROM ( --COBRO DE SERVICIOS 
							SELECT NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA) COD_BANCO,
									TR.COD_RECAUDADORA,
									TRUNC(A.FECHA_cie) FECHA,
									M.DES_MONEDA,
									TP.DES_TIPO_PAGO,
									SUM(DECODE(S.ID_RUBRO_SERVICIO,
											   23,
											   0,
											   DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0))) SERVICIOS,
									SUM(DECODE(S.ID_RUBRO_SERVICIO,
											   23,
											   DECODE(S.TIPO_MOV, 'C', TR.IMPORTE, 0),
											   0)) ENVIOS,
									0 COBRANZAS_MH,
									0 CHQ_RECHAZADO,
									SUM(DECODE(S.TIPO_MOV, 'D', TR.IMPORTE, 0)) DESEMBOLSO,
									0 AS DEPOSITO,
									'N' AS MH
							  FROM ADMIN.TRANSACCION            TR,
									ADMIN.MONEDA                 M,
									ADMIN.TIPO_PAGO              TP,
									ADMIN.APERTURA_CIERRE        A,
									ADMIN.SERVICIO               S,
									ADMIN.BANCO_DEPOSITO_SUB_RED BD
							 WHERE TR.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
							   AND TR.COD_MONEDA = M.COD_MONEDA
							   AND TR.COD_EMPRESA = A.COD_EMPRESA
							   AND TR.COD_RECAUDADORA = A.COD_RECAUDADORA
							   AND TR.COD_SUCURSAL_REC = A.COD_SUCURSAL_REC
							   AND TR.NRO_CAJA = A.NRO_CAJA
							   AND TR.NRO_LOTE = A.NRO_LOTE
							   AND TR.ID_TERMINAL = A.ID_TERMINAL
							   AND TR.COD_EMPRESA = S.COD_EMPRESA
							   AND TR.COD_EMISORA = S.COD_EMISORA
							   AND TR.COD_SERVICIO = S.COD_SERVICIO
							   AND TR.COD_EMPRESA = BD.COD_EMPRESA(+)
							   AND TR.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
							   AND TR.COD_EMISORA <> TR.COD_RECAUDADORA -- EXCLUIR COBROS PROPIOS
							   AND a.fecha_cie >=
								   TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
							   AND a.fecha_cie <
								   TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
							   AND TR.COD_BANCO_RECAUDADORA IN (97, 98)
							   AND TR.COD_TIPO_TRANSACCION = 3
							   AND TR.ANULADO = 'N'
							   AND (TR.ENLINEA = 'N' OR
								   (TR.ENLINEA = 'S' AND TR.COD_ESTADO = 'A'))
							 " . $recaudadoraTR . "
							   AND TR.COD_SERVICIO != 1123
							   AND M.COD_MONEDA = 1
							 GROUP BY NVL(BD.COD_BANCO, TR.COD_BANCO_RECAUDADORA),
									   TR.COD_RECAUDADORA,
									   TRUNC(A.FECHA_cie),
									   M.DES_MONEDA,
									   TP.DES_TIPO_PAGO,
									   TR.COD_SERVICIO
							UNION ALL
							--COBRANZAS DE HACIENDA
							select NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA) COD_BANCO,
								   P.COD_RECAUDADORA,
								   TRUNC(p.fechapago) AS FECHA,
								   'GUARANI',
								   decode(trim(p.cod_medio_pago),
										  'E',
										  'Efectivo',
										  'Cheque'),
								   0 SERVICIOS,
								   0 ENVIOS,
								   sum(p.importe) COBRANZAS_MH,
								   0 CHQ_RECHAZADO,
								   0 DESEMBOLSO,
								   0 DEPOSITO,
								   'S' AS MH
							  from admin.pagos_mh p, ADMIN.BANCO_DEPOSITO_SUB_RED BD
							 where p.cod_empresa = bd.cod_empresa(+)
							   and p.cod_recaudadora = bd.cod_recaudadora(+)
							   and p.fechapago >=
								   TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
							   and p.fechapago <
								   TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
							   and p.cod_banco_recaudadora = 98
							 group by NVL(BD.COD_BANCO, p.COD_BANCO_RECAUDADORA),
									  P.COD_RECAUDADORA,
									  TRUNC(p.fechapago),
									  decode(trim(p.cod_medio_pago),
											 'E',
											 'Efectivo',
											 'Cheque')
							UNION ALL
							--DEPOSITOS CORRESPONSALIA (POR HACIENDA Y SERVICIOS)      
							SELECT T.COD_BANCO,
								   T.COD_RECAUDADORA,
								   TRUNC(T.Fecha_Insercion) AS FECHA,
								   M.DES_MONEDA,
								   TP.DES_TIPO_PAGO,
								   0 SERVICIOS,
								   0 ENVIOS,
								   0 COBRANZAS_MH,
								   0 CHQ_RECHAZADO,
								   0 DESEMBOLSO,
								   SUM(T.IMPORTE) DEPOSITO,
								   T.PAGOS_MH AS MH
							  FROM ADMIN.CONTROL_SOBREGIRO T,
								   ADMIN.MONEDA            M,
								   ADMIN.TIPO_PAGO         TP
							 WHERE T.COD_MONEDA = M.COD_MONEDA
							   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
							   AND T.CONCILIADO = 'S'
							   AND T.Fecha_Insercion >=
								   admin.f_valida_fecha(TO_DATE('" . $fecha_inicio . "',
																'DD/MM/YYYY') + 1)
							   AND T.Fecha_Insercion <
								   admin.f_valida_fecha(TO_DATE('" . $fecha_fin . "',
																'DD/MM/YYYY') + 1) + 1
							   AND M.COD_MONEDA = 1
							   AND T.TIPO_MOVIMIENTO = 'C'
							   HAVING SUM(T.IMPORTE) > 0
							 GROUP BY T.COD_BANCO,
									  T.COD_RECAUDADORA,
									  TRUNC(T.Fecha_Insercion),
									  M.DES_MONEDA,
									  TP.DES_TIPO_PAGO,
									  T.PAGOS_MH
							UNION ALL
							--DEPOSITOS DE REMESAS
							SELECT BR.COD_BANCO,
								   BR.COD_RECAUDADORA,
								   TRUNC(D.FECHA_HORA_DEPOSITO) AS FECHA,
								   M.DES_MONEDA,
								   TP.DES_TIPO_PAGO,
								   0 SERVICIOS,
								   0 ENVIOS,
								   0 COBRANZAS_MH,
								   0 CHQ_RECHAZADO,
								   0 DESEMBOLSO,
								   SUM(D.IMPORTE) DEPOSITO,
								   'N' AS MH
							  FROM ADMIN.DEPOSITOS_CLEARING D,
								   ADMIN.BANCO_RECAUDADORA  BR,
								   ADMIN.MONEDA             M,
								   ADMIN.TIPO_PAGO          TP
							 WHERE D.NRO_CUENTA = BR.NRO_CUENTA_DEBITO
							   AND D.COD_MONEDA = M.COD_MONEDA
							   AND D.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
							   AND BR.COD_BANCO = 97
							   AND BR.COD_MONEDA = 1
							   AND D.ANULADO = 'N'
							   AND D.FECHA_HORA_DEPOSITO >=
								   ADMIN.F_VALIDA_FECHA(TO_DATE('" . $fecha_inicio . "',
																'DD/MM/YYYY') + 1)
							   AND D.FECHA_HORA_DEPOSITO <
								   ADMIN.F_VALIDA_FECHA(TO_DATE('" . $fecha_fin . "',
																'DD/MM/YYYY') + 1) + 1
							 HAVING SUM(D.IMPORTE) > 0
							 GROUP BY BR.COD_BANCO,
									  BR.COD_RECAUDADORA,
									  TRUNC(D.FECHA_HORA_DEPOSITO),
									  M.DES_MONEDA,
									  TP.DES_TIPO_PAGO
							UNION ALL
							--CHEQUES RECHAZADOS
							SELECT T.COD_BANCO,
								   T.COD_RECAUDADORA,
								   TRUNC(T.FECHA_OPERACION) AS FECHA,
								   M.DES_MONEDA,
								   TP.DES_TIPO_PAGO,
								   0 SERVICIOS,
								   0 ENVIOS,
								   0 COBRANZAS_MH,
								   SUM(DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0)) CHQ_RECHAZADO,
								   0 DESEMBOLSO,
								   0 DEPOSITO,
								   'N' MH
							  FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
								   ADMIN.MONEDA               M,
								   ADMIN.TIPO_PAGO            TP
							 WHERE T.COD_MONEDA = M.COD_MONEDA
							   AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
							   AND T.FECHA_OPERACION >=
								   TO_DATE('" . $fecha_inicio . "', 'DD/MM/YYYY')
							   AND T.FECHA_OPERACION <
								   TO_DATE('" . $fecha_fin . "', 'DD/MM/YYYY') + 1
							 " . $recaudadoraT . "
							   AND M.COD_MONEDA = 1
							 GROUP BY T.COD_BANCO,
									  T.COD_RECAUDADORA,
									  TRUNC(T.FECHA_OPERACION),
									  M.DES_MONEDA,
									  TP.DES_TIPO_PAGO,
									  T.PAGOS_MH)) C,
				   ADMIN.BANCO B,
				   ADMIN.ENTIDAD E
			 WHERE C.COD_BANCO = B.COD_BANCO
			   AND C.COD_RECAUDADORA = E.COD_ENTIDAD
			 GROUP BY C.COD_BANCO,
					  DES_BANCO,
					  COD_RECAUDADORA,
					  DES_ENTIDAD,
					  FECHA,
					  UPPER(DES_MONEDA),
					  DES_TIPO_PAGO
			 ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA, DES_TIPO_PAGO";

    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadoraTipoServicio', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraTipoServicio', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/saldoRecaudadoraTipoServicio', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $dec = 0;
    $html = '<br>
			<div id="rep" align="center">
				<ul><li><b>' . $_SESSION['repName'] . '</b></li></ul>
				<table border="0">
				<tr>
				<td><a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" alt="IMPRIMIR" border="0"></a></td>
				<td><a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" alt="EXCEL" border="0"></a></td>				
				<td><a href="ticket/pdf.php" target="_blank"><img src="img/pdf.jpg" width="40" height="41" alt="PDF" border="0" /></a></td>								
				</tr>
				</table>
				<table cellspacing="1" class="tablesorter">
					<thead>
						<tr>
							<th>Banco</th>
							<th>Fecha</th>
							<th>Entidad</th>
							<th>Moneda</th>
							<th>Tipo Pago</th>
							<th>Servicios</th>
							<th>Envios</th>
							<th>Cobranzas MH</th>
							<th>Cheque Rechazado</th>
							<th>Desembolso</th>				
							<th>Deposito</th>
							<th>Saldo</th>
							<th>Saldo Reembolso</th>
						</tr>
					</thead>
					';

    $imp = "<table>
			  <thead>
				<tr>
					<th>Banco</th>
					<th>Fecha</th>
					<th>Entidad</th>
					<th>Moneda</th>
					<th>Tipo Pago</th>
					<th>Servicios</th>
					<th>Envios</th>
					<th>Cobranzas MH</th>
					<th>Cheque Rechazado</th>
					<th>Desembolso</th>				
					<th>Deposito</th>
					<th>Saldo</th>
					<th>Saldo Reembolso</th>
				</tr>
			</thead>";
    $adic = '<tbody>';

    $SR_servicio = 0;
    $SR_envios = 0;
    $SR_cobranza_mh = 0;
    $SR_cheque = 0;
    $SR_desembolso = 0;
    $SR_deposito = 0;
    $SR_saldo = 0;
    $SR_saldo_reembolso = 0;

    $contador = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $contador++;

        if ($contador == 1) {
            $laFecha = $fila['FECHA'];
            $adic .= "<tr>";
            $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
            $adic .= "  <td>" . $fila['FECHA'] . "</td>";
            $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
            $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
            $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
            $adic .= "  <td>" . $fila['SERVICIOS'] . "</td>";
            $adic .= "  <td>" . $fila['ENVIOS'] . "</td>";
            $adic .= "  <td>" . $fila['COBRANZAS_MH'] . "</td>";
            $adic .= "  <td>" . $fila['CHQ_RECHAZADO'] . "</td>";
            $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
            $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
            $adic .= "  <td>" . $fila['SALDO'] . "</td>";
            $adic .= "  <td>" . $fila['SALDO_REEMBOLSO'] . "</td>";
            $adic .= "</tr>";

            $SR_servicio += $fila['SERVICIOS'];
            $SR_envios += $fila['ENVIOS'];
            $SR_cobranza_mh += $fila['COBRANZAS_MH'];
            $SR_cheque += $fila['CHQ_RECHAZADO'];
            $SR_desembolso += $fila['DESEMBOLSO'];
            $SR_deposito += $fila['DEPOSITO'];
            $SR_saldo += $fila['SALDO'];
            $SR_saldo_reembolso += $fila['SALDO_REEMBOLSO'];
        } else {
            if ($comparacion != $fila['FECHA']) {
                $adic .= "</tbody><tr>";
                $adic .= "<th align='left' colspan='5'><strong>Total " . $laFecha . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_servicio, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_envios, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_cobranza_mh, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_cheque, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_desembolso, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_deposito, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_saldo, $dec) . "</strong></th>";
                $adic .= "<th align='right'><strong>" . numberFormater($SR_saldo_reembolso, $dec) . "</strong></th>";
                $adic .= "</tr>";

                $SR_servicio = 0;
                $SR_envios = 0;
                $SR_cobranza_mh = 0;
                $SR_cheque = 0;
                $SR_desembolso = 0;
                $SR_deposito = 0;
                $SR_saldo = 0;
                $SR_saldo_reembolso = 0;

                $SR_servicio += $fila['SERVICIOS'];
                $SR_envios += $fila['ENVIOS'];
                $SR_cobranza_mh += $fila['COBRANZAS_MH'];
                $SR_cheque += $fila['CHQ_RECHAZADO'];
                $SR_desembolso += $fila['DESEMBOLSO'];
                $SR_deposito += $fila['DEPOSITO'];
                $SR_saldo += $fila['SALDO'];
                $SR_saldo_reembolso += $fila['SALDO_REEMBOLSO'];

                $adic .= "<tr>";
                $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
                $adic .= "  <td>" . $fila['FECHA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
                $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
                $adic .= "  <td>" . $fila['SERVICIOS'] . "</td>";
                $adic .= "  <td>" . $fila['ENVIOS'] . "</td>";
                $adic .= "  <td>" . $fila['COBRANZAS_MH'] . "</td>";
                $adic .= "  <td>" . $fila['CHQ_RECHAZADO'] . "</td>";
                $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
                $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO_REEMBOLSO'] . "</td>";
                $adic .= "</tr>";
            } else {
                $laFecha = $fila['FECHA'];
                $adic .= "<tr>";
                $adic .= "  <td>" . $fila['DES_BANCO'] . "</td>";
                $adic .= "  <td>" . $fila['FECHA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_ENTIDAD'] . "</td>";
                $adic .= "  <td>" . $fila['MONEDA'] . "</td>";
                $adic .= "  <td>" . $fila['DES_TIPO_PAGO'] . "</td>";
                $adic .= "  <td>" . $fila['SERVICIOS'] . "</td>";
                $adic .= "  <td>" . $fila['ENVIOS'] . "</td>";
                $adic .= "  <td>" . $fila['COBRANZAS_MH'] . "</td>";
                $adic .= "  <td>" . $fila['CHQ_RECHAZADO'] . "</td>";
                $adic .= "  <td>" . $fila['DESEMBOLSO'] . "</td>";
                $adic .= "  <td>" . $fila['DEPOSITO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO'] . "</td>";
                $adic .= "  <td>" . $fila['SALDO_REEMBOLSO'] . "</td>";
                $adic .= "</tr>";

                $SR_servicio += $fila['SERVICIOS'];
                $SR_envios += $fila['ENVIOS'];
                $SR_cobranza_mh += $fila['COBRANZAS_MH'];
                $SR_cheque += $fila['CHQ_RECHAZADO'];
                $SR_desembolso += $fila['DESEMBOLSO'];
                $SR_deposito += $fila['DEPOSITO'];
                $SR_saldo += $fila['SALDO'];
                $SR_saldo_reembolso += $fila['SALDO_REEMBOLSO'];
            }
        }
        $comparacion = $fila['FECHA'];
        $laFecha = $fila['FECHA'];
    }

    $adic .= "<tr>";
    $adic .= "<th colspan=5><strong>Total " . $laFecha . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_servicio, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_envios, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_cobranza_mh, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_cheque, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_desembolso, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_deposito, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_saldo, $dec) . "</strong></th>";
    $adic .= "<th ><strong>" . numberFormater($SR_saldo_reembolso, $dec) . "</strong></th>";
    $adic .= "</tr>";

    $_SESSION['datosImprimir'] = $imp . $adic . "</tbody></table>";
    $adic = $html . $adic . '</table>';
    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;

    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/saldoRecaudadoraTipoServicio', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku, se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function saldoConsolidado($log, $idXLog, $txtFechaDesde, $txtFechaHasta, $rec, $moneda) {
    include("db.inc.php");
    // $_SESSION['repName'] = 'ListadoRemesadora ' . $remesadora;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    if ($rec != "*") {
        $laRecaudadora_1 = "AND C.COD_RECAUDADORA =" . $rec;
        $laRecaudadora_2 = "AND T.COD_RECAUDADORA =" . $rec;
    } else {
        $laRecaudadora_1 = "";
        $laRecaudadora_2 = "";
    }

    /* $query="SELECT C.COD_BANCO,
      B.DES_BANCO,
      C.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      --   TO_CHAR(FECHA, 'DD/MM/YYYY'),
      FECHA ,
      UPPER(DES_MONEDA),
      DEBITOS,
      DEPOSITOS,
      DEPOSITOS - DEBITOS DIFERENCIA,
      SALDO,
      DIAS_SOBREGIRO
      FROM (
      --MOVIMIENTOS DE DEPOSITO Y COBRANZAS
      SELECT NVL(BD.COD_BANCO, C.COD_BANCO) COD_BANCO,
      C.COD_RECAUDADORA,
      TRUNC(C.FECHA_INSERCION) FECHA,
      M.DES_MONEDA,
      DECODE(C.TIPO_MOVIMIENTO, 'D', C.IMPORTE, 0) DEBITOS,
      DECODE(C.TIPO_MOVIMIENTO, 'C', C.IMPORTE, 0) DEPOSITOS,
      C.SALDO_ACTUAL SALDO,
      C.DIAS_SOBREGIRO
      FROM ADMIN.CONTROL_SOBREGIRO C,
      ADMIN.MONEDA          M,
      ADMIN.BANCO_DEPOSITO_SUB_RED BD
      WHERE C.COD_MONEDA = M.COD_MONEDA
      AND C.COD_EMPRESA = BD.COD_EMPRESA(+)
      AND C.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
      AND C.FECHA_INSERCION >= TO_DATE('".$txtFechaDesde."', 'DD/MM/YYYY')
      AND C.FECHA_INSERCION < TO_DATE('".$txtFechaHasta."', 'DD/MM/YYYY') + 1
      AND C.CONCILIADO = 'S'
      ".$laRecaudadora_1."
      AND C.COD_MONEDA = ".$moneda."
      UNION ALL
      --CHEQUES RECHAZADOS
      SELECT T.COD_BANCO,
      T.COD_RECAUDADORA,
      TRUNC(T.FECHA_OPERACION) AS FECHA,
      M.DES_MONEDA,
      DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0) DEBITOS,
      0 DEPOSITOS,
      0 SALDO,
      0 DIAS_SOBREGIRO
      FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
      ADMIN.MONEDA               M,
      ADMIN.TIPO_PAGO            TP
      WHERE T.COD_MONEDA = M.COD_MONEDA
      AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
      AND T.FECHA_OPERACION >= TO_DATE('".$txtFechaDesde."', 'DD/MM/YYYY')
      AND T.FECHA_OPERACION < TO_DATE('".$txtFechaHasta."', 'DD/MM/YYYY') + 1
      and t.cod_empresa = '01'
      ".$laRecaudadora_2."
      AND M.COD_MONEDA = ".$moneda."  ) C,   ADMIN.BANCO B, ADMIN.ENTIDAD E
      WHERE C.COD_BANCO = B.COD_BANCO
      AND C.COD_RECAUDADORA = E.COD_ENTIDAD
      ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA,DIAS_SOBREGIRO desc "; */
    /* $query="SELECT C.COD_BANCO,
      B.DES_BANCO,
      C.COD_RECAUDADORA,
      E.DES_ENTIDAD,
      TO_CHAR(FECHA, 'DD/MM/YYYY') FECHA,
      UPPER(DES_MONEDA) MONEDA,
      DEBITOS,
      '0' DESEMBOLSO,
      DEPOSITOS,
      DEPOSITOS - DEBITOS DIFERENCIA,
      SALDO
      FROM (
      --MOVIMIENTOS DE DEPOSITO Y COBRANZAS
      SELECT NVL(BD.COD_BANCO, C.COD_BANCO) COD_BANCO,
      C.COD_RECAUDADORA,
      TRUNC(C.FECHA_INSERCION) FECHA,
      M.DES_MONEDA,
      DECODE(C.TIPO_MOVIMIENTO, 'D', C.IMPORTE, 0) DEBITOS,
      DECODE(C.TIPO_MOVIMIENTO, 'C', C.IMPORTE, 0) DEPOSITOS,
      C.SALDO_ACTUAL SALDO
      FROM ADMIN.CONTROL_SOBREGIRO C,
      ADMIN.MONEDA          M,
      ADMIN.BANCO_DEPOSITO_SUB_RED BD
      WHERE C.COD_MONEDA = M.COD_MONEDA
      AND C.COD_EMPRESA = BD.COD_EMPRESA(+)
      AND C.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
      AND C.FECHA_INSERCION >= TO_DATE('".$txtFechaDesde."', 'DD/MM/YYYY')
      AND C.FECHA_INSERCION < TO_DATE('".$txtFechaHasta."', 'DD/MM/YYYY') + 1
      AND C.CONCILIADO = 'S'
      ".$laRecaudadora_1."
      AND C.COD_MONEDA = 1
      UNION ALL
      --CHEQUES RECHAZADOS
      SELECT T.COD_BANCO,
      T.COD_RECAUDADORA,
      TRUNC(T.FECHA_OPERACION) AS FECHA,
      M.DES_MONEDA,
      DECODE(T.TIPO_MOVIMIENTO, 'D', T.IMPORTE, 0) DEBITOS,
      0 DEPOSITOS,
      0 SALDO
      FROM ADMIN.CTRL_CHEQUES_SUB_RED T,
      ADMIN.MONEDA               M,
      ADMIN.TIPO_PAGO            TP
      WHERE T.COD_MONEDA = M.COD_MONEDA
      AND T.COD_TIPO_PAGO = TP.COD_TIPO_PAGO
      AND T.FECHA_OPERACION >= TO_DATE('".$txtFechaDesde."', 'DD/MM/YYYY')
      AND T.FECHA_OPERACION < TO_DATE('".$txtFechaHasta."', 'DD/MM/YYYY') + 1
      and t.cod_empresa = '01'
      ".$laRecaudadora_2."
      AND M.COD_MONEDA = 1) C,   ADMIN.BANCO B, ADMIN.ENTIDAD E
      WHERE C.COD_BANCO = B.COD_BANCO
      AND C.COD_RECAUDADORA = E.COD_ENTIDAD
      ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA ";
     */
    $query = " SELECT 
	   C.COD_BANCO,
       B.DES_BANCO,
       C.COD_RECAUDADORA,
       E.DES_ENTIDAD,
       FECHA,
       UPPER(DES_MONEDA),  
       DEBITOS,
       DEPOSITOS,       
       DEPOSITOS - DEBITOS DIFERENCIA, 
       SALDO, 
       DIAS_SOBREGIRO      
  FROM (
    --MOVIMIENTOS DE DEPOSITO Y COBRANZAS
        SELECT NVL(BD.COD_BANCO, C.COD_BANCO) COD_BANCO,               
                C.COD_RECAUDADORA,
                C.FECHA_PROCESO FECHA,
                M.DES_MONEDA,
                C.TOTAL_DEBITOS DEBITOS,
                C.TOTAL_CREDITOS DEPOSITOS, 
                C.SALDO SALDO, 
                C.DIAS_SOBREGIRO                
          FROM ADMIN.CONTROL_SOB_SALDO C,                
                ADMIN.MONEDA          M, 
                ADMIN.BANCO_DEPOSITO_SUB_RED BD
         WHERE C.COD_MONEDA = M.COD_MONEDA
           AND C.COD_EMPRESA = BD.COD_EMPRESA(+)
           AND C.COD_RECAUDADORA = BD.COD_RECAUDADORA(+)
           AND C.FECHA_PROCESO >= TO_DATE('" . $txtFechaDesde . "', 'DD/MM/YYYY')
           AND C.FECHA_PROCESO < TO_DATE('" . $txtFechaHasta . "', 'DD/MM/YYYY') + 1
           $laRecaudadora_1
           AND C.COD_MONEDA = $moneda) C, ADMIN.BANCO B, ADMIN.ENTIDAD E
WHERE C.COD_BANCO = B.COD_BANCO
AND C.COD_RECAUDADORA = E.COD_ENTIDAD
ORDER BY B.DES_BANCO, E.DES_ENTIDAD, FECHA ";


    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<h2 style="text-align:center">Saldo consolidado del ' . $txtFechaDesde . ' al ' . $txtFechaHasta . '</h2>
			<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>COD. BANCO</th>
					<th>BANCO</th>
					<th>COD. RECAUDADORA</th>
					<th>ENTIDAD</th>
					<th>FECHA</th>
					<th>MONEDA</th>
					<th>DEBITOS</th>
					<th>DEPOSITOS</th>
					<th>DIFERENCIA</th>
					<th>SALDO</th>
					<th>DIAS SOBREGIRO</th>
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $incremento = 0;
    $debitos = 0;
    $depositos = 0;
    $saldo = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $incremento++;
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        /* PARAM */
        $debitos += $fila[DEBITOS];
        $depositos += $fila[DEPOSITOS];
        $diferencia += $fila[DIFERENCIA];
        if ($fila[9] < 0 and $fila[DIAS_SOBREGIRO] > 0) {
            $style = 'style="color:red; font-weight:bold;"';
        } else {
            $style = '';
        }
        $adic .= '<tr>';
        $adic .= '<td  ' . $style . ' >' . $fila[0] . '</td>';
        $adic .= '<td   ' . $style . '>' . $fila[1] . '</td>';
        $adic .= '<td   ' . $style . '>' . $fila[2] . '</td>';
        $adic .= '<td   ' . $style . '>' . $fila[3] . '</td>';
        $adic .= '<td   ' . $style . '>' . $fila[4] . '</td>';
        $adic .= '<td   ' . $style . '>' . $fila[5] . '</td>';
        $adic .= '<td   ' . $style . '>' . numberFormater($fila[6], 0) . '</td>';
        $adic .= '<td   ' . $style . '>' . numberFormater($fila[7], 0) . '</td>';
        $adic .= '<td   ' . $style . '>' . numberFormater($fila[8], 0) . '</td>';
        $adic .= '<td   ' . $style . '>' . numberFormater($fila[9], 0) . '</td>';
        $adic .= '<td   ' . $style . '>' . numberFormater($fila[DIAS_SOBREGIRO], 0) . '</td>';
        $adic .= '</tr>';
    }
    /* TOTALIZADOR */
    $adic .= '<tr>';
    $adic .= '<td colspan="6"><b style="font-size:16px"> &nbsp; TOTAL </b></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($debitos, 0) . '</b></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($depositos, 0) . '</b></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($diferencia, 0) . '</b></td>';
    $adic .= '<td ><b style="font-size:16px"> -- </b></td>';
    $adic .= '<td >&nbsp;</td>';
    $adic .= '</tr>';

    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 1;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/SaldoConsolidado', 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function reporteChequeRechazado($id, $log, $idXLog, $fechaDesde, $fechaHasta, $codRecaudadora, $codBancoCheque, $importe, $nroCheque, $tipo) {
    include("db.inc.php");
    // $_SESSION['repName'] = 'ListadoRemesadora ' . $remesadora;
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $adic = '';

    $msg = 'No se obtuvieron resultados para su consulta';

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    if ($tipo == "insercion") {
        $laFecha = "AND C.FECHA_INSERCION >= TO_DATE('" . $fechaDesde . "', 'DD/MM/YYYY')
			   	  AND C.FECHA_INSERCION < TO_DATE('" . $fechaHasta . "', 'DD/MM/YYYY') + 1";
    } else {
        $laFecha = "AND C.FECHA_AUTORIZACION >= TO_DATE('" . $fechaDesde . "', 'DD/MM/YYYY')
			   	  AND C.FECHA_AUTORIZACION < TO_DATE('" . $fechaDesde . "', 'DD/MM/YYYY') + 1";
    }
    $condicion = "";
    if ($codRecaudadora != "*") {
        $condicion .= " AND C.COD_RECAUDADORA = " . $codRecaudadora . "";
    }
    if ($codBancoCheque != "*") {
        $condicion .= " AND C.COD_BANCO_CHEQUE = " . $codBancoCheque . "";
    }
    if ($nroCheque != "") {
        $condicion .= " AND C.NRO_CHEQUE = " . $nroCheque . "";
    }

    $query = "SELECT BR.DES_BANCO BANCO_RECAUDARA,
				   C.FECHA_OPERACION FECHA_PAGO,
				   C.COD_OPERACION COD_TRANSACCION,
				   DECODE(C.TIPO_MOVIMIENTO, 'D', 'DEBITO', 'CREDITO') TIPO_OPERACION,
				   C.IMPORTE,
				   BC.DES_BANCO BANCO_CHEQUE,
				   C.NRO_CHEQUE,
				   C.ESTADO,
				   C.OBSERVACION,
				   C.PAGOS_MH MH,
				   C.FECHA_INSERCION GENERADO,
				   C.COD_USUARIO_ALTA USUARIO_CARGA,
				   C.COD_USUARIO_AUT USUARIO_AUTORIZ,
				   C.FECHA_AUTORIZACION FECHA_AUTORIZ,
				   E.DES_ENTIDAD DESC_RECAUDADORA
			  FROM ADMIN.CTRL_CHEQUES_SUB_RED C,
				   ADMIN.BANCO                BR,
				   ADMIN.BANCO                BC,
				   ADMIN.ENTIDAD              E
			 WHERE E.COD_EMPRESA = C.COD_EMPRESA
			   AND E.COD_ENTIDAD = C.COD_RECAUDADORA
			   AND C.COD_BANCO = BR.COD_BANCO
			   AND C.COD_BANCO_CHEQUE = BC.COD_BANCO
			   $laFecha
			   $condicion
			ORDER BY C.FECHA_INSERCION ";


    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic.='<table cellspacing="1" border=1 class="tablesorter" align="center" style="font-size:12px;border-collapse: collapse">
			<thead>
				<tr>
					<th>BANCO RECAUDADORA</th>
					<th>RECAUDADORA</th>
					<th>FECHA PAGO</th>
					<th>TRANSACCION</th>
					<th>TIPO OPERACION</th>
					<th>IMPORTE</th>
					<th>BANCO CHEQUE</th>
					<th>NRO CHEQUE</th>
					<th>ESTADO</th>
					<th>OBSERVACION</th>
					<th>MH</th>
					<th>GENERADO</th>
					<th>USUARIO CARGA</th>
					<th>USUARIO AUTORIZO</th>
					<th>FECHA AUTORIZO</th>
					
				</tr>
			</thead>
		   <tbody>
			';
    $totalReg = 0;
    $incremento = 0;
    $debitos = 0;
    $depositos = 0;
    $saldo = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $incremento++;
        $est = 1;
        $msg = 'OK';
        $totalReg++;
        $adic .= '<tr>';
        $adic .= '<td >' . $fila[0] . '</td>';
        $adic .= '<td nowrap>' . $fila[14] . '</td>';
        $adic .= '<td >' . $fila[1] . '</td>';
        $adic .= '<td >' . $fila[2] . '</td>';
        $adic .= '<td >' . $fila[3] . '</td>';
        $adic .= '<td >' . $fila[4] . '</td>';
        $adic .= '<td nowrap>' . $fila[5] . '</td>';
        $adic .= '<td nowrap>' . $fila[6] . '</td>';
        $adic .= '<td >' . $fila[7] . '</td>';
        $adic .= '<td nowrap>' . $fila[8] . '</td>';
        $adic .= '<td nowrap>' . $fila[9] . '</td>';
        $adic .= '<td >' . $fila[10] . '</td>';
        $adic .= '<td >' . $fila[11] . '</td>';
        $adic .= '<td >' . $fila[12] . '</td>';
        $adic .= '<td >' . $fila[13] . '</td>';
        $adic .= '</tr>';
    }

    $adic.="</tbody></table>";
    if ($totalReg == 0) {
        $est = 1;
        $msg = 'No existe resultados en la consulta.';
    }

    $adic_ini = '<div id="rep" align="center">
					<a href="ticket/impresion.php" target="_blank"><img src="img/imprimir.jpg" border="0"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="ticket/xls.php" target="_blank"><img src="img/excel.jpg" border="0"></a>
				 </div>';

    $adic = $adic_ini . $adic;

    $_SESSION['datosImprimir'] = $adic;
    oci_free_statement($id_sentencia);

    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Respuesta: ' . $id . '|' . $est . '|' . $msg . '|Se omite el HTML porque ipuku');
    return $respuesta;
}

function insertarChequeRechazadoCodTrx($log, $idXLog, $observacion, $codTrx, $motivos) {
    //var_dump($motivos);
    //$motivoCheque = explode(",", $motivos);
    //var_dump($motivoCheque);
    //exit;
    $msg = '';
    $adic = '';
    $id = 52;
    $est = 0;
    $codOpe = codOperacionCtrCheqRechazado($log, $idXLog);
    $cod = explode("|", $codOpe);
    if ($cod[1] == 1) {
        $codigo = $cod[2];
    } else {
        $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx', 'ERROR1: ' . $codOpe);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }


//inicio de insert
    $query = "
insert into admin.ctrl_cheques_sub_red c
  (c.cod_banco,
   c.cod_empresa,
   c.cod_recaudadora,
   c.cod_moneda,
   c.cod_tipo_pago,
   c.conciliado,
   c.tipo_movimiento,
   c.fecha_insercion,
   c.fecha_operacion,
   c.pagos_mh,
   c.cod_usuario_alta,
   c.nro_cheque,
   c.cod_banco_cheque,
   c.estado,
   c.observacion,
   c.importe,
   c.cod_operacion,
   ult_actualizacion,
   cod_transaccion)
( select '98',
         t.cod_empresa,
         t.cod_recaudadora,
         t.cod_moneda,
         t.cod_tipo_pago,
         'N',
         'D',
         sysdate,
         t.fecpago,
         'N',
         '" . $_SESSION['codUsuario'] . "',
         t.nrocheque,
         t.cod_banco_cheque,
         'P',
         '" . $observacion . "',
         t.importe,
		 '$codigo',
		 sysdate,
         $codTrx
    from admin.transaccion t
   where t.cod_transaccion =$codTrx )";
    $msg = '';
    $adic = '';
    $id = 52;

    $log->write($idXLog, 0, 'commonFunctions.php/insertarChequeRechazadoCodTrx', 'Query: ' . $query);
    include("db.inc.php");
    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx', 'ERROR1: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx', 'ERROR2: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    $committed = oci_commit($ora_conn);






    ///insertar motivos
    $query1 = '';
    $motivoCheque = explode(",", $motivos);
    foreach ($motivoCheque as $motivoCheque) {
        $query1 = "insert into admin.controlcheque_motivo
                              (tid,
                              cod_operacion,
                              tid_motivo)
                            values
                              ((select (nvl((select max(TID) from admin.controlcheque_motivo), 0)+1) from dual),
                              '$codigo',
                              $motivoCheque
                              )";
        $log->write($idXLog, 0, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'Query: ' . $query1);



        include("db.inc.php");
        $id_sentencia1 = @oci_parse($ora_conn, $query1);

        if (!$id_sentencia1) {
            $e = oci_error($ora_conn);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'ERROR1: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query1;
            return $respuesta;
        }

        $r = @oci_execute($id_sentencia1, OCI_DEFAULT);

        if (!$r) {
            $e = oci_error($id_sentencia1);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/insertarChequeRechazadoCodTrx_motivos', 'ERROR2: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
            return $respuesta;
        }

        $committed1 = oci_commit($ora_conn);

        if ($committed1) {
            
        } else {
            $msg = oci_error($id_sentencia);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
            return $respuesta;
        }
        oci_close($ora_conn);
    }



    if ($committed) {

        $est = '1';
        $msg = 'OK';
        $adic = "Insertado correctamente\nCodigo Operacion:" + $codigo;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    } else {
        $msg = oci_error($id_sentencia);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . $query;
        return $respuesta;
    }

    oci_close($ora_conn);
}

function genlistaGenerarChequesRech($log, $idXLog, $codOperacion) {
    include("db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;
    $id = 25;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
//consultar el si existe ya registrado

    $query = "select t.cod_operacion, cod_recaudadora
from admin.CTRL_CHEQUES_SUB_RED t where t.cod_transaccion= '$codOperacion'";

    $log->write($idXLog, 0, 'commonFunctions.php/' . __FUNCTION__, 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic . '|';
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/' . __FUNCTION__, 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $existe = 0;
    $recaudadora = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $existe = $fila['COD_OPERACION'];
        $recaudadora = $fila['COD_RECAUDADORA'];
    }
    if ($existe == 0) {
        $query = "
select t.cod_transaccion,
       e.des_entidad,
       bc.des_banco, 
       t.nrocheque,
       t.fecpago,
       m.des_moneda,
       m.siglas ||' ' ||t.importe, 
       tp.des_tipo_pago
  from admin.transaccion t, 
       admin.banco       bc,
       admin.entidad     e,
       admin.moneda      m,
       admin.tipo_pago   tp
 where t.cod_banco_cheque = bc.cod_banco 
   and t.cod_empresa = e.cod_empresa
   and t.cod_recaudadora = e.cod_entidad
   and t.cod_moneda = m.cod_moneda
   and t.cod_tipo_pago=tp.cod_tipo_pago
   and t.cod_tipo_pago=2
   and t.cod_banco_recaudadora='98'
   and t.cod_transaccion=$codOperacion";

        $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech', 'Query: ' . $query);

        $id_sentencia = @oci_parse($ora_conn, $query);

        if (!$id_sentencia) {
            $e = oci_error($ora_conn);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
            return $respuesta;
        }

        $r = @oci_execute($id_sentencia, OCI_DEFAULT);

        if (!$r) {
            $e = oci_error($id_sentencia);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech', 'ERROR: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
            return $respuesta;
        }
//consulta para cargar motivos de cheques
        $query1 = "select * from admin.MOTIVOS_RECHAZO_CHEQUE ";
        $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech ', 'Query: ' . $query1);

        $id_sentencia2 = @oci_parse($ora_conn, $query1);

        if (!$id_sentencia2) {
            $e = oci_error($ora_conn);
            $msg = $e['message'];
            $log->write($idXLog, 3, 'commonFunctions.php/genlistaAprobarChequesRech ' . __LINE__, 'ERROR: ' . $msg);
            $est = 0;
            $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
            return $respuesta;
        }

        $r = @oci_execute($id_sentencia2, OCI_DEFAULT);

        $selec = '<select size="10" class="multiselect" multiple="multiple">';
        while ($fila1 = oci_fetch_array($id_sentencia2, OCI_RETURN_NULLS)) {
            $selec.=" <option value='$fila1[0]'>$fila1[1]</option>"; //loo 
        }
        $selec.=' </select>';
        $boton = '';
        $adic.='<h2>..:: Confirmaci&oacute;n de Cheques Rechazados - proNET SUB RED ::..</h2>';

        $adic.='<table id="table" name="table" cellspacing="1" class="tablesorter">
                    <thead>
                   
                            <th>Cod. Transaccion&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Entidad&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Banco&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>Nro Cheque.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Fecha Pago&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Moneda&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Importe&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>Tipo Pago&nbsp;&nbsp;&nbsp;&nbsp;</th>  
                        <tr>
                    </thead>
                    <tbody>';
        $aprobar = '';
        while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
            $aprobar = '"' . $fila[0] . '","A"';




            $adic.="  <td>" . $fila[0] . "</td>
                            <td>" . $fila[1] . "</td>
                            <td>" . $fila[2] . "</td>
                            <td>" . $fila[3] . "</td>
                            <td>" . $fila[4] . "</td>
                            <td>" . $fila[5] . "</td>
                            <td>" . $fila[6] . "</td>
							<td>" . $fila[7] . "</td>  
                         </tr>";
            $est = '1';
            $msg = 'OK';
        }
        $adic.="</tbody></table>";
        $adic.='<div id="detalle"> <p>Motivo:  
        <br />
CTRL+CLICK = Selecciona varios motivos       
<br />
' . $selec . '
    <label for="textarea"><br />
      Observacion:<br />
     </label>
    <textarea name="observacion" id="observacion" cols="45" rows="5"></textarea><br />
    
</div>';
        $adic.="<input type='button' href='javascript:void(0)' onclick='accionOperacion(" . $aprobar . ");' title='Click para GENERAR este Cheque Rechazado' value='Generar' />";
        oci_close($ora_conn);
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

        /*   $vars = explode("|", $resulGetNoficaciones);
          $id = $vars[0];
          $est = $vars[1];
          $msg = $vars[2];
          $adic = $vars[3];
          unset($vars);
         */


        $finF4 = microtime(true);
        $totalF4 = round($finF4 - $inicioF4, 4);
        $log->write($idXLog, 0, 'commonFunctions.php/genlistaAprobarChequesRech', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
        return $respuesta;
    } else {
        $est = '1';
        $adic = '<h2 style="color:red;">La trasaccion ya fue cargada con Expediente <b>Nro.' . $existe . '</b></h2>';
        $adic.="<br/> <input type='button' value='Ver Nota' onclick='verNota($existe, $recaudadora)' /> <div id='dvnotas'></div>";
        //12791736
        return $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
    }
}

function codOperacionCtrCheqRechazado($log, $idXLog) {
    include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/codOperacionCtrCheqRechazado', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select admin.seq_ctr_cheq_rechazado.nextval from  dual";

    $log->write($idXLog, 0, 'commonFunctions.php/codOperacionCtrCheqRechazado', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/codOperacionCtrCheqRechazado', '* ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', '** ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        return '1|1|' . $fila[0];
        $est = '1';
        $msg = 'OK';
    }



    oci_close($ora_conn);
    echo $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    $vars = explode("|", $resulGetNoficaciones);
    $id = $vars[0];
    $est = $vars[1];
    $msg = $vars[2];
    $adic = $vars[3];
    unset($vars);



    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/codOperacionCtrCheqRechazado', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function consultarSQL($query){
    global $log, $idXLog;
        include("db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/codOperacionCtrCheqRechazado', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
     $log->write($idXLog, 0, 'commonFunctions.php/codOperacionCtrCheqRechazado', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/codOperacionCtrCheqRechazado', '* ERROR: ' . $msg);
        $est = 0;
   
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', '** ERROR: ' . $msg);

        return $respuesta;
    }
    return $id_sentencia;
}

function consultaRV() {
    global $ora_conn, $log, $idXLog;
    $ubicacion = getcwd() . "/" . __FUNCTION__;
    $est = 0;
    $cont = 0;
    //est=1;
    $query = "select * from REMESAS.RANGO_VOLUMEN RV, REMESAS.MONEDA M
WHERE M.COD_MONEDA= RV.COD_MONEDA
";
    $result = consultarSQL($query, $ubicacion);
    $cuTabla = "";
    while ($fila = oci_fetch_array($result, OCI_RETURN_NULLS)) {

        $cont++;
        $cuTabla .= "
                <tr>
                  <td><div id='dvdesde" . $fila['COD_RANGO_VOLUMEN'] . "'>" . $fila['DESDE'] . "</div>&nbsp;</td>
                  <td><div id='hasta" . $fila['COD_RANGO_VOLUMEN'] . "'>" . $fila['HASTA'] . "&nbsp;</td>
                  <td><div id='moneda" . $fila['COD_RANGO_VOLUMEN'] . "'>" . $fila['DES_MONEDA_SINGULAR'] . "&nbsp;</td>
                  <td><div id='puntaje" . $fila['COD_RANGO_VOLUMEN'] . "'>" . $fila['PUNTAJE_RIESGO'] . "&nbsp;</td>
                  <td> <center><input type='button' name='btneditar' id='btneditar' value='Editar' onclick='editarRango(".$fila['COD_RANGO_VOLUMEN'] ."," . $fila['COD_MONEDA'] . ")'/>&nbsp;
                   <input type='button' name='btneditar' id='btneditar' value='Borrar' /></center></td>
                </tr>";
    }
    // $log->write($idXLog, 0, $ubicacion, '' . 'Cuerpo tabla--->' . $cuTabla);
    $cbtabla = "
        <table width='100%' class='tinytable'>
          <thead>
            <tr>
                <th>Desde</th>
                <th>Hasta</th>
                <th>moneda</th>
                <th>Puntaje Riesgo</th>
                <th>Acciones</th>
            </tr>
        </thead>
$cuTabla
</table>
";
    if ($cont > 0) {
        $est = 1;
        $cbtabla = aplicarTiny($cbtabla);
        return "$est|$cbtabla";
    } else {
        return "$est| No se pudo obtner ningun dato";
    }
}
function aplicarTiny($tabla) {
    $tabla = str_replace("<table class='tinytable'>", "<table align='center' class='tinytable' cellpadding='0' cellspacing='0' border='0' id='lista' >", $tabla);
    $tabla = str_replace("<th class='nosort'>", "<th class='nosort'><h3>", $tabla);
    $tabla = str_replace("<th>", "<th><h3>", $tabla);
    $tabla = str_replace("</th>", "</h></th>", $tabla);

    $tabla = "

	<div id='tablewrapper'>
		<div id='tableheader'>
        	<div class='search'>
                <select id='columns' onchange=\"sorter.search('query')\"></select>
                <input type='text' id='query' onkeyup=\"sorter.search('query')\" />
            </div>
            <span class='details'>
				<div>Registros <span id='startrecord'></span>-<span id='endrecord'></span> de <span id='totalrecords'></span></div>
        		<div><a class='enlace' href='javascript:sorter.reset()' onclick='javascript: nosort();'>[Restaurar]</a></div>
        	</span>
        </div>
" . "<br />" . $tabla . "<br />";

    $tinyTools = "<li id='tinyTools' style='float:right; margin-top:0; border-top:0; padding-top:0;'><a onclick='javascript: invisible();' target='blank' title='Herramientas' class='enlaceTinyTools'></a></li>";


    $tabla .= "
<div id='tablefooter'>
          <div id='tablenav'>
            	<div>
                    <img src='images/first.gif' width='16' height='16' alt='First Page' onclick='sorter.move(-1,true)' />
                    <img src='images/previous.gif' width='16' height='16' alt='First Page' onclick='sorter.move(-1)' />
                    <img src='images/next.gif' width='16' height='16' alt='First Page' onclick='sorter.move(1)' />
                    <img src='images/last.gif' width='16' height='16' alt='Last Page' onclick='sorter.move(1,true)' />
                </div>
                <div><select id='pagedropdown'></select></div>
                <div><a class='enlace' href='javascript:sorter.showall()'>[Ver Todos]</a></div>
            </div>
			<div id='tablelocation'>
            	<div>
                    <select onchange='sorter.size(this.value)'>
                    	<option value='5'>5</option>
                        <option value='10' selected='selected'>10</option>
                        <option value='20'>20</option>
                        <option value='50'>50</option>
                        <option value='100'>100</option>
                    </select>
                    <span>Resultados por Pagina</span>
                </div>
                <div class='page'>Pagina <span id='currentpage'></span> de <span id='totalpages'></span></div>
            </div>
        </div>
    </div>
";
    $tabla .= '
	<script type="text/javascript">	  
	var sorter = new TINY.table.sorter("sorter","lista",{
		headclass:"head",
		ascclass:"asc",
		descclass:"desc",
		evenclass:"evenrow",
		oddclass:"oddrow",
		evenselclass:"evenselected",
		oddselclass:"oddselected",
		paginate:true,
		size:10,
		colddid:"columns",
		currentid:"currentpage",
		totalid:"totalpages",
		startingrecid:"startrecord",
		endingrecid:"endrecord",
		totalrecid:"totalrecords",
		pageddid:"pagedropdown",
		navid:"tablenav",
		sortcolumn:0,
		sortdir:1,
		init:true
	});	
	//resaltar filas al posicionarsesobre ellas
	$("#lista tbody tr").mouseover(function() {$(this).children("td").each(function(){$(this).css("background-color", "#DDD"); }); });
	$("#lista tbody tr").mouseout(function() {$(this).children("td").each(function(){$(this).css("background-color", ""); }); });	
			
  </script>
';
    return $tabla;
}
?>