<?php
/************************************************************/
/* Name: Gestor de log's.					     			*/ 
/* Version: 2.0         						     		*/
/* Autor: <PERSON>.  					     			*/
/* Fecha Creacion: Enero-2009.    					     	*/
/* Fecha Actualizacion: Agosto-2011.    					*/
/* Funcionalidades:   						     			*/
/* Backup de varios niveles (debug, notice, warning, etc)   */
/* Puede escribir varios archivos distintos.				*/
/* Envio de notificaciones para:                            */
/*  - Los niveles de escritura, se configura con $mailXlog	*/
/*  - Backup's realizados					     			*/
/*	- Utiliza la clase [PHPMailer]							*/
/* Control del tama�o del archivo log 			     		*/
/* Tipos de almacenamiento de historicos					*/
/*	- Ciclicos: Usa [maxBackupIndex] para controlar.		*/
/*	- Historico: Guarda todo.								*/
/* Generador de ID �nico para cada proceso de escritura   	*/
/************************************************************/
class log4My{

	var $type;//Definici�n de variables.
	var $fromName;

 	function log4My(){
		include($_SERVER['DOCUMENT_ROOT']."/config/log4MyGestionInterna.inc.php"); //Variables
		$this->path=$path;
		$this->filename="procesos_".date('dmY').".log";
		$this->backupType=$backupType;
		$this->maxFileSize=10*1048576; // 1 megabyte(MB) = 1,048,576 bytes 
		$this->maxBackupIndex=$maxBackupIndex-1; // Restamos 1, ya que la cantidad hace referencia a los archivos del history.
		$this->hostMail=$host_mail;
		$this->SMTPAuth=$SMTPAuth;
		$this->userNameMail=$userName_mail;
		$this->passwordMail=$password_mail;
		$this->to=$to;
		$this->subject=$subject; 
		$this->from=$from;
		$this->enviarNotificacion=$enviarNotificacion;
		$this->mailXlog=$mailXlog;
		$this->app=$app;
	}
	
	function write($id, $nivel, $locate, $msg, $fileName="")
	{
		//if($fileName!="") $this->filename = $fileName."_".$this->filename;	
		$filename = (trim($fileName) != '')? $fileName."_".$this->filename : $this->filename ; 
		$enviarMail = '0'; //Por defecto.
		switch($nivel){
			case 0:
				$nivel = "DEBUG";
				$enviarMail = substr($this->mailXlog, 0, 1);
				break;
			case 1:
				$nivel = "NOTICE";
				$enviarMail = substr($this->mailXlog, 1, 1);
				break;
			case 2:
				$nivel = "WARNING";
				$enviarMail = substr($this->mailXlog, 2, 1);
				break;
			case 3:
				$nivel = "FATAL";
				$enviarMail = substr($this->mailXlog, 3, 1);
				break;
			default:
				$nivel = "ERROR";
				$enviarMail = substr($this->mailXlog, 4, 1);
		}
		$this->backup(); //Se realizarn los controles correspondientes al backup
		$datos="[".$id."]".date("H:i:s")." ".$nivel."-->".$locate.": ".$msg."\r\n";
		$this->fileProcess(0);
		$this->fileProcess(1, $datos);
		$this->fileProcess(2);		
		if($enviarMail == '1') $this->SendMail($datos);
	}

	function getId4Log(){
	//Se obtiene el ID para identificar al proceso en el log-->dia+mes+a�o+hora+min+seg+miliseg(exactitud 4 puntos decimales.)
	   list($MiliSegundos, $Segundos) = explode(" ", microtime());
	   $id=substr(strval($MiliSegundos),2,4);
	   return strval(date("d").date("m").date("y").date("H").date("i").date("s").$id);
	}
	
	function FileSize(){//Obtiene el tama�o del archivo
		$tam = filesize($this->path.'/'.$this->filename);
		return $tam;
	}
	
	function backup(){
		
		$name = $this->filename;
		$path = $this->path;
	
		if (file_exists($path.'/'.$name)){
			if ($this->FileSize() > $this->maxFileSize){
				//Si el archivo pesa mas de "$this->maxFileSize" MB se copia en el historico (renombrado) y se borra el original.
				if($this->backupType=='H'){
					$this->newName=date("dmY-His").'_'.$name;
					copy($path.'/'.$name,$path.'/history/'.$this->newName);
					unlink($path.'/'.$name);
					$datos=date("d/m/Y H:i:s")." Backup realizado para ".$this->newName."\r\n";
					$this->fileProcess(0);
					$this->fileProcess(1, $datos);
					$this->fileProcess(2);
					if ($this->enviarNotificacion) $this->SendMail('B');
				}
				else{
					$this->mantenimientoLogs($path.'/history/', $this->recorrerArchivos($this->escanearDirectorio($path.'/history/')), $this->maxBackupIndex);
					copy($path.'/'.$name,$path.'/history/'.$name.'.1');
					unlink($path.'/'.$name);
					$datos=date("d/m/Y H:i:s")." Backup realizado para ".$this->name.".1\r\n";
					$this->fileProcess(0);
					$this->fileProcess(1, $datos);
					$this->fileProcess(2);
					if ($this->enviarNotificacion) $this->SendMail('B');
					
				}
			}
		}
	}
	
	function fileProcess($action, $datos = ''){
	// $action->0 = Abrir.	1 = Escribir.	2 = Cerrar
		switch($action){
			case 0: //Se abre el archivo.
				$this->type=fopen($this->path.'/'.$this->filename,"a+");
				break;
			case 1: //Se escribe en el.
				fwrite($this->type,$datos);
				break;
			default: //Se cierra.
				fclose($this->type);
		}
	}
	
	function sendMail($datos){//Env�o de mail con el datalle del proceso.
		require_once("class.phpmailer.php");
		$mail = new PHPMailer();
		$mail->IsSMTP();
		$mail->Host=$this->hostMail;
		$mail->SMTPAuth=$this->SMTPAuth;
		$mail->Username=$this->userNameMail;
		$mail->Password=$this->passwordMail;
		$mail->From=$this->from;
		$mail->FromName=$this->fromName;
		
		if($datos == 'B'){
			$contenido  = '<html>';
			$contenido .= 	'<body>';
			$contenido .= 		'<h2>'.$this->subject.'</h2>';
			$contenido .= 		'<b>Llegamos al l&iacute;mite del tama�o del log, fue almacenado en el hist&oacute;rico</b>';
			$contenido .= 		'<br><p><u>Detalle</u></p>';
			$contenido .= 		'<p>Archivo Original: '.$this->filename.'</p>';
			$contenido .= 		'<p>Destino: history/'.$this->newName.'</p>';
			$contenido .= 		'<p>App: '.$this->app.'</h2>';
			$contenido .= 	'</body>';
			$contenido .= '</html>';
		}
		else{
			$contenido  = '<html>';
			$contenido .= 	'<body>';
			$contenido .= 		'<h2>'.$this->subject.'</h2>';
			$contenido .= 		'<b>Mensaje enviado por class.log4My.php</b>';
			$contenido .= 		'<br><p><u>Detalle</u></p>';
			$contenido .= 		'<p>'.$datos.'</p>';
			$contenido .= 		'<p>App: '.$this->app.'</h2>';
			$contenido .= 	'</body>';
			$contenido .= '</html>';
		}
		
		$mail->AddReplyTo("no-reply", "No responder");
		$mail->WordWrap = 50;
		$mail->IsHTML(true);
		$mail->Subject = $this->subject;
		$mail->Body = $contenido;
		$mail->AltBody = $contenido;
		$mail->AddAddress($this->to);
		if(!$mail->Send()){
			$datos=date("d/m/Y H:i:s")." La notificacion no se pudo enviar para -> ".$this->to." (".$mail->ErrorInfo.") ";
			$datos.="Host<".$mail->Host.">\r\n";
			$this->fileProcess(0);
			$this->fileProcess(1, $datos);
			$this->fileProcess(2);
		}
		else{
			$datos=date("d/m/Y H:i:s")." Notificacion enviada para -> ".$this->to." .\r\n";
			$this->fileProcess(0);
			$this->fileProcess(1, $datos);
			$this->fileProcess(2);
		}
	}
	
	function mantenimientoLogs($path, $archivos, $maxLogs){
		if($archivos!=NULL){
			foreach($archivos as $clave=>$valor){
				foreach($valor as $archivo=>$indice){
					list($nombre, $extension, $nroLog) = explode(".", $archivo);
					if($maxLogs<$nroLog){
						if(file_exists($path.$archivo)){
							@unlink($path.$archivo);
						}
					}
					else{
						$nroLog = $nroLog + 1;
						@rename($path.$archivo, $path.$nombre.'.'.$extension.'.'.$nroLog);
					}
				}
			}
		}
		return true;
	}
	
	function escanearDirectorio($path){
		$scanarray = scandir($path);
		$contador=0;
		for ($i = 0; $i < count ($scanarray); $i++){  
			if ($scanarray[$i] != "." && $scanarray[$i] != ".."){  
				if (is_file ($path . "/" . $scanarray[$i])){  
					$contador++;
					$thepath = pathinfo ($path . "/" . $scanarray[$i]);  
					if(is_numeric($thepath['extension'])){
						$archivos[$contador][$scanarray[$i]] = $thepath['extension'];
					}
				}  
			}  
		}
		return $archivos;
	}
	
	function recorrerArchivos($archivos){
		if($archivos!=NULL){
			arsort($archivos);
			foreach($archivos as $clave=>$valor){
				$archivos[$clave]=$valor;
			}
			return $archivos;
		}
	}
}
?>