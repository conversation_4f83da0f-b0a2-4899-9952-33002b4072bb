<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body leftmargin="0" topmargin="0">
<table width="98%" border="0" align="right" cellpadding="2" cellspacing="1">
  <tr> 
    <td><span class="pageHeader">Candlestick Chart &gt; Overview</span></td>
  </tr>
  <tr> 
    <td>&nbsp;</td>
  </tr>
  <tr> 
    <td class="text"><p>The candlestick chart is a special type of chart basically 
        meant for charting financial stock market data. </p>
      <p> Candlestick charts are on record as being the oldest type of charts 
        used for price prediction. There are four elements necessary to construct 
        a candlestick chart, the OPEN, HIGH, LOW and CLOSE price for a given time 
        period. Below are examples of candlesticks and a definition for each candlestick 
        component:<br>
      </p></td>
  </tr>
  <tr> 
    <td class="text"><img src="Images/CC_1.gif" width="349" height="213"></td>
  </tr>
  <tr> 
    <td class="text"><img src="Images/CC_2.gif" width="347" height="211"></td>
  </tr>
  <tr> 
    <td class="text">A Candlestick chart created using FusionCharts looks as under:</td>
  </tr>
  <tr> 
    <td class="text"><img src="Images/CC_FC.gif" ></td>
  </tr>
  <tr> 
    <td class="text">You can also set the candles as bar (stock) as in the image 
      below:</td>
  </tr>
  <tr> 
    <td class="text"><img src="Images/CC_3.gif" width="545" height="290"></td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="text">Let us quickly move on and see the various elements of a 
      candlestick chart.</td>
  </tr>
  <tr>
    <td class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
