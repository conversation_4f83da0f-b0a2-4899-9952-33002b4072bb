<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><!-- InstanceBegin template="/Templates/Documentation Page.dwt" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- InstanceBeginEditable name="doctitle" -->
<title>FudionCharts DOM Documentaion - Overview</title>
<!-- InstanceEndEditable -->

<script type="text/javascript" src="JS/lib.js"></script>

<link rel="stylesheet" type="text/css" href="assets/prettify/prettify.css">
<script type="text/javascript" src="assets/prettify/prettify.js"></script>

<link rel="stylesheet" type="text/css" href="css/typoset.css">

<!-- InstanceBeginEditable name="head" -->
<!-- InstanceEndEditable -->

</head>

<body>

<div id="wrapper">

  <noscript class="hl-red">&nbsp;<br />
  	For maximum compatibility, it is requested that you browse this page in a JavaScript enabled browser.</noscript>
  
  <div id="topshadow"></div>
  <div id="viewport">
  <h2><!-- InstanceBeginEditable name="pagetitle" -->
  FusionCharts DOM  Overview<!-- InstanceEndEditable --></h2>

  <div id="contents"><!-- InstanceBeginEditable name="pagebody" -->
    <p>FusionCharts DOM provides an elegant way to include charts in your web pages. Instead of writing standard JavaScript code or OBJECT/EMBED tags to embed charts, FusionCharts DOM lets you create a chart by just including <span class="code-inline">&lt;fusioncharts&gt; ... &lt;/fusioncharts&gt;</span> tags in your HTML code. Configuration of the charts is as simple as providing attributes to this HTML element.</p>
    <p>Essentially, FusionCharts DOM is a JavaScript file that  you include in your HTML page. For the time being forget everything about JavaScript. Whenever you need to create a chart, you just use the  tag in your HTML code. As such, even your designers can create and manage charts in your web applications.</p>
    <p>&nbsp;</p>
    <h3>The advantages of using FusionCharts DOM</h3>
    <p>The advantages of using FusionCharts DOM over the standard chart embedding code are multi-fold, as listed below:    </p>
    <ul>
      <li>Provides an easy and consistent way to include charts in your web pages.</li>
      <li>Helps you do away with complex JavaScript snippets and OBJECT/EMBED tags.</li>
      <li>Easy to understand codes make it more maintainable.</li>
      <li>Runs seamlessly on all major browsers like IE, FF, Opera and Safari. It also runs on older FF2 and IE6.</li>
      <li>Still provides native integration with JavaScript, retaining all the features provided by FusionCharts.</li>
      <li>Does not require any server-side script and can run on pure HTML documents.</li>
      <li>Easily integrates with other JavaScript libraries and frameworks.</li>
    </ul>
    <p>FusionCharts DOM JavaScript code is highly efficient, compact, object-oriented and localized. The only drawback is: it will not pass W3C validation. But failing W3C validation does not impact your web pages in any other way. We choose to call it a trade-off between ease of use and standards.</p>
    <p>&nbsp;</p>
    <!-- InstanceEndEditable --></div>
  
  <div id="footer"><!-- InstanceBeginEditable name="pageFooter" --><!-- InstanceEndEditable --></div>
  </div>
  
</div>

</body>
<!-- InstanceEnd --></html>
