<?php
//We've included ../Includes/FusionCharts.php and ../Includes/DBConn.php, which contains
//functions to help us easily embed the charts and connect to a database.
include("../Includes/FusionCharts.php");
include("../Includes/DBConn.php");
?>
<HTML>
<HEAD>	
    <TITLE>
    FusionCharts Free - Database + JavaScript Example
    </TITLE>

    <?php
    //In this example, we show a combination of database + JavaScript rendering using FusionCharts.

    //The entire app (page) can be summarized as under. This app shows the break-down
    //of factory wise output generated. In a pie chart, we first show the sum of quantity
    //generated by each factory. These pie slices, when clicked would show detailed date-wise
    //output of that factory.

    //The XML data for the pie chart is fully created in PHP at run-time. PHP interacts
    //with the database and creates the XML for this.
    //Now, for the column chart (date-wise output report), we do not submit request to the server.
    //Instead we store the data for the factories in JavaScript arrays. These JavaScript
    //arrays are rendered by our PHP Code (at run-time). We also have a few defined JavaScript
    //functions which react to the click event of pie slice.

    //We've used an MySQL databasw which contains two databases. 

    //Before the page is rendered, we need to connect to the database and get the
    //data, as we'll need to convert this data into JavaScript variables.
    $link = connectToDB();

    //The following string will contain the JS Data and variables.
    //This string will be built in PHP and rendered at run-time as JavaScript.
    $jsVarString = "";

    //Generate the chart element
    $strXML = "<graph caption='Factory Output report' subCaption='By Quantity' decimalPrecision='0' showNames='1' numberSuffix=' Units' pieSliceDepth='20' formatNumberScale='0' >";

    //initialize index
    $indexCount = 0;

    // Fetch all factory records
    $strQuery = "select * from Factory_Master";
    $result = mysql_query($strQuery) or die(mysql_error());

    //Iterate through each factory
    if ($result) {
        while($ors = mysql_fetch_array($result)) {

            $indexCount++;

            //Create JavaScript code to add sub-array to data array
            //data is an array defined in JavaScript (see below)
            //We've added \t and \n to data so that if you View Source of the
            //output HTML, it will appear properly. It helps during debugging
            $jsVarString .= "\t\t data[" . $indexCount . "] = new Array(); \n";

            //Now create second query to get date-wise details for this factory
            $strQuery = "select * from Factory_Output where FactoryId=" . $ors['FactoryId'] . " order by DatePro Asc";
            $result2 = mysql_query($strQuery) or die(mysql_error()); 

            if ($result2) {
                while($ors2 = mysql_fetch_array($result2)) {
                    //Put this data into JavaScript as another nested array.
                    //Finally the array would look like data[factoryIndex][i][dataLabel/name,dataValue]
                    $jsVarString .= "\t\t" . "data[" . $indexCount . "].push(new Array('" . datePart("d",$ors2["DatePro"]) . "/" . datePart("m",$ors2['DatePro']) . "'," . $ors2['Quantity'] . ")); \n";
                }
                //free the resultset
                mysql_free_result($result2);
            }

            //Now create another query to get details for this factory
            $strQuery = "select sum(Quantity) as TotOutput from Factory_Output where FactoryId=" . $ors['FactoryId'];
            $result2 = mysql_query($strQuery) or die(mysql_error()); 
            $ors2 = mysql_fetch_array($result2);
            //Generate <set name='..' value='..' link='..' />
            //Note that we're setting link as updateChart(factoryIndex) - JS Function
            $strXML .= "<set name='" . $ors['FactoryName'] . "' value='" . $ors2['TotOutput'] . "' link='javaScript:updateChart(" . $indexCount . ")'/>";
        }
    }

    //Finally, close <graph> element
    $strXML .= "</graph>";
	
	?>	
	<SCRIPT LANGUAGE="Javascript" SRC="../../FusionCharts/FusionCharts.js">
		//You need to include the above JS file, if you intend to embed the chart using JavaScript.
		//Embedding using JavaScripts avoids the "Click to Activate..." issue in Internet Explorer
		//When you make your own charts, make sure that the path to this JS file is correct. Else, you would get JavaScript errors.
	</SCRIPT>
	
	<SCRIPT LANGUAGE="JavaScript">
		//Here, we use a mix of server side script (PHP) and JavaScript to
		//render our data for factory chart in JavaScript variables. We'll later
		//utilize this data to dynamically plot charts.
		
		//All our data is stored in the data array. From PHP, we iterate through
		//each recordset of data and then store it as nested arrays in this data array.
		var data = new Array();
		
		<?php
		//Write the data as JavaScript variables here
		echo $jsVarString;
		//The data is now present as arrays in JavaScript. Local JavaScript functions
		//can access it and make use of it. We'll see how to make use of it.
		?>
		
		/** 
		 * updateChart method is invoked when the user clicks on a pie slice.
		 * In this method, we get the index of the factory, build the XML data
		 * for that that factory, using data stored in data array, and finally
		 * update the Column Chart.
		 *	@param	factoryIndex	Sequential Index of the factory.
		*/		
		function updateChart(factoryIndex){
			//defining array of colors
			//We also initiate a counter variable to help us cyclically rotate through
			//the array of colors.
			var FC_ColorCounter=0;
			var arr_FCColors= new Array("1941A5" , "AFD8F8", "F6BD0F", "8BBA00", "A66EDD", "F984A1", "CCCC00", "999999", "0099CC", "FF0000", "006F00", "0099FF", "FF66CC", "669966", "7C7CB4", "FF9933", "9900FF", "99FFCC", "CCCCFF", "669900");
			
			
			//Storage for XML data document
			var strXML = "<graph caption='Factory " + factoryIndex  + " Output ' subcaption='(In Units)' xAxisName='Date' decimalPrecision='0'>";
			
			//Add <set> elements
			var i=0;
			for (i=0; i<data[factoryIndex].length; i++){
				strXML = strXML + "<set name='" + data[factoryIndex][i][0] + "' value='" + data[factoryIndex][i][1] + "' color='"+ arr_FCColors[++FC_ColorCounter % arr_FCColors.length] +"' />";
			}
			
			//Closing graph Element
			strXML = strXML + "</graph>";
						
			//Update it's XML
			updateChartXML("FactoryDetailed",strXML);
		}
	</SCRIPT>
	<style type="text/css">
	<!--
	body {
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	.text{
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	-->
	</style>
</HEAD>
	
<BODY>

<CENTER>
<h3><a href="http://www.fusioncharts.com" target="_blank">FusionCharts Free</a> - Database + JavaScript Example</h3>
<h5>Inter-connected charts - Click on any pie slice to see detailed chart below.</h5>
<p>The charts in this page have been dynamically generated using data contained in a database. We've NOT hard-coded the data in JavaScript.</p>
<?php		
	//Initialize the Pie chart with sum of production for each of the factories
	//$strXML will be used to store the entire XML document generated
		
	//Create the chart - Pie 3D Chart with data from strXML
	echo renderChart("../../FusionCharts/FCF_Pie3D.swf", "", $strXML, "FactorySum",650,300,false,false);
?>
	<BR>
<?php
	//Column 2D Chart with changed "No data to display" message
	//We initialize the chart with <graph></graph>
	echo renderChart("../../FusionCharts/FCF_Column2D.swf?ChartNoDataText=Please click on a pie slice above to view detailed data.", "", "<graph></graph>", "FactoryDetailed",600,300,false,false);
?>
<BR><BR>
<a href='../NoChart.html' target="_blank">Unable to see the charts above?</a>
<H5 ><a href='../default.htm'>&laquo; Back to list of examples</a></h5>
</CENTER>
</BODY>
</HTML>