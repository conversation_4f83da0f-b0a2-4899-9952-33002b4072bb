<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" type="text/css" href="../Style.css">
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts PHP Class API &gt;Advanced Usage &gt; Chart Messages  </h2></td>
  </tr>
  

  <tr>
    <td valign="top" class="text"><p>FusionCharts PHP Class API lets you provide various chart messages while the chart is loading, retrieving data or rendering. For this you need to&nbsp; use <span class="codeInline">setChartMessage </span>function. </p>    </td>
  </tr>
  <tr>
     <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
     <td valign="top" class="text"><span class="codeInline">To use setChartMessage put all chart message attributes and values in a delimiter (default is ;) separated string and pass to the function. </span></td>
  </tr>
  
  <tr>
     <td valign="top" class="codeBlock">$FC-&gt;<strong>setChartMessage</strong>(&quot;ChartNoDataText=Chart Data not provided;PBarLoadingText=Please Wait.The chart is loading...&quot;);</td>
  </tr>
  <tr>
     <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="text">To know more on the varous available chat message please see page: <a href="../Adv_ChartMessages.html" title="Changing various chart messages" >Changing chart messages</a> . </td>
  </tr>
</table>
</body>
</html>
