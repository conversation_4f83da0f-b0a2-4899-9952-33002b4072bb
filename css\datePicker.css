table.jCalendar {
	border: 1px solid #000;
	background: #aaa;
    border-collapse: separate;
    border-spacing: 2px;
}
table.jCalendar th {
	background: #333;
	color: #fff;
	font-weight: bold;
	padding: 3px 5px;
}
table.jCalendar td {
	background: #ccc;
	color: #000;
	padding: 3px 5px;
	text-align: center;
}
table.jCalendar td.other-month {
	background: #ddd;
	color: #aaa;
}
table.jCalendar td.today {
	background: #666;
	color: #fff;
}
table.jCalendar td.selected {
	background: #f66;
	color: #fff;
}
table.jCalendar td.selected:hover {
	background: #f33;
	color: #fff;
}
table.jCalendar td:hover, table.jCalendar td.dp-hover {
	background: #fff;
	color: #000;
}
table.jCalendar td.disabled, table.jCalendar td.disabled:hover {
	background: #bbb;
	color: #888;
}

/* For the popup */

/* NOTE - you will probably want to style a.dp-choose-date - see how I did it in demo.css */

div.dp-popup {
	position: relative;
	background: #ccc;
	font-size: 10px;
	font-family: arial, sans-serif;
	padding: 2px;
	width: 171px;
	line-height: 1.2em;
}
div#dp-popup {
	position: absolute;
	z-index: 199;
}
div.dp-popup h2 {
	font-size: 12px;
	text-align: center;
	margin: 2px 0;
	padding: 0;
}
a#dp-close {
	font-size: 11px;
	padding: 4px 0;
	text-align: center;
	display: block;
}
a#dp-close:hover {
	text-decoration: underline;
}
div.dp-popup a {
	color: #000;
	text-decoration: none;
	padding: 3px 2px 0;
}
div.dp-popup div.dp-nav-prev {
	position: absolute;
	top: 2px;
	left: 4px;
	width: 100px;
}
div.dp-popup div.dp-nav-prev a {
	float: left;
}
/* Opera needs the rules to be this specific otherwise it doesn't change the cursor back to pointer after you have disabled and re-enabled a link */
div.dp-popup div.dp-nav-prev a, div.dp-popup div.dp-nav-next a {
	cursor: pointer;
}
div.dp-popup div.dp-nav-prev a.disabled, div.dp-popup div.dp-nav-next a.disabled {
	cursor: default;
}
div.dp-popup div.dp-nav-next {
	position: absolute;
	top: 2px;
	right: 4px;
	width: 100px;
}
div.dp-popup div.dp-nav-next a {
	float: right;
}
div.dp-popup a.disabled {
	cursor: default;
	color: #aaa;
}
div.dp-popup td {
	cursor: pointer;
}
div.dp-popup td.disabled {
	cursor: default;
}