<graph caption='3 Months - As on 04/02/04' yAxisMinValue='24' yAxisMaxValue='29' 
canvasBorderColor='DAE1E8' canvasBgColor='FFFFFF' bgColor='EEF2FB' 
numDivLines='9' divLineColor='DAE1E8' decimalPrecision='2' numberPrefix='$' showNames='1' bearBorderColor='E33C3C' bearFillColor='E33C3C' bullBorderColor='1F3165' baseFontColor='444C60' outCnvBaseFontColor='444C60' hoverCapBorderColor='DAE1E8' hoverCapBgColor='FFFFFF' rotateNames='0'> 

<categories font='' fontSize='10' fontColor='' verticalLineColor='' verticalLineThickness='1' verticalLineAlpha='100'>
<category Name='2004' xIndex='1' showLine='1'/>
<category Name='Feb' xIndex='31' showLine='1'/>
<category Name='March' xIndex='59' showLine='1'/>
</categories>

<data>
<set open='24.6' high='25.24' low='24.58' close='25.19' />
<set open='24.36' high='24.58' low='24.18' close='24.41' />
<set open='24.63' high='24.66' low='24.11' close='24.15' />
<set open='24.53' high='24.84' low='24.01' close='24.5' />
<set open='24.84' high='24.94' low='24.56' close='24.63' />
<set open='24.96' high='25.03' low='24.58' close='24.89' />
<set open='25.25' high='25.46' low='25.11' close='25.13' />
<set open='25.27' high='25.37' low='25.0999' close='25.18' />
<set open='25.33' high='25.43' low='25.06' close='25.16' />
<set open='25.38' high='25.51' low='25.23' close='25.38' />
<set open='25.2' high='25.78' low='25.07' close='25.09' />
<set open='25.66' high='25.8' low='25.35' close='25.37' />
<set open='25.77' high='25.97' low='25.54' close='25.72' />
<set open='26.31' high='26.35' low='25.81' close='25.83' />
<set open='26.23' high='26.6' low='26.2' close='26.35' />
<set open='26.37' high='26.42' low='26.21' close='26.37' />
<set open='26.35' high='26.55' low='26.22' close='26.37' />
<set open='26.63' high='26.69' low='26.35' close='26.39' />
<set open='26.65' high='26.72' low='26.5' close='26.7' />
<set open='26.48' high='26.62' low='26.35' close='26.53' />
<set open='26.63' high='26.65' low='26.41' close='26.5' />
<set open='26.89' high='26.99' low='26.61' close='26.7' />
<set open='26.6' high='26.95' low='26.55' close='26.88' />
<set open='26.75' high='26.76' low='26.4799' close='26.61' />
<set open='26.65' high='26.795' low='26.5' close='26.57' />
<set open='26.9' high='26.98' low='26.43' close='26.46' />
<set open='26.92' high='27.11' low='26.74' close='26.77' />
<set open='26.7' high='27.1' low='26.59' close='26.99' />
<set open='26.98' high='27.06' low='26.5' close='26.59' />
<set open='27.09' high='27.15' low='26.93' close='26.95' />
<set open='26.95' high='27.23' low='26.85' close='27.15' />
<set open='26.86' high='27.15' low='26.82' close='27.02' />
<set open='27.18' high='27.229' low='26.85' close='26.9' />
<set open='27' high='27.19' low='26.93' close='27.08' />
<set open='27.06' high='27.17' low='26.83' close='26.96' />
<set open='27.15' high='27.43' low='27.01' close='27.01' />
<set open='27.42' high='27.55' low='27.18' close='27.29' />
<set open='27.63' high='27.8' low='27.24' close='27.4' />
<set open='27.85' high='27.9' low='27.55' close='27.65' />
<set open='27.78' high='27.95' low='27.57' close='27.91' />
<set open='28.28' high='28.44' low='27.47' close='27.71' />
<set open='28.6' high='28.72' low='28.22' close='28.25' />
<set open='28.49' high='28.83' low='28.32' close='28.8' />
<set open='28.27' high='28.76' low='28.22' close='28.48' />
<set open='28.37' high='28.44' low='27.94' close='28.01' />
<set open='28.13' high='28.3' low='27.85' close='28.3' />
<set open='27.99' high='28.2' low='27.93' close='28.1' />
<set open='27.74' high='27.88' low='27.53' close='27.81' />
<set open='27.55' high='27.72' low='27.42' close='27.54' />
<set open='27.51' high='27.73' low='27.47' close='27.7' />
<set open='27.54' high='27.64' low='27.26' close='27.43' />
<set open='27.67' high='27.73' low='27.35' close='27.57' />
<set open='28.03' high='28.061' low='27.59' close='27.66' />
<set open='28.39' high='28.48' low='28' close='28.16' />
<set open='28.17' high='28.31' low='28.01' close='28.21' />
<set open='28.19' high='28.28' low='28.07' close='28.24' />
<set open='27.73' high='28.18' low='27.72' close='28.14' />
<set open='27.58' high='27.77' low='27.33' close='27.45' />
<set open='27.42' high='27.55' low='27.23' close='27.37' />
<set open='27.41' high='27.55' low='27.4' close='27.52' />
<set open='27.21' high='27.53' low='27.16' close='27.46' />
<set open='27.05' high='27.25' low='27' close='27.21' />

</data>
</graph>
