<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts and JavaScript &gt; How it Works? </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>FusionCharts offers tremendous integration capabilities with JavaScript. You can easily update the chart at client side by providing it XML data from JavaScript functions, without incurring any page refreshes. Using this method, you can integrate FusionCharts with your AJAX applications. Your AJAX wrappers can get the data from server, process it, convert it into XML and then update the charts already present on the page. </p>
      <p>Using a combination of FusionCharts and JavaScript, you can offer a seamless experience to your end users. Here, we'll discuss how to integrate both these technologies to yield the best results. </p>
      <p class="highlightBlock">The following sections assume that you already know how FusionCharts works and the XML data structure it accepts. If you do not already know this, please read the relevant sections of documentation, as examples in this section are based on concepts explained in those sections. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
