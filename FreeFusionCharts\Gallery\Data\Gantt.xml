<chart dateFormat='dd/mm/yyyy' hoverCapBorderColor='2222ff' hoverCapBgColor='e1f5ff' ganttWidthPercent='60' ganttLineAlpha='80' canvasBorderColor='024455' canvasBorderThickness='0' gridBorderColor='4567aa' gridBorderAlpha='20'>
<categories  bgColor='009999'>
	<category start='1/3/2005' end='31/8/2005' align='center' name='Residential Construction'  fontColor='ffffff' isBold='1' fontSize='16' />
</categories>
<categories  bgColor='4567aa' fontColor='ff0000'>
	<category start='1/3/2005' end='31/8/2005' align='center' name='Months'  alpha='' font='Verdana' fontColor='ffffff' isBold='1' fontSize='16' />
</categories>
<categories  bgColor='ffffff' fontColor='1288dd' fontSize='10' >
	<category start='1/3/2005' end='31/3/2005' align='center' name='March'  isBold='1' />
	<category start='1/4/2005' end='30/4/2005' align='center' name='April'  isBold='1' />
	<category start='1/5/2005' end='31/5/2005' align='center' name='May' isBold='1' />
	<category start='1/6/2005' end='30/6/2005' align='center' name='June' isBold='1'/>
	<category start='1/7/2005' end='31/7/2005' align='center' name='July' isBold='1' />
	<category start='1/8/2005' end='31/8/2005' align='center' name='August' isBold='1'/>
</categories>
<processes headerText='Tasks' fontColor='ffffff' fontSize='10' isBold='1' isAnimated='1' bgColor='4567aa'  headerVAlign='right' headerbgColor='4567aa' headerFontColor='ffffff' headerFontSize='16' width='80' align='left'>
	<process Name='Wrting' id='1' />
	<process Name='Signing' id='2' />
	<process Name='Financing' id='3' />
	<process Name='Permission' id='4' />
	<process Name='Plumbing' id='5' />
	<process Name='Terrace' id='6' />
	<process Name='Inspection' id='7' />
	<process Name='Wood Work' id='8' />
	<process Name='Interiors' id='9' />
	<process Name='Shifting' id='10' />
</processes>
<dataTable showProcessName='1' nameAlign='left' fontColor='000000' fontSize='10' isBold='1' headerBgColor='00ffff' headerFontColor='4567aa' headerFontSize='11' vAlign='right' align='left'>
	<dataColumn width='70' headerfontcolor='ffffff' headerBgColor='4567aa' bgColor='eeeeee'  headerColor='ffffff' headerText='Start' isBold='0'>
		<text label='7/3/2005' /> 
		<text label='6/4/2005' />
		<text label='1/5/2005' />
		<text label='13/5/2005' />
		<text label='2/5/2005' />
		<text label='1/6/2005' />
		<text label='15/6/2005' /> 
		<text label='22/6/2005' />
		<text label='18/6/2005' />
		<text label='15/7/2005' />
	</dataColumn>
	<dataColumn width='70' headerfontcolor='ffffff'  bgColor='eeeeee' headerbgColor='4567aa'  fontColor='000000' headerText='Finish' isBold='0'>
		<text label='22/4/2005' /> 
		<text label='12/5/2005' />
		<text label='2/6/2005' />
		<text label='19/6/2005' /> 
		<text label='19/6/2005' />
		<text label='19/7/2005' />
		<text label='11/8/2005' />
		<text label='5/8/2005' /> 
		<text label='22/7/2005' />
		<text label='11/8/2005' />
	</dataColumn>

	<dataColumn align='center' headerfontcolor='ffffff'  headerbgColor='4567aa'  bgColor='eeeeee' headerText='Dur.' width='35' isBold='0'>
		<text label='150' /> 
		<text label='340' />
		<text label='60' />
		<text label='20' /> 
		<text label='30' />
		<text label='45' />
		<text label='40' />
		<text label='102' /> 
		<text label='60' />
		<text label='30' />
		<text label='90' />
		<text label='30' />
	</dataColumn>
	<dataColumn headerbgColor='4567aa' headerfontcolor='ffffff'  align='right' fontColor='000000' bgColor='4567aa' bgAlpha='25' headerText='Cost' >
		<text label='$400' /> 
		<text label='$890' />
		<text label='$1234' />
		<text label='$230' /> 
		<text label='$450' />
		<text label='$120' />
		<text label='$1780' />
		<text label='$3330' /> 
		<text label='$890' />
		<text label='$1110' />
		<text label='$260' />
		<text label='$460' />
	</dataColumn>

</dataTable>
<tasks  width='10' >
	<task name='Planned' processId='1' start='7/3/2005' end='18/4/2005' id='1-1' color='4567aa' height='10' topPadding='5' animation='0'/>
	<task name='Actual' processId='1' start='7/3/2005' end='22/4/2005' id='1' color='cccccc' alpha='100'  topPadding='19' height='10' />
	<task name='Planned' processId='8' start='22/6/2005' end='29/7/2005' id='2-1' color='4567aa' alpha='100'  height='10' topPadding='5' animation='0'/>
	<task name='Actual' processId='8' start='22/6/2005' end='5/8/2005' id='2' color='cccccc' alpha='100'  height='10' topPadding='19'/>
	<task name='Planned' processId='2' start='6/4/2005' end='2/5/2005' id='3-1' color='4567aa' height='10' topPadding='5' animation='0'/>
	<task name='Actual' processId='2' start='6/4/2005' end='12/5/2005' id='3' color='cccccc' alpha='100'  isAnimated='1' height='10' topPadding='19'/>
	<task name='Planned' processId='9' start='18/6/2005' end='21/7/2005' id='4-1' color='4567aa' height='10' topPadding='5' animation='0'/>
	<task name='Actual' processId='9' start='18/6/2005' end='22/7	/2005' id='4' color='cccccc' alpha='100'  isAnimated='1' height='10' topPadding='19'/>
	<task name='Planned' processId='3' start='1/5/2005' end='2/6/2005' id='5-1' color='4567aa' height='10' topPadding='5' animation='0'/>
	<task name='Actual' processId='3' start='1/5/2005' end='2/6/2005' id='5' color='cccccc' height='10' topPadding='19'/>
	<task name='Planned' processId='4' start='11/5/2005' end='12/6/2005' id='6-1' color='4567aa' height='10' topPadding='5' animation='0'/>
  	<task name='Actual' processId='4' start='13/5/2005' end='19/6/2005' id='6' color='cccccc'  height='10' topPadding='19'/>
	<task name='Planned' processId='5' start='1/5/2005' end='12/6/2005' id='7-1' color='4567aa' height='10' topPadding='5' animation='0'/>
  	<task name='Actual' processId='5' start='2/5/2005' end='19/6/2005' id='7' color='cccccc'  height='10' topPadding='19'/>
	<task name='Planned' processId='6' start='1/6/2005' end='12/7/2005' id='8-1' color='4567aa' height='10' topPadding='5' animation='0'/>
  	<task name='Actual' processId='6' start='1/6/2005' end='19/7/2005' Id='8' color='cccccc' height='10' topPadding='19'/>
	<task name='Planned' processId='7' start='11/6/2005' end='7/8/2005' Id='9-1' color='4567aa' height='10' topPadding='5' animation='0'/>
  	<task name='Actual' processId='7' start='15/6/2005' end='11/8/2005' Id='9' color='cccccc' height='10' topPadding='19'/>
	<task name='Planned' processId='10' start='11/7/2005' end='7/8/2005' Id='10-1' color='4567aa' height='10' topPadding='5' animation='0'/>
  	<task name='Actual' processId='10' start='15/7/2005' end='11/8/2005' Id='10' color='cccccc' height='10' topPadding='19'/>

</tasks>
<connectors>
	<connector fromTaskId='3' toTaskId='5' color='4567aa' thickness='2' fromTaskConnectStart='1'/>
	<connector fromTaskId='8' toTaskId='9' color='4567aa' thickness='2' fromTaskConnectStart='1'/>
</connectors>

</chart>