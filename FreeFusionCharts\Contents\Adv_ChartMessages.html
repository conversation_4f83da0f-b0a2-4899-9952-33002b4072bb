<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Changing Chart Messages </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>FusionCharts Free allows you to change the various messages that gets displayed to the user like &quot;No data to display&quot;, &quot;Loading Chart&quot;, &quot;Retrieving data&quot; etc.</p>
      <p>The following attributes define the different messages for the chart:  </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><table width="98%" border="1" cellpadding="2" cellspacing="0" bordercolor="#f1f1f1">
      <tr>
        <td width="25%" class="header">Attribute Name </td>
        <td class="header">What message it controls </td>
      </tr>
      <tr>
        <td width="25%" class="codeInline">PBarLoadingText</td>
        <td class="text">Loading Chart. Please Wait</td>
      </tr>
      <tr>
        <td width="25%" class="codeInline">XMLLoadingText</td>
        <td class="text">Retrieving Data. Please Wait</td>
      </tr>
      <tr>
        <td width="25%" class="codeInline">ParsingDataText</td>
        <td class="text">Reading Data. Please Wait</td>
      </tr>
      <tr>
        <td class="codeInline">ChartNoDataText</td>
        <td class="text">No data to display.</td>
      </tr>
      
    </table></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>Let's quickly see an example where we change the chart's no data to display message. This can be effectively useful when you want to start the chart with empty data and then populate data on a user interaction.</p>
    <p>To change the &quot;No data to  display message&quot;, you'll need to use the following HTML code: </p></td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock"><p>&lt;div id=&quot;chart1div&quot;&gt;<br />
      &nbsp;&nbsp;&nbsp;This text is replaced by the chart<br />
  &lt;/div&gt;<br />
  &lt;script type=&quot;text/javascript&quot;&gt;<br />
      &nbsp;&nbsp;&nbsp;var chart1 = new FusionCharts(&quot;<strong>FCF_Column2D.swf?ChartNoDataText=Please select a record above</strong>&quot;, &quot;ChId1&quot;, &quot;300&quot;, &quot;250&quot;);<br />
        &nbsp;&nbsp;&nbsp;chart1.setDataXML(&quot;&lt;graph&gt;&lt;/graph&gt;&quot;);<br />
        &nbsp;&nbsp;&nbsp;chart1.render(&quot;chart1div&quot;);<br />
  &lt;/script&gt;</p>    </td>
  </tr>
  <tr>
    <td valign="top" class="text">In the above code, we're first changing the &quot;No data to display..&quot; message of the chart. Thereafter, we're initializing the chart with empty <span class="codeInline">&lt;graph&gt;</span> element. When you run this chart, you'll get the following message instead of the normal message, which is way more intuitive for the user: </td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/AppMessage.gif" width="232" height="126" class="imageBorder" /></td>
  </tr>
</table>
</body>
</html>
