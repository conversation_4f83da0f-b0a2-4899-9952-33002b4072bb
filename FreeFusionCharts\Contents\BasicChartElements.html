<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Creating your first Chart &gt; Basic Chart Elements</h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>Here, we&#8217;ll see the various chart elements 
      that constitute a chart. The following images would help you interpret the 
      various sections of the chart. </p></td>
  </tr>
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="text"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="0">
        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Chart Background and Canvas</td>
        </tr>
        <tr valign="top" class="text"> 
         <td>&nbsp;</td>
         <td>For a 3D chart, the canvas and background can be shown as under. 
		  You can change the colors and set the visibility of all the three elements 
		  below to false using XML attributes.		 </td>
        </tr>
        <tr valign="top" class="text"> 
			<td>&nbsp;</td>  
			<td><img src="Images/ChartElements1.gif" width="500" height="264"  class="imageBorder"></td>  
		</tr>
		
        <tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
        <tr valign="top" class="text"> 
         <td>&nbsp;</td>
         <td>For a 2D chart, the canvas and background is shown below. 
      You can set the canvas background color, background alpha, canvas border 
      color, canvas border thickness, canvas background color via XML.</td>
        </tr>
        <tr valign="top" class="text"> 
			<td>&nbsp;</td>  
			<td><img src="Images/ChartElements2.gif" width="421" height="292"  class="imageBorder"></td>  
		</tr>
		
        <tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
        <tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
        <tr valign="middle" class="text"> 
          <td valign="top"><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Chart Labels- caption, sub caption, x-axis label, y-axis label, y-axis upper limit, y-axis lower limit, number Prefix, number Suffix etc. </td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td><img src="Images/ChartElements3.gif" width="580" height="295"  class="imageBorder" /></td>
        </tr>
        <tr valign="middle" class="text"> 
          <td>&nbsp;</td>
          <td class="textBold">&nbsp;</td>
        </tr>
        <tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>

        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Div lines, trend lines and zero plane</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td><img src="Images/ChartElements4.gif" width="589" height="250" class="imageBorder" /></td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
        <tr valign="top" class="text"> 
          <td width="15" valign="middle"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Alternating Colored Horizontal and vertical grids</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>You can choose colors and alphas for alternating horizontal and vertical grids.</td>
        </tr>
        <tr valign="top" class="text">
          <td>&nbsp;</td>
          <td><img src="Images/ChartElements5.gif" width="530" height="250"  class="imageBorder" /></td>
        </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
        <tr valign="top" class="text">
          <td><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Hover Caption Box, hover caption background, hover caption border, hover caption data separator character.</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>You can define various properties for the hover caption 
        box too - like border color, background color, separator character etc.</td>
        </tr>
		<tr class="text"> <td>&nbsp;</td>   <td><img src="Images/ChartElements6.gif" width="247" height="197"  class="imageBorder" /></td>  
		</tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		        <tr valign="top" class="text">
          <td><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Line, Scatter, Radar Chart Anchors</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>You can define a lot of properties for the anchors like 
        shape, radius, border color, thickness, alpha, background color, background 
        alpha etc. </td>
        </tr>
		<tr class="text"> <td>&nbsp;</td>   <td><img src="Images/ChartElements7.gif" class="imageBorder" /></td>  
		</tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		        <tr valign="top" class="text">
          <td><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Chart Legend </td>
        </tr>
		<tr class="text"> <td>&nbsp;</td>   <td><img src="Images/ChartElements8.gif" width="433" height="424"  class="imageBorder" /></td>  
		</tr>
		<tr class="text"> <td>&nbsp;</td>   <td>&nbsp;</td>  </tr>
		<tr class="text"> <td>&nbsp;</td>   <td>And now that you are familiar with the various elements of 
      the graph, let&#8217;s have a look at the XML attributes that control them.</td>  
		</tr>
		
      </table></td>
  </tr>
</table>
</body>
</html>
