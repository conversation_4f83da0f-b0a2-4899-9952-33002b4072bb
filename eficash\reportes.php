<script type="text/javascript" src="js/jquery.tablesorter.min.js"></script> 
<script language="JavaScript" src="FreeFusionCharts/JSClass/FusionCharts.js"></script>

<script id="demo" type="text/javascript">
$(document).ready(function() {
	$("#mainReportes").hide();
	// Desactivado CP-1336 
// 	$("#btnReporteMovEficash").click(function (){ 
// 		$("#main").load("eficash/repMovEficash.php");
// 		$("#status").fadeOut("Fast");
// 	}); 
// 	$("#btnResumen121").click(function (){ 
// 		$("#main").load("eficash/resumenCta121.php");
// 		$("#status").fadeOut("Fast");
// 	}); 
// 	$("#btnDetalles121").click(function (){ 
// 		$("#main").load("eficash/detalleCta121.php");
// 		$("#status").fadeOut("Fast");
// 	}); 
// 	$("#btnCredInterfisa").click(function (){ 
// 		$("#main").load("eficash/creditoInterfisa.php");
// 		$("#status").fadeOut("Fast");
// });
// 	$("#btnDebitos").click(function (){ 
// 		$("#main").load("eficash/debitosEficash.php");
// 		$("#status").fadeOut("Fast");
// 	}); 
// 	$("#btnreporteSipap").click(function (){ 
// 		$("#main").load("eficash/reporteSIPAP.php");
// 		$("#status").fadeOut("Fast");
// 	}); 
});
</script>
<div id="titulo" align="center">
	<!-- Desactivado CP-1336
	<ul>
    	<li>
        	<b>Generaci&oacute;n de reportes para Sub Red Pronet.</b>
        </li>
    </ul>
</div>
<div id="interna" align="center">
          <table>
	        <tr>
            	<td><input type="button" value="Movimientos Eficash" id="btnReporteMovEficash" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
               </tr>
	        <tr>
            	<td><input type="button" value="Resumen de Movimientos Cta 121" id="btnResumen121" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
            </tr>
	        <tr>
            	<td><input type="button" value="Detalle por Movimientos Cta 121" id="btnDetalles121" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
                
        </table>
    <table>
	     <tr>
            	<td><input type="button" value="Credito al Recaudador Interfisa" id="btnCredInterfisa" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
            </tr>
	     <tr>
            	<td><input type="button" value="Debitos Eficash" id="btnDebitos" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
            </tr>
	     <tr>
            	<td><input type="button" value="Reporte SIPAP" id="btnreporteSipap" class="button"></td>            
            	<td><input type="button" value="Resumen con Saldo" id="btnResumenSaldo" class="button"></td>
            </tr>
    </table> -->
</div>