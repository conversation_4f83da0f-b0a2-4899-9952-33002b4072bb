<?php
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
?>
<script type="text/javascript">
    $(document).ready(function() {
        monedaSelect('');
$("#fecha").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});

    });
    //detectar inputs vacios

//fin



    function guardarDatos() {

        if ($("#moneda option:selected").val() == "") {
            alert("La Moneda es obligatoria");
            return false;
        }
        var emptyTextBoxes = $('input:text').not('#txt5,#txt6').filter(function() {
            return this.value == "";
        });
        var string = "Los Campos no pueden quedar vacios son: \n";
        emptyTextBoxes.each(function() {
            string += "\n" + this.id;
        });

        //alert(string.length);
        if (string.length < 1) {
            return

        }
        var codigo_operacion
        if ($('#operacion').val() == 1) {
            codigo_operacion = $('#operacion').val();
        } else {
            codigo_operacion = 0;

        }

        $('#formRangos').submit(
                function() {



                    var cadena = $(this).serialize();
                    datos = cadena;
                    datos = datos + '&id=58';
                    //alert(datos);
                    $.ajax({
                        type: "POST",
                        url: "commonFunctions.php",
                        data: datos,
                        success: function(msg) {
                            //alert(msg);
                            var resultado = msg.split("|");
                            //alert(resultado[1]);
                            if (resultado[1] == 1) {
                                $("#dvResultado").show();
                                $("#dvResultado").html("<div align='center' style='color:red;'>" + resultado[2] + "</div>");
                                //consultarRango();
                                $('#dvAgregar').hide(200);
                                $("#status").fadeOut("Fast");
                            } else if (resultado[1] == 0) {
                                $("#dvResultado").show();
                                $("#dvResultado").html("<h2 style='text-aling:center; color:red' >" + resultado[2] + "</h2>");
                            } else {
                                $("#status").fadeOut("Fast");
                                $("#dvResultado").html('Error #021 "' + resultado[2] + '",consulte con el administrador facilitandole este datos.');

                            }

                        }
                    });
//return del serilize        
                    return false;

                });

    }

    function borrarRango(codigo) {
// viene id
        if (id = '') {
            alert('No se pudo borrar no. Error de ID');
        }
        var datos = 'id=59&codigo=' + codigo + '';
        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: datos,
            success: function(msg) {
                var resultado = msg.split("|");
                if (resultado[1] == 1) {
                    alert(resultado[3]);
                    $("#dvResultado").hide(200);
                    $("#dvResultado").show(200);
                    consultarRango();
                } else if (resultado[1] == 0) {
                    $("#dvResultado").html('Error #021 "' + resultado[1] + '",consulte con el administrador facilitandole este datos.');
                }
            }});

    }
    function consultarRango() {
        $("#dvAgregar").hide(300);
        $("#dvResultado").show(300);
        $("#status").fadeIn("Fast");
        var fecha=$('#fecha').val()
        if(fecha == ''){
            alert("Fecha no puede estar vacio");
            exit;
        }
        var datos = "id=61&fecha="+fecha;
        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: datos,
            success: function(msg) {
                //alert(msg);
                var resultado = msg.split("|");
                //alert(resultado[1]);
                if (resultado[0] == 1) {
                    $("#dvResultado").html("<div align='center'>" + resultado[1] + "</div>");
                    $("#status").fadeOut("Fast");
                } else if (resultado[0] == 0) {
                    $("#status").fadeOut("Fast");
                    $("#dvResultado").html('<div style="color: #FF0000; font-size: 17px; font-weight: bold; text-align: center;">' + resultado[1] + '</div>');
              
                } else{
                    $("#status").fadeOut("Fast");
                    $("#dvResultado").html('Error #021 "' + resultado[1] + '",consulte con el administrador facilitandole este datos.');

                }

            }
        });
    }
    function monedaSelect(sele) {

        var datos = "funcion=select2&cod=1";
///alert(datos)
        $.ajax({
            type: "POST",
            url: "seprelad/commonFunctions.php",
            data: datos,
            success: function(msg) {
                //alert(msg);
                $('#dvmoneda').html(msg);

            }
        });

    }


    function AgregarRango() {

        $("#dvResultado").hide(200);
        $("#dvAgregar").hide(300);
        $("#dvAgregar").show(300);
        $("#operacion").val(0);
        $("#ocupacion").val('');
        $("#punto_riesgo").val('');
    }
    function editarRango(rango, codmoneda) {

        $("#moneda option[value=" + codmoneda + "]").attr("selected", true);
        //monedaSelect($("#moneda"+rango).text());
        $("#dvAgregar").show(300);
        $("#operacion").val(1);
        $("#codigo_operacion").val(rango);
        var desde = $("#dvocupacion" + rango).text();
        desde = $.trim(desde);

        var punto_riesgo = $("#puntaje" + rango + "").text();
        punto_riesgo = $.trim(punto_riesgo);
        $("#ocupacion").val(desde);

        //$("#moneda").val($("#moneda"+rango).text());
        //$("#moneda").val($("#moneda"+rango).text());
        $("#punto_riesgo").val(punto_riesgo);

    }
</script>
<center>
    <h2>Resumen de Movimientos Cta 121</h2>
    <table>
        <tr>
            <td> Fecha:</td>
            <td> <input type="text" name="fecha" id="fecha" > </td>
        </tr>
        <tr>
            <td> </td>
            <td>     <input type="button" value="Consultar" id="consultar" onclick="consultarRango()"/>
            </td>
        </tr>
    </table>

    <div id="dvAgregar" style="display: none;">
        <br>
        <form id="formRangos">
            <input type="hidden" value="0" name="operacion" id="operacion">
            <input type="hidden" value="0" name="codigo_operacion" id="codigo_operacion">
            <table>

                <tr>
                    <td>Ocupación</td>
                    <td><input type="text" name="ocupacion" id="ocupacion"/></td>
                </tr>


                <tr>
                    <td>Puntaje Riesgo</td>
                    <td><input type="text" name="puntoR" id="punto_riesgo"/></td>
                </tr>
                <tr>
                    <td></td>
                    <td><input type="submit" id="enviando" value="Guardar" onclick="guardarDatos()" /></td>
                </tr>
            </table>

        </form>

    </div>
    <div id="dvResultado"></div>

</center>