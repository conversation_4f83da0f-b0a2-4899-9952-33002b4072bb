
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr>
    <td><h2 class="pageHeader">Using FusionCharts with RoR &gt; UTF-8 Examples </h2></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In this section, we'll show you how to use FusionCharts and Ruby to plot charts from data containing <strong>UTF-8 characters</strong>. Using dataURL method we'll do the following: </p>
      <ul>
        <li><span class="text">Create a column3D chart to show the Monthly Sales with data from <strong>a XML file</strong>. </span></li>
        <li>Create a pie chart to show &quot;Production by Factory&quot; data from the <strong>database</strong>.</li>
      </ul>
      <p>We'll use Japanese text in this example but you could extend it to any left-to-right language by applying the same procedure. <strong>Before you proceed with the contents in this page, we recommend you to go through the section &quot;Basic Examples &quot;.</strong> </p>
        <p class="highlightBlock">All code discussed here is present in <br>      
	     <span class="codeInline">Controller : Download Package > Code > RoR > SampleApp &gt;  app > fusioncharts
    &gt; controllers > utf8_example_controller.rb</span>. <br> 
    <span class="codeInline">View : Download Package > Code > RoR > SampleApp &gt;  app > views > fusioncharts    &gt;  utf8_example</span> folder.<br>
    <span class="codeInline">View Helper Modules: Download Package > Code > RoR > SampleApp &gt;  lib &gt; fusioncharts_helper.rb </span></p> 
	  <p><span class="header">Plotting a chart with Japanese text from JapaneseData.xml </span><br>
	    <br>
      While using FusionCharts with UTF-8 characters, please remember the following:</p>
	  <ul>
        <li>dataURL method has to be used to get the xml.</li>
        <li>Rotated text cannot render UTF-8 characters. For example, UTF-8 characters in the rotated labels will not be rendered correctly.</li>
        <li>BOM has to present in the xml given as input to the chart. </li>
      </ul>
	  <p><span class="header">Setting up the charts for use </span><br>
      In our code, we've used the charts contained in Download Package > Code &gt;  RoR > SampleApp &gt;  public >FusionCharts folder. When you run your samples, you need to make sure that the SWF files are in proper location. Also the JapaneseData.xml file used in japanese_xmlfile_example action is present in the Download Package &gt; Code > RoR > public > Data folder.    
    <p>Let's now get to building our first example. In this example, we'll create a &quot;Monthly Unit Sales&quot; chart using dataURL method. For a start, we'll hard code our XML data in a physical XML document JapaneseData.xml, save it with UTF-8 encoding and then utilize it for our chart.
    <p class="text">Let's first have a look at the XML Data document:</p>
 <p class="codeBlock">

    &lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; ?&gt;<br>
    &lt;graph caption='&#26376;&#38291;&#36009;&#22770;' xAxisName='&#26376;' yAxisName='Units' decimalPrecision='0' formatNumberScale='0'&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#19968;&#26376;' value='462' color='AFD8F8' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#20108;&#26376;' value='857' color='F6BD0F' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#19977;&#26376;' value='671' color='8BBA00' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#22235;&#26376;' value='494' color='FF8E46' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#20116;&#26376;' value='761' color='008E8E' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#20845;&#26376;' value='960' color='D64646' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#19971;&#26376;' value='629' color='8E468E' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#20843;&#26376;' value='622' color='588526' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#20061;&#26376;' value='376' color='B3AA00' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#21313;&#26376;' value='494' color='008ED6' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#21313;&#19968;&#26376;' value='761' color='9D080D' /&gt;<br>
    &nbsp;&nbsp;&nbsp;&nbsp;&lt;set name='&#21313;&#20108;&#26376;' value='960' color='A186BE' /&gt;<br>
    &lt;/graph&gt;</p>
    <p class="text">
    The 


 XML document should begin with an <strong>XML declaration </strong> which specifies the version of XML being used and the encoding as seen in the above xml:<br>
 <span class="codeInline">&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; ?&gt; </span></p>
    <p class="text">As you would notice, the caption, x-axisname and names of the months in the xml are in Japanese. </p>
    <p class="text">Assuming that the Fusioncharts::Utf8ExampleController class has been created, we will define a function called japanese_xmlfile_example in this controller. The view used for this controller is the japanese_xmlfile_example.html.erb file. We will use the &quot;common&quot; layout for this view. It is important to have the &lt;meta&gt; tag in the head section of the html wih the charset defined as UTF-8 as shown below. This tag has been declared in the &quot;common.html.erb&quot; file, so that it is present in all the pages. </p>
    <p class="codeBlock">&lt;meta http-equiv=&quot;content-type&quot; content=&quot;text/html; charset=utf-8&quot; /&gt; </p>
    <p class="text">The code in the controller and view are given below. </p>    <p class="codeBlock"><b>Controller: <b>Fusioncharts::Utf8ExampleController</b><br/>
  Action: japanese_xmlfile_example<br>
      </b><span class="style1">class Fusioncharts::Utf8ExampleController &lt; ApplicationController</span><br>
       <span class="codeComment">#This is an example showing Japanese characters on the chart.<br>
  #Here, we've used a pre-defined JapaneseData.xml (contained in /Data/ folder)<br>
  #This action uses the dataURL method of FusionCharts. <br>
  #A view with the same name japanese_xmlfile_example.html.erb is present <br>
  #and it is this view, which gets rendered with the layout &quot;common&quot;.<br>
  #render_chart function from the helper is invoked to render the chart.<br>
  #The function itself has no code, all the work is done in the view.</span><br>
  def japanese_xmlfile_example<br>
  &nbsp;&nbsp;&nbsp;&nbsp;#The common layout for this view<br>
  &nbsp;&nbsp;&nbsp;&nbsp;render(:layout =&gt; &quot;layouts/common&quot;)<br>
  end<font color="blue"><br>
        <br/>
     </font> <b>View: japanese_xmlfile_example</b><strong>.html.erb</strong><br>
&lt;% @page_title=&quot;FusionCharts Free - UTF8 &#26085;&#26412;&#35486; (Japanese) Example&quot; %&gt;<br>
&lt;% @page_heading=&quot;UTF8 &#26085;&#26412;&#35486; (Japanese) Example&quot; %&gt;<br>
&lt;% @page_subheading=&quot;Basic example using data from pre-built JapaneseData.xml&quot; %&gt;<br>
&lt;%<br>
<span class="codeComment">#Create the chart - Column 3D Chart with data from /Data/JapaneseData.xml</span><br>
render_chart &quot;/FusionCharts/FCF_Column3D.swf&quot;, &quot;/Data/JapaneseData.xml&quot;, &quot;&quot;, &quot;JapaneseChart&quot;, 600, 300, false, false do-%&gt; <br>
&lt;% end -%&gt;</p>
     <p class="text">The code in the controller action and the view page are similar to the basic example simple_chart action and its view simple_chart.html.erb. Only here, we have used some Japanese text. That's the effort involved in rendering UTF-8 characters on the chart! The chart with Japanese text will look as shown:</p>
     <p class="text"><img src="Images/Code_JapXMLFileChart.jpg" width="577" height="274" class="imageBorder" /> </p>
     <p class="text">Let' move on to our next example where we get the data from the database and dynamically create the xml. </p>
     <p class="header">Plotting a chart with Japanese text from the database      
     <p class="text">Let us now create a chart with UTF characters present in the database. For this we will modify the database and add table to contain the Japanese data.     </p>
	 <p class="header">Database Configuration </p>
    <ol>
      <li>Please refer to <a href="Ruby_db.html">Plotting From Database</a> section for basic setup and configuration of the database.</li>
      <li>Ensure that the tables required for the UTF8 examples have been created. The required sql script &quot;create_utfexample_tables_data.sql&quot; is present in the <span class="codeInline">Download Package > Code > RoR > SampleApp &gt; db </span>folder. You could run this script in your mysql, (if not already done)- this will alter the database to use UTF8 as default character set, create the japanese_factory_masters and french_factory_masters tables and insert sample data.</li>
      </ol>    
    <p>Let's now shift our attention to the code that will interact with the database, fetch data and then render a chart. We will create an action japanese_dbexample, its .html.erb view, pie_data_japanese action and its corresponding pie_data_japanese view. </p>
    <p>The code contained in the controller action, view page are as shown:</p>
      <p class="codeBlock">        <b>Controller: Fusioncharts::Utf8ExampleController<br>
  Action: japanese_dbexample<br>
        </b><span class="codeComment">#In this example, we show how to use UTF8 characters in FusionCharts by connecting to a database <br>
#and using dataURL method. Here, the XML data<br>
#for the chart is generated in pie_data_japanese function.<br>
#The function itself does not contain any specific code to handle UTF8 characters.<br>
#NOTE: It's necessary to encode the dataURL if you've added parameters to it.</span><br>
<strong>def japanese_dbexample</strong><br>
<span class="codeComment"># Escape the URL using CGI.escape if it has parameters </span><br>
@str_data_url = &quot;/Fusioncharts/utf8_example/pie_data_japanese&quot;<br>
<br>
<span class="codeComment">#The common layout for this view</span><br>
render(:layout =&gt; &quot;layouts/common&quot;)<br>
<strong>end</strong><br>
<br>
<span class="codeComment"># Generates the xml with each factory's name and total output quantity.</span><br>
<span class="codeComment"># Factory name in japanese is obtained from JapaneseFactoryMaster.<br>
# Content-type for its view is set to text/xml and char-set to UTF-8.</span><br>
<strong>def pie_data_japanese<br>
</strong><br>
<strong>headers[&quot;Content-Type&quot;] = &quot;text/xml; charset=utf-8&quot; # xml content with charset=uttf-8</strong><br>
@factory_data = []<br>
        <br>
        <span class="codeComment"># Find all the factories</span><br>
  <strong>factory_masters = Fusioncharts::JapaneseFactoryMaster.find(:all)<br>
  </strong><br>
  <span class="codeComment"># For each factory, find the factory output details.</span><br>
  factory_masters.each do |factory_master|<br>
  factory_name = factory_master.name <br>
  total = 0.0<br>
  factory_master.factory_output_quantities.each do |factory_output|<br>
  <span class="codeComment"># Total the output quantity for a particular factory</span><br>
  total = total + factory_output.quantity<br>
  end<br>
  <span class="codeComment"># Append the array of factory name and total output quantity to the existing array @factory_data</span><br>
  @factory_data&lt;&lt;[factory_name,total]<br>
  <strong>end</strong><br>
  end<br>
  <br>
  <b>View:</b><strong> japanese_dbexample.html.erb</strong> <br>
  &lt;% @page_title=&quot;FusionCharts Free - UTF8 &#26085;&#26412;&#35486; (Japanese) Database Example&quot; %&gt;<br>
  &lt;% @page_heading=&quot; - UTF8 &#26085;&#26412;&#35486; (Japanese) Example With Data from Database&quot; %&gt;<br>
  &lt;% @page_subheading=&quot;&quot; %&gt;<br>
      &lt;%<br>
      <span class="codeComment">#Create the chart - Pie 3D Chart with dataURL as @str_data_url.</span><br>
  render_chart '/FusionCharts/FCF_Pie3D.swf',@str_data_url,'','FactorySum', 650, 450, false, false do-%&gt;<br>
  &lt;% end -%&gt;<br>
  <br>
  <strong>View: pie_data_japanese.html.erb</strong><br>
  &lt;% str_xml = get_UTF8_BOM #Function present in fusioncharts_helper library<br>
  <br>
str_xml +=&quot;&lt;?xml version='1.0' encoding='UTF-8'?&gt;&quot;<br>
str_xml +=&quot;&lt;graph caption='&#24037;&#22580;&#20986;&#21147;&#12524;&#12509;&#12540;&#12488;' subCaption='&#25968;&#37327;&#38754;&#12391;' decimalPrecision='0' showNames='1' numberSuffix=' units' pieSliceDepth='30' formatNumberScale='0'&gt;&quot;<br>
for item in @factory_data<br>
str_xml += &quot;&lt;set name='&quot;+item[0]+&quot;' value='&quot;+item[1].to_s+&quot;'/&gt;&quot;<br>
end<br>
str_xml+=&quot;&lt;/graph&gt;&quot;<br>
#Create the chart - Pie 3D Chart with data from strXML<br>
%&gt;&lt;%=str_xml%&gt;      </p>
      <p class="text">The action japanese_db_example and its view japanese_db_example.html.erb are very similar to default action of <span class="text"><b>Fusioncharts::DbDataUrlController</b></span> and corresponding default.html.erb view. Here we have used Japanese text for page heading etc. There is no UTF specific changes that need to be done to this action and its view. Let's move on to the pie_data_japanese action and its view. In pie_data_japanese action we have set the header with right content-type and charset as shown:</p>
      <p class="text"><span class="codeBlock">headers[&quot;Content-Type&quot;] = &quot;text/xml; charset=utf-8&quot;</span></p>
      <p class="text"><br>
      We have used the <span class="codeInline">JapaneseFactoryMaster</span> to find the factory names in Japanese. In the view pie_data_japanese.html.erb, we have built the xml as a string using <span class="codeInline">@factory_data</span> present in the controller. The steps followed in this page are:</p>
      <ul>
        <li> Call the function get_UTF8_BOM from fusioncharts_helper and store it in str_xml.</li>
        <li>Append the XML declaration with encoding:  <span class="codeInline">&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; ?&gt; </span>to str_xml.</li>
        <li>Append the actual xml content starting with the &lt;graph&gt; element. </li>
        <li>Output str_xml which contains the UTF8 BOM, XML declaration with encoding and the actual data xml. <br> 
        </li>
      </ul>      
      <p>When we view the chart in the browser, it would look like this: </p>
    <p>      
    <p><img src="Images/Code_JapDBChart.jpg" class="imageBorder" /> </p>
    <p class="highlightBlock">In <span class="codeInline">Download Package > Code > RoR > SampleApp &gt; app > controllers &gt; fusioncharts > utf8_example_controller.rb</span>, 
  we've more example codes for French language too, which have not been explained here, 
  as they're similar in concept. You can directly see the code and get more insight into it. </p></td>
</tr>
 
</table>
</body>
</html>
