<?php
/*FN: Este archivo debe ser incluido por ruta absoluta en los proyectos*/
	date_default_timezone_set('America/Asuncion');
	//putenv('ORACLE_HOME=/usr/lib/oracle/12.1/client64');
    //putenv('TNS_ADMIN=/etc/oracle');
    //putenv('LD_LIBRARY_PATH=/usr/lib/oracle/12.1/client64/lib:/lib:/usr/lib');

    putenv('ORACLE_HOME=/usr/lib/oracle/12.1/client64');
        putenv('TNS_ADMIN=/etc/oracle');
        putenv('LD_LIBRARY_PATH=/usr/lib/oracle/12.1/client64/lib:/lib:/usr/lib');



//validacionesWeb
//$oraUserValWeb = 'valweb';  
    //$oraUserValWeb = 'admin';  
    $oraUserValWeb = 'admin';  
//$oraPassValWeb = 'v4lw3b';
    //$oraPassValWeb = 'adminpronet';
    $oraPassValWeb = 'adminpronet';

$possUserValWeb ='valweb';
$possUserValWeb ='valweb';
$possPassValWeb='rsuv4lw3b2012lqs';

$soatUsu = 'soat';
$soatPass = 's04t';

//$possUserValWeb ='dba';
//$possPassValWeb='rsupanambilqs';



	$esquema = 0;
	global $wsHacienda;
	/*PagosWeb*/
	//$sr = '*************/dbdesa';
	$sr = 'devel';
//		$sr = "desarrollo";
//		$sr = 'dbdesa';
		///$bd = 'dbdesa';		
		$bd = 'devel';	
        // $ur = 'admin';
        // $ps = 'adminpronet';
        $ur = 'admin';
        $ps = 'adminpronet';
	// $urPosWeb = 'admin';
        // $psPosWeb = 'adminpronet';
	$urPosWeb = 'admin';
        $psPosWeb = 'adminpronet';
//		$urPosWeb = 'posweb';
//		$psPosWeb = 'w3bb0s_pr0n37';
//		$wsdlLoginPosWeb = 'http://*************:8080/GWClienteWebPos/ValidacionCajero?wsdl'; //Produccion
		$wsdlLoginPosWeb = 'http://*************:8080/GWClienteWebPos/ValidacionCajero?wsdl'; //Desarrollo

		//Parametros del autorizador.
		$ip_aut = '*************';
		$puerto_aut = '9300';
//                $ip_aut = '*************';
//                $puerto_aut = '9300';
		//$puerto_aut = '20000';
		$intentos_envio_autorizador = 2;
		$intentos_envio_ws = 3;
		//Parametros enviados en el POST/GET a ser controlados.
		$parametros = array("1"=>"id", "2"=>"ct", "3"=>"us", "4"=>"ps", "5"=>"ns", "6"=>"ip", "7"=>"se", "8"=>"dv");
		//1: Desarrollo 2: DESARROLLO.
		$esquema_ws = 1;	
		$nError = '<EMAIL>';		
		///$nError = '<EMAIL>';	
		$possConnect = "host=************* port=5432 dbname=bd_pronet2 user=postgres password=12345";		   		
//		$wsHacienda = "https://*************:8080/ServsDGRWS/ServsDGRService?wsdl";
	/*PagosWeb*/	

/*FN: Este archivo debe ser incluido por ruta absoluta en los proyectos*/
global $esquema, $notificaciones, $vinculoDesEquema, $sPara, $vinculoDesEquema, $APP_NAME; 
global $ERA, $SUCURSAL, $CENTRO, $nombreERA, $ENTIDAD, $logoChico, $logoGrande, $logoIndex;
$esquema = 1; $vinculoDesEquema = " "; 
$sPara = '<EMAIL>';
$notificaciones = '<EMAIL>';


/*solo activo mietras no ente en DESARROLLO la nueva version, borrar en la proxima actualizacion*/
$vinculoRedirect = "https://secure.ticpro.net/vinculo/"; 	 					
$mailFrom = '<EMAIL>';
$mailHost = '************';	
$mailName = 'Vinculo Desa';
$fire_host = "*************:/opt/firebird/examples/BD_SET_PRONET_1/bd_set.fdb"; 
$fire_user = "vinculo"; 
$fire_pass = "pr0n3t_w3b";
/*solo activo mietras no ente en DESARROLLO la nueva version, borrar en la proxima actualizacion*/

//$ora_host = '*************';	
//$ora_host = 'DB_DESA';	
///$ora_host = '*************/dbdesa';
//$ora_host = '*************/devel';	
$ora_host = '************/desamaverick.sub01091406381.prodnetworkvcn0.oraclevcn.com';
$ora_db = 'desamaverick';
$ora_user = 'fschein';
$ora_pass = 'FSchein#25';

$poss_driver = 'postgres';
$poss_host = '*************';
$poss_user = 'postgres';
$poss_pass = '12345';	
$poss_db = 'bd_pronet2';

$fire_host_ybaga = "*************:/opt/firebird/examples/BD_SET_PRONET_1/bd_set.fdb";	
//$fire_host_ybaga = "*************:/opt/firebird/examples/BD_SET_PRONET/bd_set.fdb"; 
$fire_user_ybaga = "vinculo"; 
$fire_pass_ybaga = "pr0n3t_w3b";

$fire_host_bbva = "*************:/opt/firebird/examples/BD_SET_BBVA/bd_set.fdb"; 
$fire_user_bbva = "vinculo"; 
$fire_pass_bbva = "pr0n3t_w3b";

$fire_host_interfisa = "*************:/opt/firebird/examples/BD_SET_INTERFISA/bd_set.fdb"; 
$fire_user_interfisa = "vinculo"; 
$fire_pass_interfisa = "pr0n3t_w3b";

$fire_host_pronet = "*************:/opt/firebird/examples/BD_SET_PRONET_1/bd_set.fdb"; 
$fire_user_pronet = "vinculo"; 
$fire_pass_pronet = "pr0n3t_w3b";

$fire_host_servifacil = "*************:/opt/firebird/examples/BD_SET_SERVIFACIL/bd_set.fdb"; 
$fire_user_servifacil = "vinculo"; 
$fire_pass_servifacil = "pr0n3t_w3b";

$fire_host_itapua = "*************:/opt/firebird/examples/BD_SET_ITAPUA/bd_set.fdb"; 
$fire_user_itapua = "SYSDBA"; 
$fire_pass_itapua = "pr0v1ncul0l33";

$fire_host_atlas = "*************:/opt/firebird/examples/BD_SET_ATLAS/bd_set.fdb"; 
$fire_user_atlas = "vinculo"; 
$fire_pass_atlas = "pr0n3t_w3b";


$mailHost = '************';	
$PATHSELLO = "https://secure.ticpro.net/sellos/";	

//GestEmi
//$oraUserGestEmi = 'admin';
//$oraPassGestEmi = 'adminpronet';

$oraUserGestEmi = 'admin';
$oraPassGestEmi = 'adminpronet';

if(!is_bool(strrpos(strtolower($_SERVER['PHP_SELF']), "interfisa"))){ //Pronet
	$ERA = "45"; $SUCURSAL = "425"; $CENTRO = "INTERFISA"; $nombreERA = "INTERFISA S.A."; $ENTIDAD = 65;  $TERMINAL = "01914120";
	$logoChico = "interfisaGente.png"; $logoGrande = "interfisa.JPG"; $logoIndex = "interfisa.JPG";
	$vinculoRedirect = "https://secure.ticpro.net/interfisa/vinculo/"; 	 						
	$mailFrom = '<EMAIL>';
	$mailName = 'Vinculo INTERFISA';
	$fire_host_entidad = $fire_host_interfisa;
	$fire_user_entidad = $fire_user_interfisa;
	$fire_pass_entidad = $fire_pass_interfisa;	
	$APP_NAME = 'Vinculo Interfisa';
	$SELLO = 'sello_interfisa.png';	
	$APP_NAME = 'Vinculo Interfisa';
	$SELLO = 'sello_interfisa.png';	
}elseif(!is_bool(strrpos(strtolower($_SERVER['PHP_SELF']), "bbva"))){
	$ERA = "45"; $SUCURSAL = "396"; $CENTRO = "BBVA"; $nombreERA = "BBVA PARAGUAY S.A."; $ENTIDAD = 854; $TERMINAL = "84600020";
	$logoChico = "bbvaChico.jpg"; $logoGrande = "bbvalogo.jpg"; $logoIndex = "bbvalogo.jpg";
	$vinculoRedirect = "https://secure.ticpro.net/bbva/vinculo/"; 	 							
	$mailFrom = '<EMAIL>';
	$mailName = 'Vinculo BBVA';
	$fire_host_entidad = $fire_host_bbva;
	$fire_user_entidad = $fire_user_bbva;
	$fire_pass_entidad = $fire_pass_bbva;	
	$APP_NAME = 'Vinculo BBVA';
	$SELLO = '';	
}elseif(!is_bool(strrpos(strtolower($_SERVER['PHP_SELF']), "servifacil"))){
	$ERA = "45"; $SUCURSAL = "459"; $CENTRO = "SERVIFACIL"; $nombreERA = "SERVIFACIL PARAGUAY S.A."; $ENTIDAD = 123; $TERMINAL = "83800620";
	$logoChico = "serviFacilChico.jpg"; $logoGrande = "serviFacil.png"; $logoIndex = "serviFacil.png";
	$vinculoRedirect = "https://secure.ticpro.net/servifacil/vinculo/"; 	 							
	$mailFrom = '<EMAIL>';
	$APP_NAME = 'Vinculo Servi Facil';	
	$mailName = $APP_NAME;
	$fire_host_entidad = $fire_host_servifacil;
	$fire_user_entidad = $fire_user_servifacil;
	$fire_pass_entidad = $fire_pass_servifacil;	
	$SELLO = 'sello_pronet.png';	
}elseif(!is_bool(strrpos(strtolower($_SERVER['PHP_SELF']), "atlas"))){
	$ERA = "45"; $SUCURSAL = "424"; $CENTRO = "BANCO ATLAS"; $nombreERA = "BANCO ATLAS"; $ENTIDAD = 67; $TERMINAL = "04920620";	
	//logos
	$logoChico = "logo_150_80.gif"; $logoGrande = "atlas229_112.jpg"; $logoIndex = "atlas229_112.jpg";	
	$vinculoRedirect = "https://secure.ticpro.net/atlas/vinculo/"; 	 							
	$mailFrom = '<EMAIL>';
	$APP_NAME = 'Vinculo Atlas';	
	$mailName = $APP_NAME;
	$fire_host_entidad = $fire_host_atlas;
	$fire_user_entidad = $fire_user_atlas;
	$fire_pass_entidad = $fire_pass_atlas;	
	$SELLO = 'sello_pronet.png';	
}elseif(!is_bool(strrpos(strtolower($_SERVER['PHP_SELF']), "itapua"))){
	$ERA = "45"; $SUCURSAL = "426"; $CENTRO = "ITAPUA"; $nombreERA = "BANCO ITAPUA S.A.E.C.A."; $ENTIDAD = 95; $TERMINAL = "41601620";
	$logoChico = "logoAquiPago.png"; $logoGrande = "aquiPago.png"; $logoIndex = "pronet_iso.gif";
	$vinculoRedirect = "https://secure.ticpro.net/itapua/vinculo/"; 	 							
	$mailFrom = '<EMAIL>';
	$APP_NAME = 'Vinculo Itapua';	
	$mailName = $APP_NAME;
	$fire_host_entidad = $fire_host_itapua;
	$fire_user_entidad = $fire_user_itapua;
	$fire_pass_entidad = $fire_pass_itapua;	
	$SELLO = 'sello_itapua.png';	
}else{
	$ERA = "45"; $SUCURSAL = "441"; $CENTRO = "AQUI PAGO"; $nombreERA = "PRONET S.A."; $ENTIDAD = 367;  $TERMINAL = "70000097";
	$logoChico = "logoAquiPago.png"; $logoGrande = "aquiPago.png"; $logoIndex = "pronet_iso.gif";
	$vinculoRedirect = "https://secure.ticpro.net/vinculo/"; 	 								
	$mailFrom = '<EMAIL>';
	$mailName = 'Vinculo PRONET';	
	$fire_host_entidad = $fire_host_pronet;
	$fire_user_entidad = $fire_user_pronet;
	$fire_pass_entidad = $fire_pass_pronet;
	$APP_NAME = 'Vinculo Pronet';
	$SELLO = 'sello_pronet.png';	
}	


/*$arrServiciosApi = array('3099','3100','3101','3102','3094','1992', '1595', '1995', '1596', '2006', '1628', '2098', '1925', '2004', '2005', '2141', '2142', '2381', '2391', '2394',
 '2390', '2401', '2382', '2565', '2566', '2853', '2854', '2855', '2856', '3231', '3057', '3475','3476', '3512','3513','3506' ,'3507', '3514','3515', '3379','3380','3510','3511',
 '3474','3536','3524','3525', '3107','3017','3018','3097','3098','3096','3121','3136','3137','3139','3140','3154','3900','3100','3101','3102', '3202', '3203', '3228','1420',
 '1421', '3229','3239','3240','3282','3283','3338','3328','3329','3359','3363','3368','3369','3192','3193', '3451','3452','3549','3550','3412','3413','3056','3057','3192','3193',
 '3404','3405','3472','3473','3453','3454','3477','3478','3479','3480','3483','3484','3481','3482','3495','3496','3489','3490','3498','3497','3487','3488','3493','3494','3491',
 '3492','3508','3509','3486','3485','3499','3500','3501', '2500','2501','2502','2503','2518','2519','3502','3503','3545','3546','3531','3530','3439','3440','3534','3535','3537',
 '3538','3543','3544','3520','3521','3516','3517','3372','3373','3374','3375','3455','3456','3532','3533','3565','3566','3564','3466','3467','3463','3465','3518','3519','3556',
 '3557','3573','3574','3560','3561','3584','3585','3828','3829','3807','3808','3773','3774','3818','3819','3859','3860','3861','3954','3955','3970','3971','3522','3523','3578',
 '3579','3522','3523','3606','3588','3597','3599','3526','3527','3459','3460','3616','3617','3431','3432','3504','3505','3591','3592','3618','3619','3326','3327','3602','3603',
 '3604','3605','3642','3643','1680','1679','3586','3587','3562','3563','3651','3652','3593','3594','3595','3596','2190','2191','3941','3942','3824','3825','3952','3953','3649',
 '3650','3693','3694','3634','3635','3653','3654','3697','3698','3029','3030','3659','3660','3620','3621','3622','3623','3687','3688','3539','3540','3703','3704','3640','3641',
 '3672','3675','3676','3677','3600','3601','3678','3679','2644','2645','3680','3681','3682','3683','3655','3656','3714','3713','3783','3784','3607','3608','3609','3610','3580',
 '3581','3582','3631','3632','3633','3691','3692','3701','3702','3589','3590','3638','3639','3721','3722','3631','3632','3633','3717','3718','3663', '3664','3727','3728','3730',
 '3744','2455','2456','3684','3685','3742','3743','3667','3668','3669','3670','3757','3758','3731','3732','3719','3720','3699','3700','3759','3760','3644','3705','3706','3541',
 '3542','3767','3768','3547','3548','3769','3770','3754','3755','3645','3646','3735','3736','3661','3662','3689','3690','1783','1784','1410','1411','3765','3766','3414','3416',
 '3435','3567','3568','3745', '3746','3626','3627','3628','3629','3630','3762','3789','3790','3797','3798','3799','3771','3772','3775','3776','3791','3792','3031','3032','3800',
 '3801','3763','3764','3779','3780','3749','3750','3624','3625','3636','3637','3794','3795','3796','3793','3781','3782','3715','3716','3793','3794','3795','3796','3820','3821',
 '3826','3827','3021','3022','3837','3838','3841','3842','3832','3833','3843','3844','3845','3846','3855','3802','3665','3666','3835','3836','3423','3424','3858','3862','3863',
'3864','3865','3816','3817','3873','3874','3423','3424','3869','3870','3871','3872','3847','3848','3468','3469','3470','3471','3723','3724','3725','3726','3707','3708','3709',
'3711','3712','3392','3393','3962','3963','3964','3965','3856','3857','3943','3944','3853','3854','3809','3810','3811','3812','3830','3831','3966','3967','3968','3969','3973',
'3957','3956','3839','3840','3972','3851','3852','3974','3975','2631','2632','3180','3181','3988','3950','3951','3976','3977','3989','3990','3881','3882','3945','3946','3947',
'3876','3877','4000','4001','4002','4003','3813','3814','3733','3734','3948','3949','3285','3286','3287','3158','3159','3160','3984','3985','3878','3879','3986','3987','4010',
'4011','3997','3998','3880','3883','3657','3658','2469','4087','4088','2825','2826','2827','2828','2829','4227','4228','4138','4139','4224','4225','4335','4336','4237','4238',
'4239','4240','4358','4293','4294','3347','3348','4337','4338','4237','4238','4239','4240','4358');*/

//$ip_NA = '*************';
$ip_NA = 'autorizador1.com.py';
$puerto_NA = '30400';