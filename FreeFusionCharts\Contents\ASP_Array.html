<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Using FusionCharts with ASP &gt; Charting Data from an Array </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>In this section, we'll show you how to use FusionCharts  and ASP to plot charts from data contained in ASP arrays. We'll cover the following examples here:</p>
      <ul>
        <li>Creating a single series chart from data contained in arrays</li>
        <li>Creating a multi-series chart from data contained in arrays </li>
      </ul>
      <p><strong>Before you go further with this page, we recommend you to please see the previous section &quot;<a href="ASP_BasicExample.html">Basic Examples</a>&quot; as we start off from concepts explained in that page. </strong></p>
      <p class="highlightBlock">The code examples contained in this page are present in <span class="codeInline">Download Package &gt; Code &gt; ASP &gt; ArrayExample</span> folder. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Creating a single series chart from data contained in arrays</td>
  </tr>
  <tr>
    <td valign="top" class="text">The code to create a single series chart is contained in <span class="codeInline">SingleSeries.asp</span> and can be listed as under: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock"><p>&lt;%@ Language=VBScript %&gt;<br />
      &lt;HTML&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;	FusionCharts Free - Array Example using Single Series Column 3D Chart&lt;/TITLE&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;/HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;!-- #INCLUDE FILE=&quot;../Includes/FusionCharts.asp&quot; --&gt;<br/>
  <span class="codeComment">&nbsp;&nbsp;&nbsp;'File having a list of colors to be applied to each column (using getFCColor() function</span>)<br />
  &nbsp;&nbsp;&nbsp;&lt;!-- #INCLUDE FILE=&quot;../Includes/FC_Colors.asp&quot; --&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;BODY&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;%<br />
      &nbsp;&nbsp;<span class="codeComment">&nbsp;'In this example, we plot a single series chart from data contained<br />
&nbsp;&nbsp;&nbsp;'in an array. The array will have two columns - first one for data label<br />
&nbsp;&nbsp;&nbsp;'and the next one for data values.<br />
&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;'Let's store the sales data for 6 products in our array. We also store<br />
&nbsp;&nbsp;&nbsp;'the name of products. </span><br />
      &nbsp;&nbsp;&nbsp;Dim arrData(6,2)<br />
      <span class="codeComment">&nbsp;&nbsp;&nbsp;'Store Name of Products</span><br />
      &nbsp;&nbsp;&nbsp;arrData(0,1) = &quot;Product A&quot;<br />
      &nbsp;&nbsp;&nbsp;arrData(1,1) = &quot;Product B&quot;<br />
      &nbsp;&nbsp;&nbsp;arrData(2,1) = &quot;Product C&quot;<br />
      &nbsp;&nbsp;&nbsp;arrData(3,1) = &quot;Product D&quot;<br />
      &nbsp;&nbsp;&nbsp;arrData(4,1) = &quot;Product E&quot;<br />
      &nbsp;&nbsp;&nbsp;arrData(5,1) = &quot;Product F&quot;<br />
      <br />
      &nbsp;<span class="codeComment">&nbsp;&nbsp;'Store sales data</span><br />
      &nbsp;&nbsp;&nbsp;arrData(0,2) = 567500<br />
      &nbsp;&nbsp;&nbsp;arrData(1,2) = 815300<br />
      &nbsp;&nbsp;&nbsp;arrData(2,2) = 556800<br />
      &nbsp;&nbsp;&nbsp;arrData(3,2) = 734500<br />
      &nbsp;&nbsp;&nbsp;arrData(4,2) = 676800<br />
      &nbsp;&nbsp;&nbsp;arrData(5,2) = 648500</p>
      <p> &nbsp;<span class="codeComment">&nbsp;&nbsp;'Now, we need to convert this data into XML. We convert using string concatenation.</span><br />
        &nbsp;&nbsp;&nbsp;Dim strXML, i<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Initialize &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = &quot;&lt;graph caption='Sales by Product' numberPrefix='$' formatNumberScale='0' decimalPrecision='0'&gt;&quot;<br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Convert data to XML and append</span><br />
        &nbsp;&nbsp;&nbsp;For i=0 to UBound(arrData)-1<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;set name='&quot; &amp; arrData(i,1) &amp; &quot;' value='&quot; &amp; arrData(i,2) &amp; &quot;' color='&quot; &amp; getFCColor() &amp; &quot;' /&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;Next<br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Close &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;/graph&gt;&quot;<br />
  <br />
        <span class="codeComment">'Create the chart - Column 3D Chart with data contained in strXML</span><br />
        Call renderChart(&quot;../../FusionCharts/FCF_Column3D.swf&quot;, &quot;&quot;, strXML, &quot;productSales&quot;, 600, 300)<br />
        %&gt;<br />
  &lt;/BODY&gt;<br />
  &lt;/HTML&gt;</p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above example, we first include <span class="codeInline">FusionCharts.js</span> file to enable us embed the chart using JavaScript. We also include <span class="codeInline">FusionCharts.asp</span> to help us easily embed the charts.</p>
    <p>Thereafter, we define an ASP array <span class="codeInline">arrData</span> to store sales data for 6 different products. The array has two columns - first one for data label and the next one for data values. </p>
    <p>We define a variable <span class="codeInline">strXML</span> to store the entire XML data. To build the XML, we iterate through the array and using string concatenation. Finally, we render the chart using <span class="codeInline">renderChart()</span> function and pass <span class="codeInline">strXML</span> as <span class="codeInline">dataXML</span>. </p>
    <p>We include <span class="codeInline">FC_Colors.asp</span>, which contains the default color set for the FusionCharts. These  colors in Hex code are optimized to give a snazzy look to the charts.The <span class="codeInline">getFCColor()</span> function in cyclic iteration returns these color values.</p>
    <p>When you view the chart, you'll see a chart as under: </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_ArraySS.jpg" /></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Creating a multi-series chart from data contained in arrays </td>
  </tr>
  <tr>
    <td valign="top" class="text">Let us now create a multi-series chart from data contained in arrays. We create a file <span class="codeInline">MultiSeries.asp</span> with the following code: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock"><p>&lt;%@ Language=VBScript %&gt;<br />
      &lt;HTML&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;	FusionCharts Free - Array Example using Multi Series Column 3D Chart	&lt;/TITLE&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;/HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;!-- #INCLUDE FILE=&quot;../Includes/FusionCharts.asp&quot; --&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;BODY&gt;</p>
      <p>&nbsp;&nbsp;&nbsp;&lt;%<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'In this example, we plot a multi series chart from data contained<br />
&nbsp;&nbsp;&nbsp;'in an array. The array will have three columns - first one for data label (product)<br />
&nbsp;&nbsp;&nbsp;'and the next two for data values. The first data value column would store sales information<br />
&nbsp;&nbsp;&nbsp;'for current year and the second one for previous year.<br />
  <br />
&nbsp;&nbsp;&nbsp;'Let's store the sales data for 6 products in our array. We also store<br />
&nbsp;&nbsp;&nbsp;'the name of products. </span><br />
        &nbsp;&nbsp;&nbsp;Dim arrData(6,3)<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Store Name of Products</span><br />
        &nbsp;&nbsp;&nbsp;arrData(0,1) = &quot;Product A&quot;<br />
        &nbsp;&nbsp;&nbsp;arrData(1,1) = &quot;Product B&quot;<br />
        &nbsp;&nbsp;&nbsp;arrData(2,1) = &quot;Product C&quot;<br />
        &nbsp;&nbsp;&nbsp;arrData(3,1) = &quot;Product D&quot;<br />
        &nbsp;&nbsp;&nbsp;arrData(4,1) = &quot;Product E&quot;<br />
        &nbsp;&nbsp;&nbsp;arrData(5,1) = &quot;Product F&quot;<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Store sales data for current year</span><br />
        &nbsp;&nbsp;&nbsp;arrData(0,2) = 567500<br />
        &nbsp;&nbsp;&nbsp;arrData(1,2) = 815300<br />
        &nbsp;&nbsp;&nbsp;arrData(2,2) = 556800<br />
        &nbsp;&nbsp;&nbsp;arrData(3,2) = 734500<br />
        &nbsp;&nbsp;&nbsp;arrData(4,2) = 676800<br />
        &nbsp;&nbsp;&nbsp;arrData(5,2) = 648500<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Store sales data for previous year</span><br />
        &nbsp;&nbsp;&nbsp;arrData(0,3) = 547300<br />
        &nbsp;&nbsp;&nbsp;arrData(1,3) = 584500<br />
        &nbsp;&nbsp;&nbsp;arrData(2,3) = 754000<br />
        &nbsp;&nbsp;&nbsp;arrData(3,3) = 456300<br />
        &nbsp;&nbsp;&nbsp;arrData(4,3) = 754500<br />
        &nbsp;&nbsp;&nbsp;arrData(5,3) = 437600</p>
      <p> &nbsp;<span class="codeComment">&nbsp;&nbsp;'Now, we need to convert this data into multi-series XML. <br />
&nbsp;&nbsp;&nbsp;'We convert using string concatenation.<br />
&nbsp;&nbsp;&nbsp;'strXML - Stores the entire XML<br />
&nbsp;&nbsp;&nbsp;'strCategories - Stores XML for the &lt;categories&gt; and child &lt;category&gt; elements<br />
&nbsp;&nbsp;&nbsp;'strDataCurr - Stores XML for current year's sales<br />
&nbsp;&nbsp;&nbsp;'strDataPrev - Stores XML for previous year's sales</span><br />
        &nbsp;&nbsp;&nbsp;Dim strXML, strCategories, strDataCurr, strDataPrev, i<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Initialize &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = &quot;&lt;graph caption='Sales by Product' numberPrefix='$' decimalPrecision='0'  &gt;&quot;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Initialize &lt;categories&gt; element - necessary to generate a multi-series chart</span><br />
        &nbsp;&nbsp;&nbsp;strCategories = &quot;&lt;categories&gt;&quot;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Initiate &lt;dataset&gt; elements</span><br />
        &nbsp;&nbsp;&nbsp;strDataCurr = &quot;&lt;dataset seriesName='Current Year' color='AFD8F8' &gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;strDataPrev = &quot;&lt;dataset seriesName='Previous Year'  color='F6BD0F' &gt;&quot;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Iterate through the data </span><br />
        &nbsp;&nbsp;&nbsp;For i=0 to UBound(arrData)-1<br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;'Append &lt;category name='...' /&gt; to strCategories</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strCategories = strCategories &amp; &quot;&lt;category name='&quot; &amp; arrData(i,1) &amp; &quot;' /&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;'Add &lt;set value='...' /&gt; to both the datasets</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strDataCurr = strDataCurr &amp; &quot;&lt;set value='&quot; &amp; arrData(i,2) &amp; &quot;' /&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strDataPrev = strDataPrev &amp; &quot;&lt;set value='&quot; &amp; arrData(i,3) &amp; &quot;'  /&gt;&quot; <br />
        &nbsp;&nbsp;&nbsp;Next<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Close &lt;categories&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strCategories = strCategories &amp; &quot;&lt;/categories&gt;&quot;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Close &lt;dataset&gt; elements</span><br />
        &nbsp;&nbsp;&nbsp;strDataCurr = strDataCurr &amp; &quot;&lt;/dataset&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;strDataPrev = strDataPrev &amp; &quot;&lt;/dataset&gt;&quot;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Assemble the entire XML now</span><br />
        &nbsp;&nbsp;&nbsp;strXML = strXML &amp; strCategories &amp; strDataCurr &amp; strDataPrev &amp; &quot;&lt;/graph&gt;&quot;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Create the chart - MS Column 3D Chart with data contained in strXML</span><br />
        &nbsp;&nbsp;&nbsp;Call renderChart(&quot;../../FusionCharts/FCF_MSColumn3D.swf&quot;, &quot;&quot;, strXML, &quot;productSales&quot;, 600, 300)<br />
        %&gt;<br />
  &lt;/HTML&gt;</p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above example, we first include <span class="codeInline">FusionCharts.js</span> file to enable us embed the chart using JavaScript. We also include <span class="codeInline">FusionCharts.asp</span> to help us easily embed the charts.</p>
      <p>Thereafter, we define an ASP array <span class="codeInline">arrData</span> to store sales data for 6 different products. The array has three columns - first one for data label (product) and the next two for data values. The first data value column would store sales information for current year and the second one for previous year.</p>
      <p>We define a variable <span class="codeInline">strXML</span> to store the entire XML data. We also define <span class="codeInline">strCategories</span>, <span class="codeInline">strDataCurr</span> and <span class="codeInline">strDataPrev</span> variables to store XML data for categories elements, current year's dataset and previous year's dataset respectively. To build the XML, we iterate through the array and using string concatenation. We concatenate the entire XML finally in <span class="codeInline">strXML</span>. </p>
      <p>Finally, we render the chart using <span class="codeInline">renderChart()</span> function and pass <span class="codeInline">strXML</span> as <span class="codeInline">dataXML</span>. </p>
    <p>When you view the chart, you'll see a chart as under: </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_ArrayMS.jpg" alt="" /></td>
  </tr>
  <tr>
    <td valign="top" class="highlightBlock">In <span class="codeInline">Download Package &gt; Code &gt; ASP &gt; ArrayExample</span>, we've more example codes to create Stacked and Combination Charts too, which we have not explained here, as they're similar in concept. You can directly see the code if you want to. </td>
  </tr>
</table>
</body>
</html>
