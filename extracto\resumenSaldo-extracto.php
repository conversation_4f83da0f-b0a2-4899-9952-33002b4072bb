
<script  type="text/javascript">
    var explode = function () {
        jQuery("#recaudadora").select2();
    };

    cargarCmbRec();

    function cargarCmbRec() {

        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: "id=15",
            success: function (msg) {
                msg = msg.replace(/^\s+/, "");
                var resultado = msg.split("|");
                if (resultado[1] == 1) {

                    $("#recaudadora").append('<option value=\'\'>.:Todos:.</option>');
                    $("#recaudadora").append(resultado[3]);
                    $("#recaudadora option[value='*']").remove();
                    //$("#recaudadora").html(resultado[3]);
                    $("#status").fadeOut("Fast");
                    // $("table").tablesorter();
                    setTimeout(explode, 500);
                } else {
                    $("#status").fadeOut("Fast");
                    $("#resultado").html('Error"#021 ' + resultado[2] + '",consulte con el administrador facilitandole este dato.');
                }
            }
        });



    }


    $("#btnGenerarReporte").click(function () {
        var fecha_inicio = $("#fecha_inicio").val();
        var fecha_fin = $("#fecha_fin").val();
        var tipoFecha = $("#cmbTipoReporte").val();
        var moneda = $("#cmbMoneda").val();
        var banco = $("#cmbBanco").val();
        var origen = $("#cmbOrigen").val();
        if (origen == '*') {
            alert('Debe seleccionar una opcion de Origen de Datos');
            return false;
        }
        if (tipoFecha == '*') {
            alert('Debe Seleccionar un tipo de Fecha');
            $("#cmbTipoReporte").focus();
            return false;
        }
        if (fecha_inicio == '') {
            alert('Debe ingresar fecha inicio');
            return false;
        }
        if (fecha_fin == '') {
            alert('Debe ingresar fecha fin');
            return false;
        }


        var rec = $("#recaudadora").val();
        var indice = document.getElementById('recaudadora').selectedIndex;
        var des_rec = document.getElementById('recaudadora').options[indice].text;
        var condicion = "id=13&fecha_inicio=" + fecha_inicio + "&fecha_fin=" + fecha_fin + "&rec=" + rec + "&indice=" + indice + "&des_rec=" + des_rec + '&tipoFecha=' + tipoFecha + "&moneda=" + moneda + "&banco=" + banco + "&origen=" + origen;
        if (confirm('Esta seguro que desea realizar la consulta, puede demorarse unos segundos?')) {
            //$("#filtros").hide();																										
            //$('#btnGenerarReporte').attr('disabled','-1');
            $("#status").fadeIn("Slow");
            $.ajax({
                type: "POST",
                url: "commonFunctions.php",
                data: condicion,
                success: function (msg) {
                    ///  alert (msg);
                    msg = msg.replace(/^\s+/, "");
                    var resultado = msg.split("|");
                    if (resultado[1] == 1) {
                        $("#resultado").html(resultado[3]);
                        $("#saldo").html(resultado[4]);

                        $("#status").fadeOut("Fast");
                        $("#tinytable").tablesorter();
                    } else {
                        $("#status").fadeOut("Fast");
                        $("#resultado").html('Error"#013 ' + resultado[2] + '",consulte con el administrador facilitandole este dato.');
                    }
                }
            });
        }
    });


</script>

<div id="filtros" align="center">
    <ul><li><b>Movimientos Recaudadoras Resumido.</b></li></ul>
    <br>
    <div id="divFechaInicial">

        <script type="text/javascript">

            $("#fecha_inicio").datepicker({changeYear: true, changeMonth: true, firstDay: 1, dateFormat: 'dd/mm/yy'});
            $("#fecha_fin").datepicker({changeYear: true, changeMonth: true, firstDay: 1, dateFormat: 'dd/mm/yy'});

        </script>
        <table border="0">
            <tr>
                <td>Origen de Datos: </td>
                <td><select id="cmbOrigen" style="width: 50%;">
                        <option value="*" >Seleccione Origen de Datos </option>
                        <option value="A" >Actual(desde el 2017)</option>
                        <option value="H" >Historico(hasta el 2016)</option>
                    </select></td>
            </tr>
            <tr>
                <td>Tipo Fecha: </td>
                <td><select id="cmbTipoReporte" style="width: 50%;">
                        <option value="*" >Seleccione Tipo Fecha</option>
                        <option value="A" >Fecha Apertura</option>
                        <option value="C" >Fecha Cierre</option>
                    </select></td>
            </tr>
            <tr>
                <td>Fecha Inicio: </td>
                <td><input name="fecha_inicio" id="fecha_inicio" class="date" /></td>        
            </tr>
            <tr>
                <td>Fecha Fin: </td>
                <td><input name="fecha_fin" id="fecha_fin" class="date" /></td>        
            </tr>
            <tr>
                <td>Recaudadora: </td>
                <td><select id="recaudadora" name="recaudadora"></select></td>
            </tr>
            <tr>
                <td>Moneda: </td>
                <td><select id="cmbMoneda" name="cmbMoneda" style="width: 43%;">
                        <option value="1">Guarani-(GS)</option>
                        <option value="2">Dolar-(USD)</option>
                    </select>
            </tr>
            <tr>
                <td>Banco:</td>
                <td><select name="cmbBanco" id="cmbBanco" style="width: 43%;"> 
                        <option value="30">AMAMBAY S.A.</option>
                    </select></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
                <td><input type="button" value="Generar Reporte" id="btnGenerarReporte"></td>
            </tr>               
        </table>
        <input type="hidden" name="des_rec" id="des_rec" value="">
    </div>
</div>
<div id="resultado">

</div>
<div id="saldo"></div>

<br>
<style>
    #tablaOnline{
        float: left;
        margin-left: 30%;
    }
    #saldo{
        padding-bottom: 6%;
    }
</style>