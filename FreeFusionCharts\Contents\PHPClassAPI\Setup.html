<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" type="text/css" href="../Style.css">
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts PHP Class API &gt; Basic Setup </h2></td>
  </tr>
  
  <tr>
    <td valign="top" class="text">FusionCharts PHP Class basically consists of 2 files : <span class="codeInline">FusionCharts_Gen.php </span>and <span class="codeInline">FusionCharts.php</span>. The primary requirement to use the class is to keep the 2 files in one folder. Thats all. </td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text">For convenient use of FusionCharts PHP class within your projects, we would recommend to follow the folder structure described below : (though this is not any hard and fast rule to follow)<br />
<ul type="disc"><li>Create a folder inside which you will keep all your charting  projects.</li>
      
        <li>Name       this folder say, <span class="codeInline">MyFCPHPClassCharts</span>.</li>
        <li>Inside<span class="codeInline"> MyFCPHPClassCharts</span> folder, create two new folders.</li>
        <li>Name       those two folders as <span class="codeInline">Class</span> and <span class="codeInline">FusionCharts</span>.</li>
        <li>Copy       <span class="codeInline">FusionCharts_Gen.php</span> and <span class="codeInline">FusionCharts.php</span> file from Download Pack > Code > PHPClass > Includes and paste it inside <span class="codeInline">Class</span> folder.</li>
        <li>Copy       swf files and <span class="codeInline">FusionCharts.js</span> file from  <span class="codeInline"> FusionCharts </span><span class="text">and</span><span class="codeInline"> JSClass</span> folders, respectively of the       Download Pack and paste it inside <span class="codeInline">FusionCharts</span> folder created by you just       now.</li>
    </ul>    </td>
  </tr>
  
  <tr>
    <td valign="top" class="text"><p>And you are ready to unleash the power of FusionCharts with  PHP! You can save your applications within different folders inside the root  <span class="codeInline">MyFCPHPClassCharts</span> folder so as it builds up the following hierarchy.</p>      </td>
  </tr>
  <tr>
     <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><div align="left"><img src="Images/FolderStructure.JPG" width="441" height="215" class="imageBorder" /></div></td>
  </tr>
  <tr>
     <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>You may go for any other folder structure you wish; just make sure to keep <span class="codeInline">FusionCharts_Gen.php</span> and <span class="codeInline">FusionCharts.php</span> in one folder. Let&rsquo;s go ahead and create  our first chart with FusionCharts PHP class.</p>    </td>
  </tr>
  
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
