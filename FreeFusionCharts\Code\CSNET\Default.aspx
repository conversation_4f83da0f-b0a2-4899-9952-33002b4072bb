<%@ Page Language="C#" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>FusionCharts Free - VB ASP.Net Code Examples</title>
    <style type="text/css">
body{
 font-family:Verdana;
}
.text{
  font-family:Verdana;
  font-size:11px;
  line-height:15px;
}
</style>
</head>
<body>
    <div align="center">
        <h4>
            <a href='http://www.fusioncharts.com' target='_blank'>FusionCharts Free</a> - VB
            ASP.Net samples</h4>
    </div>
    <table width="600" border="0" cellspacing="3" cellpadding="3" style='border: 1px #CCCCCC solid;'
        class='text' align='center'>
        <tr>
            <td>
                <b>Basic Examples</b></td>
        </tr>
        <tr>
            <td>
                <ul>
                    <li><a href="BasicExample/BasicChart.aspx">Simple Column 3D Chart using data from XML
                        File (dataURL method)</a>&nbsp;</li>
                    <li><a href="BasicExample/BasicDataXML.aspx">Simple Column 3D Chart with XML data hard-coded
                        in ASP page (dataXML method) </a>&nbsp;</li>
                    <li><a href="BasicExample/SimpleChart.aspx">JavaScript embedding using dataURL method</a>&nbsp;</li>
                    <li><a href="BasicExample/dataXML.aspx">JavaScript Embedding using dataXML Method</a>&nbsp;</li>
                    <li><a href="BasicExample/MultiChart.aspx">Multiple Charts on a single page</a>&nbsp;</li>
                </ul>
            </td>
        </tr>
        <tr>
            <td>
                <b>Plotting Chart from Data Contained in Arrays</b></td>
        </tr>
        <tr>
            <td>
                <ul>
                    <li><a href="ArrayExample/SingleSeries.aspx">Single Series Chart Example</a>&nbsp;</li>
                    <li><a href="ArrayExample/MultiSeries.aspx">Multi Series Chart Example</a></li>
                    <li><a href="ArrayExample/Stacked.aspx">Stacked Chart Example</a>&nbsp;</li>
                    <li><a href="ArrayExample/Combination.aspx">Combination Chart Example</a></li>
                </ul>
            </td>
        </tr>
        <tr>
            <td>
                <b>Form Based Example</b></td>
        </tr>
        <tr>
            <td>
                <ul>
                    <li><a href="FormBased/Default.aspx">Plotting Charts from Data in Forms</a></li>
                </ul>
            </td>
        </tr>
        <tr>
            <td>
                <b>Database Examples</b></td>
        </tr>
        <tr>
            <td>
                <ul>
                    <li><a href="DBExample/BasicDBExample.aspx">Database Example Using dataXML Method</a></li>
                    <li><a href="DB_dataURL/Default.aspx">Database Example Using dataURL Method</a></li>
                    <li><a href="DB_DrillDown/Default.aspx">Database and Drill-Down Example</a>&nbsp;</li>
                </ul>
            </td>
        </tr>
        <tr>
            <td>
                <b>Database + JavaScript Examples</b></td>
        </tr>
        <tr>
            <td>
                <ul>
                    <li><a href="DB_JS/Default.aspx">Client Side Dynamic Chart Example</a></li>
                </ul>
            </td>
        </tr>
    </table>
</body>
</html>
