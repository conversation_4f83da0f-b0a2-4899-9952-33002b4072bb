<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
<style>

A
{
	COLOR: #291E40;
	FONT-FAMILY: Verdana;
	FONT-SIZE: 8pt;
	
	FONT-WEIGHT:normal;
	TEXT-DECORATION:underline;
	!important
}

.textgrey { color: #999999; }

</style>
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center" class="text">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts Free vs. FusionCharts v3 - Feature Comparison Matrix </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p><a href="http://www.fusioncharts.com" target="_blank">FusionCharts v3</a> offers the following features over FusionCharts Free. </p>    </td>
  </tr>
  <TR>
    <TD>&nbsp;</TD>
  </TR>
  
  <TR><TD style='border:1px solid #ededed;'>
 <table cellspacing="2" cellpadding="6" class="text" width="100%" >
   <tr >
     <td width="40%" align="left" valign="top" class="lightYellowTr"><span class="header">General Differences</span></td>
     <td width="30%" align="left" valign="top" class="lightYellowTr"><span class="header">FusionCharts v3</span></td>
     <td width="30%" align="left" valign="top" class="lightYellowTr"><span class="header">FusionCharts Free</span></td>
   </tr>
   
   <tr>
     <td width="40%" align="left" valign="top" >Number of charts and gauges</td>
     <td width="30%" align="left" valign="top" >45<br />
       <br />
       45 charts in FusionCharts v3 and 80 charts in the entire FusionCharts suite <br /></td>
     <td width="30%" align="left" valign="top" >22 </td>
   </tr>
   <tr >
     <td width="40%" align="left" valign="top" class="greyTr">Script / database independent </td>
     <td width="30%" align="left" valign="top" class="greyTr">Yes </td>
     <td width="30%" align="left" valign="top" class="greyTr">Yes </td>
   </tr>
   <tr>
     <td width="40%" align="left" valign="top">Created In <br />
        <span class="greyTr"><br/>
        </span><span class="textgrey">The version of Adobe Flash/ActionScript which was used for creating FusionCharts</span></td>
     <td width="30%" align="left" valign="top">Adobe Flash 8 / ActionScript 2 </td>
     <td width="30%" align="left" valign="top">Flash MX / ActionScript 1 </td>
   </tr>
   <tr >
     <td width="40%" align="left" valign="top" class="greyTr">JavaScript / AJAX Enabled <br />
        <br/>
        <span class="textgrey">AJAX-enabled charts can update themselves at client side without involving a page refresh</span></td>
     <td width="30%" align="left" valign="top" class="greyTr">Full <br/>
        <br />
        All charts in FusionCharts Suite v3 can update data from URL without Page refresh. This does not require any external (3rd party) AJAX framework</td>
     <td width="30%" align="left" valign="top" class="greyTr">Partial <br />
        <br/> 
           FusionCharts FREE charts can update themselves only from XML string loaded using external (3rd party) AJAX frameworks. Hence, partial.</td>
   </tr>
   <tr>
     <td width="40%" align="left" valign="top">Loads in Flash movies <br />
        <br/>
           <span class="textgrey">Support for implementation of FusionCharts in Flash movies for development of rich applications</span></td>
     <td width="30%" align="left" valign="top">Yes </td>
     <td width="30%" align="left" valign="top">No </td>
   </tr>
   <tr >
     <td width="40%" align="left" valign="top" class="greyTr">Animation in Charts</td>
     <td width="30%" align="left" valign="top" class="greyTr">All Charts support animation </td>
     <td width="30%" align="left" valign="top" class="greyTr">All Charts except Stacked 3D Column and Candlestick Charts support animation</td>
   </tr>
   <tr>
     <td width="40%" align="left" valign="top"><p>Maps Supported</p>
       <p class="textgrey" >535 maps, including all continents, major countries and all US states. Features include animation, styles, markers/cities and connector lines. </p></td>
     <td width="30%" align="left" valign="top">Yes<br />
       <br />
       In FusionMaps <br />
       (a part of FusionCharts Suite)</td>
     <td width="30%" align="left" valign="top">No</td>
   </tr>
   <tr >
     <td width="40%" align="left" valign="top" class="greyTr">In-built visual debugger <br />
        <br/> 
           <span class="textgrey">The debugger is a troubleshooting tool which helps in determining errors in data and in identification of issues related to loading of charts</span></td>
     <td width="30%" align="left" valign="top" class="greyTr">Yes </td>
     <td width="30%" align="left" valign="top" class="greyTr">No </td>
   </tr>
   <tr>
     <td width="40%" align="left" valign="top" class="text">Visual XML Generator Utility <br />
        <br/> 
           <span class="textgrey">FusionCharts takes in data only in custom XML format, the XML can be auto-generated using XML generator utility which provides a spreadsheet layout for convenient data entry</span></td>
     <td width="30%" align="left" valign="top">Yes </td>
     <td width="30%" align="left" valign="top">No </td>
   </tr>
   <tr class="greyTr">
     <td width="40%" align="left" valign="top">Support </td>
     <td width="30%" align="left" valign="top">Personalized support provided </td>
     <td width="30%" align="left" valign="top"> Personalized support NOT provided</td>
   </tr>
   </table>
   </tr>
   <tr>
   <td>&nbsp;</td>
   </tr>
   <tr>
   <td class="header">&nbsp;</td>
   </tr>
     <tr>
   <td  style='border:1px solid #ededed;'>
   <table cellspacing="2" cellpadding="6" class="text" width="100%" >
    <tr >
       <td class="lightYellowTr" valign="top" width="40%"><span class="header">Chart Types</span></td>
       <td class="lightYellowTr" valign="top" width="30%"><span class="header">FusionCharts v3</span></td>
       <td class="lightYellowTr" valign="top" width="30%"><span class="header">FusionCharts Free</span></td>
    </tr>
    <tr>
     <td valign="top" >Column Charts</td>
     <td valign="top" >Available in 2D &amp; 3D, with support for :<br />
        <br />
        &nbsp;
- 3D-lighting effect<br />
      &nbsp; - gradient fills <br />
      &nbsp; - 
       glass effect</td>
     <td valign="top" ><p>Available in  2D &amp; 3D</p>       </td>
   </tr>
    <tr class="greyTr" >
      <td valign="top" >Pie Charts</td>
      <td valign="top" >Available in  2D &amp; 3D, with support for :<br />
         <br />
        &nbsp; - 
         interactive slicing/rotation<br />
        &nbsp; -  3D-lighting<br />
        &nbsp; - gradient fills</td>
      <td valign="top" >Available in 2D &amp; 3D</td>
    </tr>
    <tr>
      <td valign="top" >Bar Charts</td>
      <td valign="top" >Available in  2D &amp; 3D, with support for :<br />
         <br />
        &nbsp; -
         3D-lighting <br />
        &nbsp; - gradient fills</td>
      <td valign="top" >Available in 2D only</td>
    </tr>
    <tr class="greyTr"  >
      <td valign="top" >Line Charts</td>
      <td valign="top" >Available in 2D &amp; 3D</td>
      <td valign="top" >Available in 2D only</td>
    </tr>
    <tr>
      <td valign="top" >Doughnut Charts</td>
      <td valign="top" >Available in  2D &amp; 3D, with support for :<br />
         <br />
        &nbsp; - 
         interactive slicing/rotation<br />
        &nbsp; - 3D lighting <br />
        &nbsp; - gradient fills</td>
      <td valign="top" >Available in 2D only</td>
    </tr>
    <tr class="greyTr" >
      <td valign="top" >Area Charts</td>
      <td valign="top" >Available in  2D &amp; 3D</td>
      <td valign="top" >Available in 2D only</td>
    </tr>
    <tr>
      <td valign="top" >Stacked Charts</td>
      <td valign="top" >Available in 2D &amp; 3D <br />
         <br />
        &nbsp; - Column, Bar and Area Charts<br />
        <br />
        &nbsp; - support for  3D-lighting effect and <br />
        &nbsp; - 
         gradient fills</td>
      <td valign="top" >Available  3D/2D Column,  2D Bar and 2D Area Charts</td>
    </tr>
    <tr class="greyTr" >
       <td valign="top" >Single Y Axis Combination Charts</td>
       <td valign="top" >Available in 2D &amp; 3D<br />
             <br />
                &nbsp; - Both 2D &amp; 3D Column, Line, Area<br />
                  <br />
         &nbsp; - support for 3D-lighting effect and <br />
         &nbsp; -         gradient fills<br />
          <br />
          &nbsp; - scroll <br />
          <br />
          Also 
          a true 3D Combination chart with lots of interactive features.</td>
       <td valign="top" >Unavailable</td>
    </tr>
    <tr >
       <td valign="top" >Dual Y  Axis Combination Charts</td>
       <td valign="top" >Available in 2D &amp; 3D, <br />
          <br />
         &nbsp; - Both 2D &amp; 3D Column, Line, Area<br />
          <br />
          &nbsp;
- support for 3D-lighting effect and <br />
gradient fills<br />
<br />
- scroll</td>
       <td valign="top" >Available<br />
          <br />
          Dual Y Axis <br />
          2D/3D Column (primary Y Axis)<br />
          Line (secondary Y Axis)</td>
    </tr>
    <tr class="greyTr" >
       <td valign="top" ><p>Scrollable charts </p>
          <p class="textgrey">2D Column,  line and area charts with scrollable x-axis</p></td>
       <td valign="top" >Available<br />
          <br />
         &nbsp; -
          2D Column, line, area charts <br />  
         &nbsp; - 
          2D stacked column chart <br />
         &nbsp; - 
          2D single/dual Y Axis combination charts <br />
          <br /></td>
       <td valign="top" >Unavailable</td>
    </tr>
    
    <tr>
      <td valign="top" >Interactive gauges (Angular, Linear, LED etc.)<br />
        <br />
          <span class="textgrey">Gauges are used to interactively plot KPIs (Key Performance Indicators)</span></td>
      <td valign="top" >Available<br />
            <br />
               &nbsp; - Angular (dial)<br />
                  &nbsp;
- Linear (scale)<br />
&nbsp; - Vertical/Horizontal LED <br />
&nbsp;
- Bulb<br />
&nbsp;
- Thermometer <br />
<br />
        In FusionWidgets <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >Unavailable</td>
    </tr>
    <tr >
       <td valign="top" class="greyTr" >Real-time/data streaming charts and gauges <br />
          <br />
          <span class="textgrey">Charts that automatically update themselves with  new data from server every n seconds</span></td>
       <td valign="top" class="greyTr" >Available<br />
          <br />
        &nbsp; - Line, area, column<br />
&nbsp; - Stacked column, area<br />
&nbsp; - Dual Y Axis Line<br />
&nbsp; - Angular, linear gauges</td>
       <td valign="top" class="greyTr" >Unavailable</td>
    </tr>
    <tr >
       <td valign="top" >Gantt Chart</td>
       <td valign="top" ><p>Available, with support for :</p>
             <p>&nbsp;&nbsp;- interactive<br />
                &nbsp; - grouping of tasks<br />
                &nbsp; - 
                milestones<br />
                &nbsp; - markers and <br />
                &nbsp; -
                task connectors<br />
                <br />
                <br />
In FusionWidgets <br />
(a part of the FusionCharts Suite)</p></td>
       <td valign="top" >Available</td>
    </tr>
    <tr >
       <td valign="top" class="greyTr" >Funnel &amp; Pyramid Charts</td>
       <td valign="top" class="greyTr" >Both Funnel &amp; Pyramid Charts available<br />
          <br />
          <br />
In FusionWidgets <br />
(a part of the FusionCharts Suite)</td>
       <td valign="top" class="greyTr" >Funnel Chart available</td>
    </tr>
    <tr>
      <td valign="top" >Bullet Graph</td>
      <td valign="top" >Available - horizontal or vertical <br />
        <br />
        In FusionWidgets <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >Unavailable</td>
    </tr>
    <tr >
      <td valign="top" class="greyTr" >Sparklines, spark columns, spark win/loss charts<br />
          <span class="textgrey"><br />
        These are word-sized graphics that can be used inline with text</span></td>
      <td valign="top" class="greyTr" >Available<br />
        <br />
        In FusionWidgets<br /> 
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >Unavailable</td>
    </tr>
    <tr>
       <td valign="top" >Candelstick Chart<span class="style1"><br />
          <br />
          </span><span class="textgrey">Powerful and interactive interface to plot your stock data</span></td>
       <td valign="top" >Available<br />
       <br />
          In PowerCharts <br />
          (a part of the FusionCharts Suite)</td>
       <td valign="top" >Available</td>
    </tr>
    
    <tr >
      <td valign="top" class="greyTr" >Drag Node Chart<br />
        <span class="textgrey"><br />
        
        A chart where each data set is shown as a drag-able node. Perfect for network diagrams and hierarchy structures</span></td>
      <td valign="top" class="greyTr" >Available<br />
        <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >Unavailable</td>
    </tr>
    <tr >
      <td valign="top" >Multi-axis Line Chart<br />
         <br />
         <span class="textgrey">Interactive line chart with multiple axes on the same chart.</span></td>
      <td valign="top" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
    <tr>
      <td valign="top" class="greyTr" >Select Scatter Chart<br />
         <br />
            <span class="textgrey">An extension of Scatter Chart with the added functionality of selecting any number of points on the chart and returning them back to server (or JavaScript functions).</span></td>
      <td valign="top" class="greyTr" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >No</td>
    </tr>
    <tr >
      <td valign="top" >Drag-able charts <br />
         <br />
         <span class="textgrey">A set of charts that allow you to visually manipulate the data on the chart and then submit it back to the server. Used in simulation and financial planning.</span></td>
      <td valign="top" >Yes (column, line, area) <br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
    <tr>
      <td valign="top" class="greyTr" >Waterfall Chart<br />
         <br />
            <span class="textgrey">A special type of column chart that shows how an initial value is increased and decreased by a series of intermediate values, leading to a final value.</span></td>
      <td valign="top" class="greyTr" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >No</td>
    </tr>
    <tr >
      <td valign="top" >Multi-level Pie Chart<br />
         <br />
            <span class="textgrey">A specialized chart that allows you to show symmetrical and asymmetrical tree structures in a consolidated pie like structure.</span></td>
      <td valign="top" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
    <tr>
      <td valign="top" class="greyTr" >Logarithmic Charts<br />
         <br />
            <span class="textgrey">Charts with logarithmic axis that support any positive base.</span></td>
      <td valign="top" class="greyTr" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >No</td>
    </tr>
    <tr >
      <td valign="top" >Spline Charts<br />
         <br />
            <span class="textgrey">Just like a line chart. Except that the data points are connected by a single continuous curve.</span></td>
      <td valign="top" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
    <tr>
      <td valign="top" class="greyTr" >Inverse y-Axis Charts<br />
         <br />
         <span class="textgrey">         Charts with inverted axis to show rankings. Used to plot ranking, hits trend etc.</span></td>
      <td valign="top" class="greyTr" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >No</td>
    </tr>
    <tr >
      <td valign="top" >Radar Charts<br />
         <br />
            <span class="textgrey">Charts with multiple axes along which data can be plotted. Useful to look at several different factors related to one item in a single glance.</span></td>
      <td valign="top" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
    <tr>
      <td valign="top" class="greyTr" >Error Bar Chart<br />
         <br />
            <span class="textgrey">Bar charts with an additional y-value to show the error value on the chart. Useful for displaying statistical information about the data on the chart.</span></td>
      <td valign="top" class="greyTr" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" class="greyTr" >No</td>
    </tr>
    <tr >
      <td valign="top" >Kagi Chart<br />
         <br />
            <span class="textgrey">Charts used to illustrate general levels of supply and demand for certain assets.</span></td>
      <td valign="top" >Yes<br />
         <br />
        In PowerCharts <br />
        (a part of the FusionCharts Suite)</td>
      <td valign="top" >No</td>
    </tr>
   </table>   </tr>
  <tr>
  <td>&nbsp;</td>
  </tr>
  <tr>
  <td class="header">&nbsp;</td>
  </tr>
   <tr>
   <td  style='border:1px solid #ededed;'><table cellspacing="2" cellpadding="6" class="text" width="100%" >
   <tr >
      <td class="lightYellowTr" valign="top" width="40%" ><span class="header">Features</span></td>
      <td class="lightYellowTr" valign="top" width="30%" ><span class="header">FusionCharts v3</span></td>
      <td class="lightYellowTr" valign="top" width="30%" ><span class="header">FusionCharts Free</span></td>
   </tr>
   <tr >
     <td valign="top" >Gradients and 3D Lighting<br />
        <br/>
        <span class="textgrey">Gradients allow for gradient fill for the data plot, the canvas and the chart background. 3D lighting gives a realistic 3D feel to the charts </span></td>
     <td  valign="top" >Yes </td>
     <td valign="top" >No </td>
   </tr>
   <tr  >
     <td width="40%" valign="top" class="greyTr">Custom Animation for each chart element <br />
        <br/> 
        <span class="textgrey">Custom animation allows animation effects like fade-in, scaling and translation with different types of tweening effects.</span></td>
     <td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No </td>
   </tr>
    <tr >
     <td width="40%" valign="top" >Hot spots (same window, new window,   JavaScript links)<br />
        <br/> 
        <span class="textgrey">Each data set (each column in a column chart for example) acts as a hotspot that can be linked to other pages for more info.</span></td>
     <td width="30%" valign="top" >Yes </td>
     <td width="30%" valign="top" >Yes </td>
   </tr>
   <tr  >
     <td width="40%" valign="top" class="greyTr">Hot spots (frames, pop-ups)<br />
        <br/> 
        <span class="textgrey">Each data set (each column in a column chart for example) acts as a hotspot that can be linked to other frames or pop-up windows.</span></td><td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No</td>
     </tr>
   <tr >
      <td valign="top" >Entire  chart as hot spot (frames, popups, JavaScript links) </td>
      <td  valign="top" >Yes</td>
      <td valign="top" >No</td>
   </tr>
   
   <tr >
      <td valign="top" class="greyTr" >JavaScript Events <br />
         <br />
         <span class="textgrey">JavaScript functions called by charts for events like chart loaded, chart rendered, chart exported and  errors occured while loading data. </span></td>
      <td  valign="top" class="greyTr" >Yes</td>
      <td valign="top" class="greyTr" >No</td>
   </tr>
      
      <tr >
     <td width="40%" valign="top">Smart labels in Pie and Doughnut Charts<br />
        <br />
        <span class="textgrey">Smart labels avoid overlapping of data labels even when there are a lot of data sets in a small space</span></td>
     <td width="30%" valign="top" >Yes </td>
     <td width="30%" valign="top" >No</td>
     </tr>
      <tr  >
     <td width="40%" valign="top" class="greyTr">Custom font properties for each chart element<br />
        <br />
        <span class="textgrey">Each chart element can have its font properties defined separately, rather than sharing the global font properties</span></td>
     <td width="30%" valign="top" class="greyTr" >Yes</td>
     <td width="30%" valign="top" class="greyTr" >No</td>
     </tr>
       <tr >
     <td width="40%" valign="top">Export Chart as Image/PDF</td>
     <td width="30%" valign="top" >No </td>
     <td width="30%" valign="top" >Server-side and client-side export to JPG, PNG and PDF</td>
     </tr>
      <tr  >
     <td width="40%" valign="top" class="greyTr">Color Palettes<br />
        <br />
        <span class="textgrey">Pre-defined color themes with visually-pleasing colors defined for all chart elements</span></td>
     <td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No </td>
   </tr>
    <tr >
     <td valign="top" width="40%">JavaScript APIs to print the chart, export, update chart, get data and individual attributes</td>
     <td width="30%" valign="top" >Yes </td>
     <td width="30%" valign="top" >No </td>
   </tr>
    <tr  >
     <td width="40%" valign="top" class="greyTr">Custom display texts for data items<br />
        <br />
        <span class="textgrey">Custom labels appended to data point for detailed info about the data points.</span></td>
     <td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No </td>
   </tr>
   <tr >
     <td valign="top" width="40%">Export chart data as CSV</td>
     <td width="30%" valign="top" >Yes </td>
     <td width="30%" valign="top" >No </td>
   </tr>
   <tr  >
     <td width="40%" valign="top" class="greyTr">Display Modes for x-axis labels </td>
     <td width="30%" valign="top" class="greyTr" >Rotate, stagger, slant and wrap </td>
     <td width="30%" valign="top" class="greyTr" >Rotate</td>
   </tr>
    <tr >
     <td valign="top" width="40%">Multi-lingual (UTF-8) characters</td>
     <td width="30%" valign="top" >Yes, both horizontally and vertically</td>
     <td width="30%" valign="top" >Horizontally</td>
   </tr>
    <tr >
     <td class="greyTr" valign="top" width="40%">Logarithmic Axis </td>
     <td width="30%" valign="top" class="greyTr">Yes<br />
        <br />
       In PowerCharts <br />
       (a part of the FusionCharts Suite)</td>
     <td width="30%" valign="top" class="greyTr">No </td>
   </tr>
   <tr >
     <td valign="top" width="40%">Multiple axis</td>
     <td width="30%" valign="top" >Yes<br />
        <br />
       In PowerCharts <br />
       (a part of the FusionCharts Suite)</td>
     <td width="30%" valign="top" >No </td>
   </tr>
   <tr >
     <td class="greyTr" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/GalleryPie.asp" target="_blank">Real 3D Pie and doughnut charts with dynamic slicing and   interactive rotation capabilities</a></td>
     <td width="30%" valign="top" class="greyTr">Yes </td>
     <td width="30%" valign="top" class="greyTr">No </td>
   <tr>
     <td class="text" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/Styles/Shadow.html" target="_blank">Shadow effects</a></td>
     <td width="30%" valign="top">Yes </td>
     <td width="30%" valign="top">No </td>
   </tr>
   <tr  >
     <td width="40%" valign="top" class="greyTr"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/Styles/Glow.html" target="_blank">Glow effects</a> </td>
     <td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No </td>
   </tr>
   <tr>
     <td class="text" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/Styles/Bevel.html" target="_blank">Bevel effects</a> </td>
     <td width="30%" valign="top">Yes </td>
     <td width="30%" valign="top">No </td>
   </tr>
   <tr >
     <td class="greyTr" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/FeatureTour/plot_border.html" target="_blank">Dashed lines and borders</a> </td>
     <td width="30%" valign="top" class="greyTr">Yes </td>
     <td width="30%" valign="top" class="greyTr">No </td>
   </tr>
   <tr >
     <td width="40%" valign="top" >Automatic Divisional Line Numbering </td>
     <td width="30%" valign="top" >Yes </td>
     <td width="30%" valign="top" >No </td>
   </tr>
   <tr >
     <td class="greyTr" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/Number_Scaling.html" target="_blank">Custom Number Scaling</a> </td>
     <td width="30%" valign="top" class="greyTr">Yes </td>
     <td width="30%" valign="top" class="greyTr">No </td>
   </tr>
   <tr>
     <td class="text" valign="top" width="40%"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/AttDesc/DataValues.html" target="_blank">Support for rotated value boxes</a> </td>
     <td width="30%" valign="top">Yes </td>
     <td width="30%" valign="top">No </td>
   </tr>
   <tr  >
     <td width="40%" valign="top" class="greyTr"><a href="http://www.fusioncharts.com/FusionCharts/Docs/Contents/AttDesc/DataValues.html" target="_blank">Value text boxes inside columns?</a> </td>
     <td width="30%" valign="top" class="greyTr" >Yes </td>
     <td width="30%" valign="top" class="greyTr" >No </td>
   </tr>
   <tr  >
      <td valign="top">Branding of charts<br />
         <br />
         <span class="textgrey">Brand  your charts with your company logo, name and URL attached to the chart.</span></td>
      <td valign="top" >Yes</td>
      <td valign="top" >No</td>
   </tr>
 </table></TD>
</TR>
</Table>

</BODY>
</HTML>
