<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" type="text/css" href="../Style.css">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table width="98%" border="0" align="right" cellpadding="2" cellspacing="1">
  <tr> 
    <td colspan="3" class="pageHeader">Funnel Chart &gt; Introduction</td>
  </tr>
  
  <tr>
    <td colspan="3" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td colspan="3" class="text"> <p>Funnel charts are basically used to plot 
        streamlined data, e.g., plotting sales data for sales pipeline analysis. 
        A funnel chart consists of various segments, each representing a data 
        set. The height of the funnel segment with respect to the entire funnel 
        depicts the value for that particular data set.</p>
      <p>Some of the funnel charts that you can make with FusionCharts Free are:</p>
      <p>&nbsp;</p></td>
  </tr>
  <tr> 
    <td class="text"><div align="center"><img src="Images/G_Funnel_1.jpg" width="140" height="89"></div></td>
    <td class="text"><div align="center"><img src="Images/G_Funnel_2.jpg" width="140" height="89"></div></td>
    <td class="text"><div align="center"><img src="Images/G_Funnel_3.jpg" width="140" height="89"></div></td>
  </tr>
  <tr> 
    <td colspan="3" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="3" class="text"><strong>Anatomy</strong></td>
  </tr>
  <tr> 
    <td colspan="3" class="text">Let's have a look at a basic funnel chart, enlisting 
      the various components of a funnel chart:</td>
  </tr>
  <tr> 
    <td colspan="3" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td colspan="3" class="text"><img src="Images/Funnel_Basic_Anatomy.jpg" width="322" height="303"></td>
  </tr>
  <tr> 
    <td colspan="3" class="text"><p>A funnel chart consists of various funnel 
        segments (separated from each other by a distance). Each funnel segment 
        can have its individual properties like background, border etc. to help 
        distinguish them from one another. The funnel segments at the bottom get 
        tapered to form the tap of the funnel. On the top of each funnel is displayed 
        the data name and value corresponding to that segment.</p>
      <p>Now that we know the basics of a funnel chart, let's try our hands at 
        a sample funnel chart.</p>      </td>
  </tr>
  <tr> 
    <td colspan="3" class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
