<script type="text/javascript">

$(document).ready(function() {

	usuario = "<?php echo $usuario = $_GET['usuario']; ?>";
	$("#usuario").val(usuario);
	
	resulCargarDatos = cargarDatos();
	
	$("#old_password").focus();
	
	$("#btnOtroUsuario").click(function (){ 
		// funcion #003
		$("#main").load('cambios.php');
	});
	
	$("#btnProcesar").click(function (){ 
	// funcion #004
		if(confirm('Estas seguro que desea realizar el cambio?')){
			
			$("#status").fadeIn("Slow");	
			nombre = $("#nombre").val();
			apellido = $("#apellido").val();
			usuario = $("#usuario").val();
			usuario = usuario.replace(/^\s+/, "");
			usuario = usuario.replace(/\./g,'');
			in_password = $("#in_password").val();
			old_password = $("#old_password").val();
			rePassword = $("#rePassword").val();
			error = 0;
			falta = '';
			var campo = 'clave_acceso';
			var tipo = '1';
			if(nombre == ''){
				falta = 'Nombre, ';
				error = 1;
			}
			if(apellido == ''){
				falta = falta+'Apellido, ';
				error = 1;
			}
			if(usuario == ''){
				falta = falta+'Usuario, ';
				error = 1;
			}
			if(usuario == ''){
				falta = falta+'Contrase\u00F1a Actual, ';
				error = 1;
			}
			if(in_password == ''){
				falta = falta+'Contrase\u00F1a Nueva, ';
				error = 1;
			}
			if(rePassword == ''){
				falta = falta+'Re-Contrase\u00F1a Nueva, ';
				error = 1;
			}
			
			if(error != 0){
				$("#status").fadeOut("Fast");
				alert("Debe completar: "+falta);
			}
			else{
				if(old_password != passActual){
					$("#status").fadeOut("Fast");
					alert("La contrase\u00F1a actual no coincide con la que se encuentra en Base de Datos");
					error = 1;
				}
				if(in_password != rePassword){
					$("#status").fadeOut("Fast");
					alert("Las nuevas contrase\u00F1as no coinciden");
					error = 1;
				}
				if(usuario == in_password){
					error = 1;
					$("#status").fadeOut("Fast");
					alert("La nueva contrase\u00F11a no puede coincidir con el usuario")
				}
				if(error != 1){
					$.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: "id=6&usuario="+usuario+"&campo="+campo+"&valor="+in_password+"&tipo="+tipo+"&nombre="+nombre+"&apellido="+apellido,
					success: function(msg)
					{
						var resultado = msg.split("|");
						if(resultado[0]=='6'){
							if(resultado[1]=='0'){
								$("#status").fadeOut("Fast");
								$("#main").html('Mensaje "#006 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
							}
							else if(resultado[1]=='1'){
								$("#status").fadeOut("Fast");
								$("#main").html(" Mensaje: La contrase&ntilde;a se modifico correctamente");
							}
							else{
								$("#status").fadeOut("Fast");
								$("#main").html('Mensaje "#006 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
							}
						}
						else{
							$("#status").fadeOut("Fast");
							$("#main").html('Mensaje en la funcion #006, favor consulte con el administrador facilitandole este dato.');
						}
					}
				});
				}
			}
		}
	});
});

function limpiarTodo(){
	$("#usuario").val('');
	$("#nombre").val('');
	$("#apellido").val('');
	$('#btnProcesar').attr('disabled','-1')
	$("#password").val('');
	$("#rePassword").val('');
	$("#nombre").focus();
}

function cargarDatos(){
// funcion #005
	$("#status").fadeIn("Fast");
	$.ajax({
		type: "POST",
		url: "commonFunctions.php",
		data: "id=5&usuario="+usuario,
		success: function(msg){
			msg = msg.replace(/^\s+/, "");
			var resultado = msg.split("|");
			if(resultado[0]=='5'){
				if(resultado[1]=='0'){
					$("#status").fadeOut("Fast");
					$("#main").html(resultado[2]);
				}
				else if(resultado[1]=='1'){
					$("#status").fadeOut("Fast");
					var adic = resultado[3].split(";");
					$("#nombre").val(adic[0]);
					$("#apellido").val(adic[1]);
					passActual = adic[2];
				}
				else{
					$("#status").fadeOut("Fast");
					$("#main").html('Mensaje "#005: '+resultado[2]+'".');
				}
			}
			else{
				$("#status").fadeOut("Fast");
				$("#main").html('Mensaje #005, favor consulte con el administrador facilitandole este dato.');
			}
		}
	});
	return true;
}

</script>


<div id="main" >
    <div id="content">
        <ul><li>Para realizar el cambio, debe ingresar la contrase&ntilde;a Actual, y la nueva.</li></ul>
        <form id="cliente" method="post" action="">
            <table>
                    <tr>
                        <td class="label"><label id="lnombre" for="lnombre">Nombre</label></td>
                        <td class="field">
                        	<input id="nombre" name="nombre" type="text" value="" disabled/>
                        </td>
                    </tr>
                <tr>
                    <td class="label"><label id="lapellido" for="lapellido">Apellido</label></td>
                    <td class="field">
                    	<input id="apellido" name="apellido" type="text" value="" disabled/>
                	</td>
                </tr>
                <tr>
                    <td class="label"><label id="lusuario" for="lusuario">Usuario</label></td>
                    <td class="field">
                        <input id="usuario" name="usuario" type="text" value="" maxlength="20" disabled />
                    </td>
                    <td class="status"></td>
                </tr>
                <tr>
                    <td class="label"><label id="l_Oldpassword" for="l_Oldpassword">Ingrese su Contrase&ntilde;a Actual</label></td>
                    <td class="field">
                        <input id="old_password" name="old_password" value="" maxlength="40" type="password"/>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label id="lpassword" for="lpassword">Ingrese su Nueva Contrase&ntilde;a</label></td>
                    <td class="field">
                        <input id="in_password" name="in_password" value="" maxlength="40" type="password"/>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label id="lre_password" for="lre_password">Repita su Nueva Contrase&ntilde;a</label></td>
                    <td class="field">
                        <input id="rePassword" name="rePassword" value="" maxlength="40" type="password"/>
                    </td>
                </tr>
              
            </table>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
             &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type="button" value="Procesar" id="btnProcesar" />
            <input type="button" value="Elegir otro usuario" id="btnOtroUsuario" />
        </form>
    </div>
    
    
    
</div>
