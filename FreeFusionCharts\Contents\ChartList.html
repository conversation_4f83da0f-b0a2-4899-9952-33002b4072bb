<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts Free  Chart List </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>FusionCharts Free contains the following chart types.</p>    </td>
  </tr>
  <tr>
    <td valign="top">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="highlightBlock">If you need advanced charts like Scroll, Funnel, Gantt, Pyramid, Dual axis charts etc., please look at <a href="http://www.fusioncharts.com" target="_blank">FusionCharts v3</a>. </td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><table width="98%" border="1" align="center" cellpadding="2" cellspacing="0" bordercolor="#f1f1f1">
      <tr>
        
		<td width="50%" valign="top" class="header">Chart Type </td>
        <td width="50%" valign="top" class="header">File Name </td>
      </tr>
      <tr>
        <td colspan="3" valign="top" class="text"><strong>Single Series Charts </strong></td>
        </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Column 3D</td>
        <td width="50%" valign="top" class="codeInline">FCF_Column3D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Column 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Column2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Line 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Line.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Area 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Area2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Bar 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Bar2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Pie 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Pie2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Pie 3D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Pie3D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Doughnut 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_Doughnut2D.swf</td>
      </tr>
      <tr>    <td width="50%" valign="top" colspan="3">&nbsp;</td>    </tr>
      <tr>
        <td colspan="3" valign="top" class="text" ><strong>Multi-series Charts </strong></td>
        </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Column 2D </td>
        <td width="50%" valign="top" class="codeInline" >FCF_MSColumn2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Column 3D</td>
        <td width="50%" valign="top" class="codeInline" >FCF_MSColumn3D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Line 2D</td>
        <td width="50%" valign="top" class="codeInline">FCF_MSLine.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Bar 2D</td>
        <td width="50%" valign="top" class="codeInline">FCF_MSBar2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Area 2D</td>
        <td width="50%" valign="top" class="codeInline">FCF_MSArea2D.swf</td>
      </tr>
           <tr>    <td width="50%" valign="top" colspan="3">&nbsp;</td>    </tr>

      <tr>
        <td colspan="3" valign="top" class="text"><strong>Stacked Charts </strong></td>
        </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Stacked Column 3D </td>
        <td width="50%" valign="top" class="codeInline">FCF_StackedColumn3D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Stacked Column 2D</td>
        <td width="50%" valign="top" class="codeInline">FCF_StackedColumn2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Stacked Bar 2D</td>
        <td width="50%" valign="top" class="codeInline">FCF_StackedBar2D.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Stacked Area 2D </td>
        <td width="50%" valign="top" class="codeInline">FCF_StackedArea2D.swf</td>
      </tr>
      <tr>    <td width="50%" valign="top" colspan="3">&nbsp;</td>    </tr>
      
      <tr>
        <td colspan="3" valign="top" class="text"><strong>Combination Charts</strong></td>
        </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Column 2D + Line - Dual Y Axis</td>
        <td width="50%" valign="top" class="codeInline">FCF_MSColumn2DLineDY.swf</td>
      </tr>
      <tr>
        <td width="50%" valign="top" class="text" style=" text-indent:20px;">Multi-series Column 3D + Line - Dual Y Axis</td>
        <td width="50%" valign="top" class="codeInline">FCF_MSColumn3DLineDY.swf</td>
      </tr>
           <tr>    <td width="50%" valign="top" colspan="3">&nbsp;</td>    </tr>

      <tr>
        <td colspan="3" valign="top" class="text"><strong>Financial Charts </strong></td>
        </tr>
      <tr>
        <td valign="top" class="text" style=" text-indent:20px;">Candlestick Chart</td>
        <td valign="top" class="codeInline">FCF_Candlestick.swf</td>
        </tr>
      <tr>
        <td valign="top" class="text" style=" text-indent:20px;">&nbsp;</td>
        <td valign="top" class="codeInline">&nbsp;</td>
      </tr>
      <tr>
        <td valign="top" class="text"><strong>Funnel Chart</strong></td>
        <td valign="top" class="codeInline">&nbsp;</td>
      </tr>
      <tr>
        <td valign="top" class="text" style=" text-indent:20px;">Funnel Chart </td>
        <td valign="top" class="codeInline">FCF_Funnel.swf</td>
      </tr>
      <tr>
        <td valign="top" class="text" style=" text-indent:20px;">&nbsp;</td>
        <td valign="top" class="codeInline">&nbsp;</td>
      </tr>
      <tr>
        <td valign="top" class="text"><strong>Gantt Chart </strong></td>
        <td valign="top" class="codeInline">&nbsp;</td>
      </tr>
      <tr>
        <td valign="top" class="text" style=" text-indent:20px;">Gantt Chart </td>
        <td valign="top" class="codeInline">FCF_Gantt.swf</td>
      </tr>
    </table></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
