/* Put this inside a @media qualifier so Netscape 4 ignores it */
@media screen, print { 
	/* Turn off list bullets */
	ul.mktree  li { list-style: none; } 
	/* Control how "spaced out" the tree is */
	ul.mktree, ul.mktree ul , ul.mktree li { margin-left:10px; padding:0px; }
	/* Provide space for our own "bullet" inside the LI */
	ul.mktree  li           .bullet { padding-left: 15px; }
	/* Show "bullets" in the links, depending on the class of the LI that the link's in */
	ul.mktree  li.liOpen    .bullet { cursor: pointer; background: url(images/minus.gif)  center left no-repeat; }
	ul.mktree  li.liClosed  .bullet { cursor: pointer; background: url(images/plus.gif)   center left no-repeat; }
	ul.mktree  li.liBullet  .bullet { cursor: default; background: url(images/bullet.gif) center left no-repeat; }
	/* Sublists are visible or not based on class of parent LI */
	ul.mktree  li.liOpen    ul { display: block; }
	ul.mktree  li.liClosed  ul { display: none; }

	/* Format menu items differently depending on what level of the tree they are in */
	/* Uncomment this if you want your fonts to decrease in size the deeper they are in the tree */
/*
	ul.mktree  li ul li { font-size: 90% }
*/

}

div.tree div {
 padding-left:16px;
}
div.tree div.parent div {
 display:none;
 cursor:default;
}
div.tree div.parent {
 cursor:pointer !important;
 background:transparent url(../images/plus.gif) no-repeat top left;
 background-color:#999;
}
div.tree div.expanded {
 background:transparent url(../images/minus.gif) no-repeat top left;
}