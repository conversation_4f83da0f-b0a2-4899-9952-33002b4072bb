<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free - php Code Examples</title>
<style type="text/css">
body{
 font-family:Verdana;
}
.text{
  font-family:Verdana;
  font-size:11px;
  line-height:15px;
}
</style>
</head>

<body>
<div align="center">
  <h4><a href='http://www.fusioncharts.com' target='_blank'>FusionCharts Free</A> - FusionCharts PHP Class samples</h4>  
</div>
<table width="600" border="0" cellspacing="3" cellpadding="3" style='BORDER:1px #CCCCCC solid;' class='text' align='center'>  
    <tr>
    <td><B>Basic Examples</B></td>    
  </tr>
  <tr>    
	<td>
	<ul>
	<li><a href="BasicExample/FirstChart.php">Simple Column 3D Chart </a>&nbsp;</li>
    <li><a href="BasicExample/SimpleChart.php">Simple Column 3D Chart using dataURL method</a>&nbsp;</li>
     <li><a href="BasicExample/CreateChartFromExtData.php">Simple Column 3D Chart using XML Hardcoded on the file</a>&nbsp;</li>
    <li><a href="BasicExample/MultiChart.php">Multiple Charts on a single page</a>&nbsp;</li>
    </ul>    </td>
  </tr>
    
    
  <tr>
    <td><b>Plotting Chart from Data Contained in Arrays</b></td>
  </tr>
  <tr>
    <td>
    <ul>
    <li><a href="ArrayExample/SingleSeries.php">Single Series Chart Example</a>&nbsp;</li>
    <li><a href="ArrayExample/MultiSeries.php">Multi Series Chart Example</a></li>
    <li><a href="ArrayExample/Stacked.php">Stacked Chart Example</a>&nbsp;</li>
    <li><a href="ArrayExample/Combination.php">Combination Chart Example</a></li>
    </ul>    </td>
  </tr>
    
  <tr>
    <td><b>Form Based Example</b></td>    
  </tr>
  <tr>
    <td>
    <ul>
    <li><a href="FormBased/Default.php">Plotting Charts from Data in Forms</a></li>
    </ul>    </td>
  </tr>
  
  <tr>
    <td><b>Database Examples</b></td>    
  </tr>
  <tr>
    <td>
    <ul>
    <li><a href="DBExample/BasicDBExample.php">Database Example Using dataXML Method</a></li>
    <li><a href="DB_dataURL/Default.php">Database Example Using dataURL Method</a></li>
    <li><a href="DB_DrillDown/Default.php">Database and Drill-Down Example</a>&nbsp;</li>
    </ul>    </td>
  </tr>
  <tr>
    <td><b>Database + UTF8 Examples</b></td>
  </tr>
  <tr>
    <td><ul>
        <li><a href="UTF8Example/JapaneseXMLFileExample.php">Japanese Example using data from XML file</a></li>
      <li><a href="UTF8Example/JapaneseDBExample.php">Japanese Example using data from Database</a></li>
      <li><a href="UTF8Example/FrenchXMLFileExample.php">French Example using data from XML file</a></li>
      <li><a href="UTF8Example/FrenchDBExample.php">French Example using data from Database</a></li>
    </ul></td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>
</body>
</html>
