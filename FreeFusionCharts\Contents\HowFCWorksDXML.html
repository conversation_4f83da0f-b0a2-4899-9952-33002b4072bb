<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
<script type="text/JavaScript">
<!--
function MM_openBrWindow(theURL,winName,features) { //v2.0
  window.open(theURL,winName,features);
}
//-->
</script>
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">dataXML Method </h2></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In this method, you send the XML data along with the HTML Content and chart SWF file to the browser. The SWF loads, reads this data (present in same page) and then renders the chart. The following diagram would help you understand better:</p>
    </td>
  </tr>
  <tr>
    <td valign="top" class="text"><p><img src="Images/dataXMLMethod.jpg" class="imageBorder"/></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>As you can see above, the following steps are involved in this process:</p>
      <ol>
        <li>You send the HTML content, chart SWF file and XML data, all at once, to the end viewer's browser. </li>
        <li>Once the SWF is loaded on the end viewer's machine, it reads the XML data present on the same page</li>
        <li>It finally parses it and then renders the chart.</li>
      </ol>    
      <p class="highlightBlock">If your XML Data Document contains special characters like - ?, &amp;  etc. in <span class="codeInline">dataXML</span> mode, you'll need to XML/URL Encode them. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>Effectively, in this process you need the following to build a chart:</p>
        <ol>
          <li><strong>Chart Container Page </strong>- The page which contains the HTML code  to embed and show the chart. It also contains the XML data to be used by the chart. </li>
          <li><strong>Chart SWF File</strong></li>
        </ol></td>
  </tr>
  <tr>    <td valign="top" class="text">&nbsp;</td>  </tr>
  <tr>
    <td valign="top" class="header">Sample Usage of dataXML method using FusionCharts JavaScript Class </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock">&lt;div id=&quot;chart1div&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;This text is replaced by the chart.<br />
&lt;/div&gt;<br />
&lt;script type=&quot;text/javascript&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;var chart1 = new FusionCharts(&quot;Column2D.swf&quot;, &quot;ChId1&quot;, &quot;600&quot;, &quot;400&quot;, &quot;0&quot;, &quot;0&quot;);
<strong><br />
&nbsp;&nbsp;&nbsp;chart1.setDataXML(&quot;&lt;chart&gt;&lt;set name='Data1' value='1' /&gt;&lt;/chart&gt;&quot;);</strong><br />
&nbsp;&nbsp;&nbsp;chart1.render(&quot;chart1div&quot;);<br />
&lt;/script&gt;</td>
  </tr>
    <tr>    <td valign="top" class="text">&nbsp;</td>  </tr>

  <tr>
    <td valign="top" class="header">Sample Usage of dataXML method using direct HTML Embedding Method </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock">&lt;object classid=&quot;clsid:d27cdb6e-ae6d-11cf-96b8-444553540000&quot; codebase=&quot;http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=8,0,0,0&quot; width=&quot;500&quot; height=&quot;300&quot; id=&quot;Column2D&quot; align=&quot;middle&quot;&gt;
      <br />
      &nbsp;&nbsp;&nbsp;&lt;param name=&quot;movie&quot; value=&quot;Column2D.swf?chartWidth=500&amp;chartHeight=300&quot; /&gt;<br />
      <strong>&nbsp;&nbsp;&nbsp;&lt;param name=&quot;FlashVars&quot; value=&quot;&amp;dataXML=&lt;chart&gt;&lt;set name='Data1' value='1' /&gt;&lt;/chart&gt;&quot; /&gt;</strong><br />
      &nbsp;&nbsp;&nbsp;&lt;param name=&quot;quality&quot; value=&quot;high&quot; /&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;embed src=&quot;Column2D.swf?chartWidth=500&amp;chartHeight=300&quot; <strong>FlashVars=&quot;&amp;dataXML=&lt;chart&gt;&lt;set name='Data1' value='1' /&gt;&lt;/chart&gt;&quot;</strong> quality=&quot;high&quot; bgcolor=&quot;#ffffff&quot; width=&quot;400&quot; height=&quot;300&quot; name=&quot;Column2D&quot; align=&quot;middle&quot; allowScriptAccess=&quot;sameDomain&quot; type=&quot;application/x-shockwave-flash&quot; pluginspage=&quot;http://www.macromedia.com/go/getflashplayer&quot; /&gt;<br />
    &lt;/object&gt;</td>
  </tr>
  
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="highlightBlock"><p><span class="text">With this method, however, there can sometimes be problems when you're working   with larger chunks of data. This problem occurs due to the limitation on dataXML string length imposed by the browser. When you   specify a larger chunk of data using dataXML   method, the browser ignores everything after a certain length. This causes   FusionCharts to hang (nothing would be displayed on-screen) as the full data has   not been supplied to it. Therefore, dataURL method   is recommended for larger chunks of data (basically - multi-series/combination   charts).</span></p>
    </td>
  </tr>
</table>
</body>
</html>
