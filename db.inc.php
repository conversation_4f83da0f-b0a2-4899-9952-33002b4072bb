<?php

if(!isset($_SESSION['dbOraUsuario']) ){ session_start(); }

require($_SERVER['DOCUMENT_ROOT']."/config/config.inc.php");

if ( isset($_SESSION['dbOraUsuario']) && isset($_SESSION['dbOraPassword']) ){

	$dbusuario = $ora_user; // aqui debes ingresar el nombre de usuario
	$dbpassword = $ora_pass; // password de acceso para el usuario de la
	$dbhost = $ora_host;

	$user = $dbusuario;
	$password = $dbpassword;
	$host = $dbhost;
	$db = '';


	$ora_conn = @oci_connect($dbusuario, $dbpassword, $dbhost);

	if (!$ora_conn){
		session_destroy();
		die("0|ERROR DE CONEXION, SESSION INEXISTENTE O USUARIO Y CONTRASEÑA INCORRECTA|ERROR DE CONEXION, SESSION INEXISTENTE O USUARIO Y CONTRASEÑA INCORRECTA");
	}else{
		$_SESSION['dbOraConn'] = 1;		
	}
}
?>
