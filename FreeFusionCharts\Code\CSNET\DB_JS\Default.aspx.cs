﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using DataConnection;
using InfoSoftGlobal;

public partial class DB_JS_Default : System.Web.UI.Page
{
    public string jsVarString;
    public int indexCount;
    protected void Page_Load(object sender, EventArgs e)
    {
        // This string will be built in ASP.Net and rendered at run-time as JavaScript
        GetjsVar();
        // Generate chart in Literal Control
        // Pie chart with sum of production for each of the factories
        FCLiteral1.Text = CreateChart();
        // Generate chart in Literal Control
        // Column 2D Chart with changed "No data to display" message
        FCLiteral2.Text = ShowNextChart();

    }
    public void GetjsVar()
    {

        //In this example, we show a combination of database + JavaScript rendering using FusionCharts.

        //The entire app (page) can be summarized as under. This app shows the break-down
        //of factory wise output generated. In a pie chart, we first show the sum of quantity
        //generated by each factory. These pie slices, when clicked would show detailed date-wise
        //output of that factory.

        //The XML data for the pie chart is fully created in ASP at run-time. ASP interacts
        //with the database and creates the XML for this.
        //Now, for the column chart (date-wise output report), we do not submit request to the server.
        //Instead we store the data for the factories in JavaScript arrays. These JavaScript
        //arrays are rendered by our ASP Code (at run-time). We also have a few defined JavaScript
        //functions which react to the click event of pie slice.

        //We've used an Access database which is present in ../DB/FactoryDB.mdb. 
        //It just contains two tables, which are linked to each other. 

        //Before the page is rendered, we need to connect to the database and get the
        //data, as we'll need to convert this data into JavaScript variables.

        //The following string will contain the JS Data and variables.
        //This string will be built in ASP and rendered at run-time as JavaScript.

        jsVarString = "";

        //Database Objects
        DbConn oRs; string strQuery;
        indexCount = 0;

        //Iterate through each factory
        strQuery = "select * from Factory_Master";
        oRs = new DbConn(strQuery);

        while (oRs.ReadData.Read())
        {
            indexCount++;

            //Create JavaScript code to add sub-array to data array
            //data is an array defined in JavaScript (see below)
            //We've added vbTab & vbCRLF to data so that if you View Source of the
            //output HTML, it will appear properly. It helps during debugging
            jsVarString += "\t\t data[" + indexCount + "] = new Array();" + "\n";

            //Now create second recordset to get date-wise details for this factory
            strQuery = "select * from Factory_Output where FactoryId=" + oRs.ReadData["FactoryId"].ToString() + " order by DatePro Asc";
            DbConn oRs2 = new DbConn(strQuery);
            while (oRs2.ReadData.Read())
            {
                //Put this data into JavaScript as another nested array.
                //Finally the array would look like data[factoryIndex][i][dataLabel,dataValue]
                jsVarString += "\t\t data[" + indexCount + "].push(new Array('" + Convert.ToDateTime(oRs2.ReadData["DatePro"]).ToString("dd/MM/yyyy") + "'," + oRs2.ReadData["Quantity"].ToString() + "));" + "\n";

            }
            //Close recordset
            oRs2.ReadData.Close();

        }
        oRs.ReadData.Read();

    }

    public string CreateChart()
    {
        //Initialize the Pie chart with sum of production for each of the factories
        //strXML will be used to store the entire XML document generated
        string strXML, strQuery;

        //Re-initialize Index
        indexCount = 0;

        //Generate the graph element
        strXML = "<graph caption='Factory Output report' subCaption='By Quantity' decimalPrecision='0' showNames='1' numberSuffix=' Units' pieSliceDepth='20' formatNumberScale='0' >";

        //Iterate through each factory
        strQuery = "select * from Factory_Master";
        DbConn oRs = new DbConn(strQuery);

        while (oRs.ReadData.Read())
        {
            //Update index count - sequential
            indexCount++;
            //Now create second recordset to get details for this factory
            strQuery = "select sum(Quantity) as TotOutput from Factory_Output where FactoryId=" + oRs.ReadData["FactoryId"].ToString();
            DbConn oRs2 = new DbConn(strQuery);
            oRs2.ReadData.Read();
            //Generate <set name='..' value='..' link='..' />
            //Note that we're setting link as updateChart(factoryIndex) - JS Function
            strXML += "<set name='" + oRs.ReadData["FactoryName"].ToString() + "' value='" + oRs2.ReadData["TotOutput"].ToString() + "' link='javascript:updateChart(" + indexCount + ")'/>";
            //Close recordset
            oRs2.ReadData.Close();

        }
        //Finally, close <graph> element
        strXML += "</graph>";
        oRs.ReadData.Read();

        //Create the chart - Pie 3D Chart with data from strXML
        return FusionCharts.RenderChart("../FusionCharts/FCF_Pie3D.swf", "", strXML, "FactorySum", "650", "300", false, true);

    }
    public string ShowNextChart()
    {
        //Column 2D Chart with changed "No data to display" message
        //We initialize the chart with <graph></graph>
        return FusionCharts.RenderChart("../FusionCharts/FCF_Column2D.swf?ChartNoDataText=Please click on a pie slice above to view detailed data.", "", "<graph></graph>", "FactoryDetailed", "600", "300", false, true);
    }
}
