<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<!-- InstanceBegin template="/Templates/Documentation Page.dwt" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- InstanceBeginEditable name="doctitle" -->
<title>FudionCharts DOM Documentation - API and Reference</title>
<style type="text/css">
	.gray-out {
		color: #666;
	}
</style>
<!-- InstanceEndEditable -->
<script type="text/javascript" src="js/lib.js"></script>
<link rel="stylesheet" type="text/css" href="assets/prettify/prettify.css">
<script type="text/javascript" src="assets/prettify/prettify.js"></script>
<link rel="stylesheet" type="text/css" href="css/typoset.css">
<!-- InstanceBeginEditable name="head" --><!-- InstanceEndEditable -->
</head>
<body>
<div id="wrapper">
  <noscript class="hl-red">
  &nbsp;<br />
  For maximum compatibility, it is requested that you browse this page in a JavaScript enabled browser.
  </noscript>
  <div id="topshadow"></div>
  <div id="viewport">
    <h2><!-- InstanceBeginEditable name="pagetitle" --> FusionCharts DOM API and Reference <!-- InstanceEndEditable --></h2>
    <div id="contents"><!-- InstanceBeginEditable name="pagebody" -->
      <h3>Chart Parameters (list of attributes for &lt;fusioncharts&gt; tag)</h3>
      <p>These parameters let you control a variety of functional elements on the chart. For example, you can set the chart height and width according to your wish. You can also set language option, scale mode using these parameters. These parameters are set as either attributes in our &lt;fusioncharts&gt; tag, or in the &quot;parameters&quot; attribute in the &lt;script&gt; tag. For more information on setting parameters, see the <a href="FCDomFeatures.html#defaultparams">parameter specification</a> section.</p>
      <p><span style="background-color:#CCCCCC">Grayed out attributes are applicable for FusionCharts v3 only</span>     </p>
      <table width="100%">
        <thead>
          <tr>
            <th width="20%">Parameter</th>
            <th width="25%">Type / Range</th>
            <th width="15%">Default Value</th>
            <th width="40%">Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>chartId</td>
            <td>Unique, Alphanumeric, Required</td>
            <td><em>Autogenerated</em></td>
            <td> This parameter turns compulsory when the 'autoChartId' setting is set to false.</td>
          </tr>
          <tr>
            <td>chartType</td>
            <td>Alphanumeric, Enumeration, Required</td>
            <td>Column2D</td>
            <td> See the '<a href="#charttype">Chart Types</a>' section below for the full list of chart types. </td>
          </tr>
          <tr>
            <td>width</td>
            <td>Positive Integer</td>
            <td>300</td>
            <td>Sets the chart width. It takes only pixel value. </td>
          </tr>
          <tr>
            <td>height</td>
            <td>Positive Integer</td>
            <td>200</td>
            <td>Sets the chart height  in pixels.</td>
          </tr>
          <tr>
            <td class="gray-out">debugMode</td>
            <td>Boolean (0/1)</td>
            <td>0</td>
            <td>Decides whether to  set the debug mode on. </td>
          </tr>
          <tr>
            <td class="gray-out">registerWithJS</td>
            <td>Boolean (0/1)</td>
            <td>0</td>
            <td>Lets you  register the chart with JavaScript.</td>
          </tr>
          <tr>
            <td>backgroundColor</td>
            <td>HexaDecimal color codes (Example: #6699cc)</td>
            <td><em>Not Specified</em></td>
            <td>Using this  you can set the background color of the chart. </td>
          </tr>
          <tr>
            <td class="gray-out">scaleMode</td>
            <td>Alphanumeric, Values: exactFit, noScale, showAll, noBorder</td>
            <td>noScale</td>
            <td>&nbsp;</td>
          </tr>
          <tr>
            <td class="gray-out">lang</td>
            <td>Alphanumeric, Language Codes</td>
            <td><em>Not Specified</em></td>
            <td>This attribute enables you to specify the language you want. </td>
          </tr>
          <tr>
            <td>detectFlashVersion</td>
            <td>Boolean (0/1)</td>
            <td><em>Not Specified</em></td>
            <td>Detects the Flash version, whether it is Flash 7 or 8. </td>
          </tr>
          <tr>
            <td>autoInstallRedirect</td>
            <td>Boolean (0/1)</td>
            <td><em>Not Specified</em></td>
            <td>Whether to download Flash components directly from Adobe. If the plug ins are not found,  this attribute is set to 1 to download Flash components from Adobe directly. </td>
          </tr>
          <tr>
            <td>chartVersion</td>
            <td>Alphanumeric, Values: v3, free</td>
            <td>free</td>
            <td>Using this attribute  you can set the chart's version according to the license you have acquired. </td>
          </tr>
          <tr>
            <td>swfPath</td>
            <td>Alphanumeric</td>
            <td>FusionCharts/</td>
            <td>Lets you set the relative or default path of the FusionCharts SWF file. </td>
          </tr>
          <tr>
            <td>dataURL</td>
            <td>Alphanumeric</td>
            <td><em>Not Specified</em></td>
            <td>You can set the path of the chart's data URL using this attribute. </td>
          </tr>
          <tr>
            <td>swfURI</td>
            <td>Alphanumeric</td>
            <td><em>Not Specified</em></td>
            <td>You can specify all FusionCharts parameter through an URI and set it as this parameter. If this parameter is set, it overrides all other parameters. </td>
          </tr>
        </tbody>
      </table>
 
      <h3>Chart Messages</h3>
      <p><span style="background-color:#CCCCCC">Grayed out attributes are applicable for FusionCharts v3 only</span></p>
      <table width="100%">
        <thead>
          <tr>
            <th width="20%">Parameter</th>
            <th width="17%">Type / Range</th>
            <th width="23%">Default Value</th>
            <th width="40%">Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>PBarLoadingText</td>
            <td>Alphanumeric</td>
            <td>Loading Chart. Please Wait</td>
            <td>You would see this message while the chart SWF is being loaded. </td>
          </tr>
          <tr>
            <td>XMLLoadingText</td>
            <td>Alphanumeric</td>
            <td>Retrieving Data. Please Wait</td>
            <td>To retrieve the chart XML data,  FusionCharts engine takes some time. During this interval the XMLLoadingText message will be displayed to the user. </td>
          </tr>
          <tr>
            <td>ParsingDataText</td>
            <td>Alphanumeric</td>
            <td>Reading Data. Please Wait</td>
            <td>To parse the chart XML data,  FusionCharts engine takes some time. During this interval the ParsingDataText message will be displayed to the user.</td>
          </tr>
          <tr>
            <td>ChartNoDataText</td>
            <td>Alphanumeric</td>
            <td>No data to display.</td>
            <td>It gets displayed if the chart fails to render due to the absence of data. </td>
          </tr>
          <tr>
            <td class="gray-out">RenderingChartText</td>
            <td>Alphanumeric</td>
            <td>Rendering Chart. Please Wait</td>
            <td>This message would be displayed while the chart is in the process of being rendered. </td>
          </tr>
          <tr>
            <td class="gray-out">LoadDataErrorText</td>
            <td>Alphanumeric</td>
            <td>Error in loading data.</td>
            <td>You would see this  message if the chart fails to render due to any error in loading the XML data. </td>
          </tr>
          <tr>
            <td class="gray-out">InvalidXMLText</td>
            <td>Alphanumeric</td>
            <td>Invalid XML data.</td>
            <td>This message would be displayed if the chart fails to render due to invalid XML data.</td>
          </tr>
        </tbody>
      </table>
 
   <a name="charttype"></a>
      <h3>Chart Types</h3>
      <p>The following is the list of charts available in this FusionCharts Free. The firendly names in this list are the possible values of the &quot;chartType&quot; attribute in the &lt;fusioncharts&gt; tag.</p>
      <table width="100%">
        <thead>
          <tr>
            <th width="25%">Friendly Name</th>
            <th width="39%">Description</th>
            <th width="36%"> Data Type Supported</th>
          </tr>
        </thead>
        <tbody>
          <tr >
            <td >Column2D</td>
            <td >2D Column chart</td>
            <td >Only Single Series</td>
          </tr>
          <tr>
            <td >Column3D</td>
            <td >3D Column chart</td>
            <td >Only Single Series</td>
          </tr>
          <tr>
            <td >Line2D</td>
            <td >Line Chart</td>
            <td >Only Single Series</td>
          </tr>
		  <tr >
            <td >Pie3D</td>
            <td >3D Pie chart</td>
            <td >Only Single Series</td>
          </tr>
		  <tr>
            <td >Pie2D</td>
            <td >2D Pie chart </td>
            <td >Only Single Series</td>
          </tr>
          <tr>
            <td >Bar2D</td>
            <td >Bar Chart</td>
            <td >Only Single Series</td>
          </tr>
          <tr>
            <td >Area2D</td>
            <td >Area Chart</td>
            <td >Only Single Series</td>
          </tr>
          <tr >
            <td >Doughnut2D / Donut2D</td>
            <td >2D Doughnut chart </td>
            <td >Only Single Series</td>
          </tr>
		  <tr>
            <td >MSColumn3D</td>
            <td >3D Column chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
		  <tr >
            <td >MSColumn2D</td>
            <td >2D Column Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
          <tr >
            <td >MSArea2D</td>
            <td >Area Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
		  <tr>
            <td >MSLine2D</td>
            <td >Line Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
          <tr >
            <td >MSBar2d </td>
            <td >2D Bar chart </td>
            <td >Preferably Multi-Series</td>
          </tr>          
          <tr >
            <td >StackedColumn2D</td>
            <td >Stacked 2D Column Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
		  <tr>
            <td >StackedColumn3D</td>
            <td >Stacked 3D Column Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
		  <tr >
            <td >StackedBar2D</td>
            <td >Stacked Bar Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>          
          <tr>
            <td >StackedArea2D</td>
            <td >Stacked Area Chart</td>
            <td >Preferably Multi-Series</td>
          </tr>
          <tr>
            <td >MSCombiDY2D</td>
            <td >Line &amp; 2D Column Combination Chart, Dual Y</td>
            <td >Only Multi-Series</td>
          </tr>
          
          <tr >
            <td >MSColumn3DLineDY</td>
            <td >Line &amp; 3D Column Combination Chart, Dual Y</td>
            <td >Only Multi-Series</td>
          </tr>          
          
          
		  <tr >
            <td >Candlestick</td>
            <td >Candlestick Chart</td>
            <td >Candlestick type </td>
          </tr>
		  <tr>
            <td >Funnel</td>
            <td >Funnel chart </td>
            <td >Only Single Series </td>
          </tr>
		  <tr>
            <td >Gantt</td>
            <td >Gantt chart </td>
            <td >Gantt type </td>
          </tr>
        </tbody>
      </table>
       
      <h3>FusionCharts DOM Behavior Settings</h3>
      <p>The settings are used mainpulate the behaviour of FusionCharts DOM. These properties are passed in the &quot;settings&quot; attribute of the &lt;script&gt; tag. For more information on manipulating settings, see the <a href="FCDomFeatures.html#advancedsettings">advanced settings</a> section.</p>
      <table width="100%">
        <thead>
          <tr>
            <th width="19%">Parameter</th>
            <th width="18%">Type / Range</th>
            <th width="23%">Default Value</th>
            <th width="40%">Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><p>debugMode<br />
                  <em>(alertOnErrors have been removed)</em><br />
              </p>            </td>
            <td>Boolean (0/1)</td>
            <td>false</td>
            <td>Shows error alerts if FusionChartsDOM encounters an error</td>
          </tr>
          
          
          <tr>
            <td>autoChartId</td>
            <td>Boolean (0/1)</td>
            <td>true</td>
            <td>Automatically generates chart id if it is not provided. If its set to false, raises an error when no unique chart id is not provided.</td>
          </tr>
          <tr>
            <td>containerClassName</td>
            <td>String</td>
            <td>&quot;fusioncharts_dom_container&quot;</td>
            <td>The class name for the element (default &lt;span&gt;) wrapping the FusionCharts object element.</td>
          </tr>
          <tr>
            <td>loadingMessage</td>
            <td>String</td>
            <td>&quot;Chart not loaded.&quot;</td>
            <td>The momentary message displayed while the DOM is being parsed.</td>
          </tr>
          <tr>
            <td>renderOnLoad</td>
            <td>Boolean (0/1)</td>
            <td>true</td>
            <td>If set to false, FusionChartsDOM parses the DOM but does not render the charts. The RenderAllChart() ir RenderChart(<em>id</em>) function needs to be called in order to render the charts.</td>
          </tr>
        </tbody>
      </table>
       
      <!-- InstanceEndEditable --></div>
    <div id="footer"><!-- InstanceBeginEditable name="pageFooter" --><!-- InstanceEndEditable --></div>
  </div>
</div>
</body>
<!-- InstanceEnd -->
</html>
