﻿/*
 * FusionCharts Free v2
 * http://www.fusioncharts.com/free
 *
 * Copyright (c) 2009 InfoSoft Global (P) Ltd.
 * Dual licensed under the MIT (X11) and GNU GPL licenses.
 * http://www.fusioncharts.com/free/license
 *
 * MIT License: http://www.opensource.org/licenses/mit-license.php
 * GPL License: http://www.gnu.org/copyleft/gpl.html
 *
 * Date: 2009-08-21
 */
defaultColors = new Array();
defaultColors[0] = "0099CC"; //Blue Shade
defaultColors[1] = "FF0000"; //Bright Red
defaultColors[2] = "006F00"; //Dark Green
defaultColors[3] = "0099FF"; //Blue (Light)
defaultColors[4] = "FF66CC"; //Dark Pink
defaultColors[5] = "996600"; //Variant of brown
defaultColors[6] = "669966"; //Dirty green
defaultColors[7] = "7C7CB4"; //Violet shade of blue
defaultColors[8] = "FF9933"; //Orange
defaultColors[9] = "CCCC00"; //Chrome Yellow+Green
defaultColors[10] = "9900FF"; //Violet
defaultColors[11] = "999999"; //Grey
defaultColors[12] = "99FFCC"; //Blue+Green Light
defaultColors[13] = "CCCCFF"; //Light violet
defaultColors[14] = "669900"; //Shade of green
defaultColors[15] = "1941A5"; //Dark Blue
