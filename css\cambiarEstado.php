<script type="text/javascript">

$(document).ready(function() {

	usuario = "<?php echo $usuario = $_GET['usuario']; ?>";
	$("#usuario").val(usuario);
	
	resulCargarDatos = cargarDatos();
	
	$("#old_password").focus();
	
	$("#btnOtroUsuario").click(function (){ 
		// funcion #003
		$("#main").load('cambios.php');
	});
	
	$("#btnProcesar").click(function (){ 
	// funcion #004
		if(confirm('Estas seguro que desea realizar el cambio?')){
			$("#status").fadeIn("Slow");	
			usuario = $("#usuario").val();
			var estado = $('#estado').attr('checked')?'S':'N';
			var campo = 'activo';
			var tipo = '1';
			nombre = $("#nombre").val();
			apellido= $("#apellido").val();
			if(estado != estadoActual){
				$.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: "id=6&usuario="+usuario+"&campo="+campo+"&valor="+estado+"&tipo="+tipo+"&nombre="+nombre+"&apellido="+apellido,
					success: function(msg)
					{
						var resultado = msg.split("|");
						if(resultado[0]=='6'){
							if(resultado[1]=='0'){
								$("#status").fadeOut("Fast");
								$("#main").html('Mensaje "#006 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
							}
							else if(resultado[1]=='1'){
								$("#status").fadeOut("Fast");
								$("#main").html(" Mensaje: El estado se modificó correctamente");
							}
							else{
								$("#status").fadeOut("Fast");
								$("#main").html('Mensaje "#006 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
							}
						}
						else{
							$("#status").fadeOut("Fast");
							$("#main").html('Mensaje en la funcion #006, favor consulte con el administrador facilitandole este dato.');
						}
					}
				});
			}
			else{
				$("#status").fadeOut("Fast");
				alert("Cambie el estado si desea procesar...!");
			}
		}
	});
});

function cargarDatos(){
// funcion #005
	$("#status").fadeIn("Fast");
	$.ajax({
		type: "POST",
		url: "commonFunctions.php",
		data: "id=5&usuario="+usuario,
		success: function(msg){
			msg = msg.replace(/^\s+/, "");
			var resultado = msg.split("|");
			if(resultado[0]=='5'){
				if(resultado[1]=='0'){
					$("#status").fadeOut("Fast");
					$("#main").html(resultado[2]);
				}
				else if(resultado[1]=='1'){
					$("#status").fadeOut("Fast");
					var adic = resultado[3].split(";");
					$("#nombre").val(adic[0]);
					$("#apellido").val(adic[1]);
					estadoActual = adic[3];
					if(estadoActual=='S'){
						$('#estado').attr('checked', true);
					}
					else{
						$('#estado').attr('checked', false);
					}
				}
				else{
					$("#status").fadeOut("Fast");
					$("#main").html('Mensaje "#005: '+resultado[2]+'".');
				}
			}
			else{
				$("#status").fadeOut("Fast");
				$("#main").html('Mensaje #005, favor consulte con el administrador facilitandole este dato.');
			}
		}
	});
	return true;
}

</script>


<div id="main" >
    <div id="content">
        <ul><li>EL "Estado Actual" indica el estado del usuario, marque o desmarque para realizar un cambio.</li></ul>
        <form id="cliente" method="post" action="">
            <table>
                    <tr>
                        <td class="label"><label id="lnombre" for="lnombre">Nombre</label></td>
                        <td class="field">
                        	<input id="nombre" name="nombre" type="text" value="" disabled/>
                        </td>
                    </tr>
                <tr>
                    <td class="label"><label id="lapellido" for="lapellido">Apellido</label></td>
                    <td class="field">
                    	<input id="apellido" name="apellido" type="text" value="" disabled/>
                	</td>
                </tr>
                <tr>
                    <td class="label"><label id="lusuario" for="lusuario">Usuario</label></td>
                    <td class="field">
                        <input id="usuario" name="usuario" type="text" value="" maxlength="20" disabled />
                    </td>
                    <td class="status"></td>
                </tr>
                <tr>
                    <td class="field" colspan="2">
			            <label id="l_estado" for="l_estado">Estado Actual</label>
                        <input id="estado" type="checkbox" name="estado" />
	  			</td>
                </tr>
                
              
            </table>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
             &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
           
            <input type="button" value="Procesar" id="btnProcesar" />
            <input type="button" value="Elegir otro usuario" id="btnOtroUsuario" />
        </form>
    </div>
</div>
