<%@ Language=VBScript %>
<HTML>
<HEAD>	
	<TITLE>
	FusionCharts Free - Client Side Dynamic Chart ( using Database) Example
	</TITLE>
	
	<%
	'We've included ../Includes/FusionCharts.asp, which contains functions
	'to help us easily embed the charts.	
	%>
	<!-- #INCLUDE FILE="../Includes/FusionCharts.asp" -->
	<!-- #INCLUDE FILE="../Includes/DBConn.asp" -->
	
	<%
	'In this example, we show a combination of database + JavaScript rendering using FusionCharts.
	
	'The entire app (page) can be summarized as under. This app shows the break-down
	'of factory wise output generated. In a pie chart, we first show the sum of quantity
	'generated by each factory. These pie slices, when clicked would show detailed date-wise
	'output of that factory.
	
	'The XML data for the pie chart is fully created in ASP at run-time. ASP interacts
	'with the database and creates the XML for this.
	'Now, for the column chart (date-wise output report), we do not submit request to the server.
	'Instead we store the data for the factories in JavaScript arrays. These JavaScript
	'arrays are rendered by our ASP Code (at run-time). We also have a few defined JavaScript
	'functions which react to the click event of pie slice.
	
	'We've used an Access database which is present in ../DB/FactoryDB.mdb. 
	'It just contains two tables, which are linked to each other. 
	
	'Before the page is rendered, we need to connect to the database and get the
	'data, as we'll need to convert this data into JavaScript variables.
	
	'The following string will contain the JS Data and variables.
	'This string will be built in ASP and rendered at run-time as JavaScript.
	Dim jsVarString
	jsVarString = ""
	
	'Database Objects
	Dim oRs, oRs2, strQuery, indexCount
	indexCount = 0
	
	'Create the recordset to retrieve data
	Set oRs = Server.CreateObject("ADODB.Recordset")

	'Iterate through each factory
	strQuery = "select * from Factory_Master"
	Set oRs = oConn.Execute(strQuery)
	
	While not oRs.EOF
		indexCount = indexCount + 1
		
		'Create JavaScript code to add sub-array to data array
		'data is an array defined in JavaScript (see below)
		'We've added vbTab & vbCRLF to data so that if you View Source of the
		'output HTML, it will appear properly. It helps during debugging
		jsVarString = jsVarString & vbTab & vbTab & "data[" & indexCount & "] = new Array();" & vbCRLF
		
		'Now create second recordset to get date-wise details for this factory
		Set oRs2 = Server.CreateObject("ADODB.Recordset")
		strQuery = "select * from Factory_Output where FactoryId=" & ors("FactoryId") & " order by DatePro Asc" & vbCRLF				
		Set oRs2 = oConn.Execute(strQuery)
		While not oRs2.EOF
			'Put this data into JavaScript as another nested array.
			'Finally the array would look like data[factoryIndex][i][dataLabel,dataValue]
			jsVarString = jsVarString &  vbTab & vbTab & "data[" & indexCount & "].push(new Array('" & datePart("d",ors2("DatePro")) & "/" & datePart("m",ors2("DatePro")) & "'," & ors2("Quantity") & "));" & vbCRLF
			oRs2.MoveNext()
		Wend
		'Close recordset
		Set oRs2 = Nothing
		oRs.MoveNext()
	Wend
	
	%>	
	<SCRIPT LANGUAGE="Javascript" SRC="../../FusionCharts/FusionCharts.js">
		//You need to include the above JS file, if you intend to embed the chart using JavaScript.
		//Embedding using JavaScripts avoids the "Click to Activate..." issue in Internet Explorer
		//When you make your own charts, make sure that the path to this JS file is correct. Else, you would get JavaScript errors.
	</SCRIPT>
	
	<SCRIPT LANGUAGE="JavaScript" >
		//Here, we use a mix of server side script (ASP) and JavaScript to
		//render our data for factory chart in JavaScript variables. We'll later
		//utilize this data to dynamically plot charts.
		
		//All our data is stored in the data array. From ASP, we iterate through
		//each recordset of data and then store it as nested arrays in this data array.
		var data = new Array();
		
		<%
		'Write the data as JavaScript variables here
		Response.Write(jsVarString)
		'The data is now present as arrays in JavaScript. Local JavaScript functions
		'can access it and make use of it. We'll see how to make use of it.
		%>
		
		/** 
		 * updateChart method is invoked when the user clicks on a pie slice.
		 * In this method, we get the index of the factory, build the XML data
		 * for that that factory, using data stored in data array, and finally
		 * update the Column Chart.
		 *	@param	factoryIndex	Sequential Index of the factory.
		*/		
		function updateChart(factoryIndex){
			//defining array of colors
			//We also initiate a counter variable to help us cyclically rotate through
			//the array of colors.
			var FC_ColorCounter=0;
			//var arr_FCColors= new Array(20);
			var arr_FCColors= new Array("1941A5" , "AFD8F8", "F6BD0F", "8BBA00", "A66EDD", "F984A1", "CCCC00", "999999", "0099CC", "FF0000", "006F00", "0099FF", "FF66CC", "669966", "7C7CB4", "FF9933", "9900FF", "99FFCC", "CCCCFF", "669900");
			
			
			//Storage for XML data document
			var strXML = "<graph caption='Factory " + factoryIndex  + " Output ' subcaption='(In Units)' xAxisName='Date' decimalPrecision='0'>";
			
			//Add <set> elements
			var i=0;
			for (i=0; i<data[factoryIndex].length; i++){
				strXML = strXML + "<set name='" + data[factoryIndex][i][0] + "' value='" + data[factoryIndex][i][1] + "' color='"+ arr_FCColors[++FC_ColorCounter % arr_FCColors.length] +"' />";
			}
			
			//Closing graph Element
			strXML = strXML + "</graph>";
						
						
						
						
						
			//Update it's XML
			updateChartXML("FactoryDetailed",strXML);

		}
	</SCRIPT>
	<style type="text/css">
	<!--
	body {
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	.text{
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	-->
	</style>
</HEAD>
	
<BODY>

<CENTER>
<h3><a href="http://www.fusioncharts.com" target="_blank">FusionCharts Free</a> Database + JavaScript Example</h3>
<h5>Inter-connected charts - Click on any pie slice to see detailed chart below.</h5>	
<p>The charts in this page have been dynamically generated using data contained in a database. We've NOT hard-coded the data in JavaScript.</p>
<%		
	'Initialize the Pie chart with sum of production for each of the factories
	'strXML will be used to store the entire XML document generated
	Dim strXML 
	
	'Re-initialize Index
	indexCount=0
	
	'Generate the graph element
	strXML = "<graph caption='Factory Output report' subCaption='By Quantity' decimalPrecision='0' showNames='1' numberSuffix=' Units' pieSliceDepth='20' formatNumberScale='0' >"
	
	'Move back to first index of the factory master recordset
	oRs.MoveFirst()
	
	While Not oRs.Eof
		'Update index count - sequential
		indexCount = indexCount + 1
		'Now create second recordset to get details for this factory
		Set oRs2 = Server.CreateObject("ADODB.Recordset")
		strQuery = "select sum(Quantity) as TotOutput from Factory_Output where FactoryId=" & ors("FactoryId")
		Set oRs2 = oConn.Execute(strQuery)				
		'Generate <set name='..' value='..' link='..' />
		'Note that we're setting link as updateChart(factoryIndex) - JS Function
		strXML = strXML & "<set name='" & ors("FactoryName") & "' value='" & ors2("TotOutput") & "' link='javascript:updateChart(" & indexCount & ")'/>"
		'Close recordset
		Set oRs2 = Nothing
		oRs.MoveNext
	Wend
	'Finally, close <graph> element
	strXML = strXML & "</graph>"
	Set oRs = nothing


	
	'Create the chart - Pie 3D Chart with data from strXML
	Call renderChart("../../FusionCharts/FCF_Pie3D.swf", "", strXML, "FactorySum", 650, 300)
	
	

%>
	<BR>
<%
	'Column 2D Chart with changed "No data to display" message
	'We initialize the chart with <graph></graph>
		Call renderChart("../../FusionCharts/FCF_Column2D.swf?ChartNoDataText=Please click on a pie slice above to view detailed data.", "", "<graph></graph>", "FactoryDetailed", 600, 300)

%>
<BR><BR>
<a href='../NoChart.html' target="_blank">Unable to see the chart(s) above?</a>
<BR><H5 ><a href='../default.htm'>&laquo; Back to list of examples</a></h5>
</CENTER>
</BODY>
</HTML>