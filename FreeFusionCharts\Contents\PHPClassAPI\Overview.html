<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" type="text/css" href="../Style.css">
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">FusionCharts PHP Class API &gt; Overview</h2></td>
  </tr>
  
  <tr>
    <td valign="top" class="text"><p>FusionCharts can be integrated with PHP to output interactive, data-driven charts.  <strong>Using with PHP</strong> section documents charting data from PHP array and forms, connecting to  database  to fetch data and create snazzy charts out of them and many more sample applications. </p>
      <p>While developing these applications in PHP, we  realized that a set of built-in PHP functions to perform various chart generation tasks could save us a lot of time! With that said, we release  FusionCharts PHP Class for FusionCharts Free! This class has some PHP  functions that can be used while chart configuration , XML generation and rendering. </p>
      <p>In the coming sections we would see how to use FusionCharts   PHP Class effectively so that charting with PHP becomes easier than ever!</p></td>
  </tr>
  
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
</table>
</body>
</html>
