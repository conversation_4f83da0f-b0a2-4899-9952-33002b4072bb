<?php
//We've included ../Includes/FusionCharts.php, which contains functions
//to help us easily embed the charts.
include("../Includes/FusionCharts.php");
?>
<HTML>
<HEAD>
	<META http-equiv="Content-Type" content="text/html;charset=UTF-8"/> 
	<TITLE>
	FusionCharts Free - UTF8 Français (French) Database Example
	</TITLE>
	<?php
	//You need to include the following JS file, if you intend to embed the chart using JavaScript.
	//Embedding using JavaScripts avoids the "Click to Activate..." issue in Internet Explorer
	//When you make your own charts, make sure that the path to this JS file is correct. Else, you would get JavaScript errors.
	?>	
	<SCRIPT LANGUAGE="Javascript" SRC="../../FusionCharts/FusionCharts.js"></SCRIPT>
	<style type="text/css">
	<!--
	body {
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	.text{
		font-family: Arial, Helvetica, sans-serif;
		font-size: 12px;
	}
	-->
	</style>
</HEAD>
<BODY>

<CENTER>
<h2><a href="http://www.fusioncharts.com" target="_blank">FusionCharts Free</a> - UTF8 Français (French) Example With Data from Database </h2>

<?php
	/*
	In this example, we show how to use UTF characters in chart created with FusionCharts 
	Here, the XML data for the chart is dynamically generated by PieDataJapanese.php. 
	*/
	/*
	In this example, FusionCharts uses dataURL method to get the xml from the data in the database. 
	In order to store and retrieve UTF8 characters from database, in our example, MySQL,
	we have to alter the database default charset to UTF8. This can be done using the command:
	ALTER DATABASE DEFAULT CHARACTER SET = utf8; on the "factorydb" database.
	For this example, we will use another table French_Factory_Master containing the names of the factories
	in French language. This table should also be defined to use UTF8 as DEFAULT CHARSET. 
	The sql script that needs to be executed before running this example is UTFExampleTablesCreation.sql 
	present in Code/PHP/DB folder.
	*/
	$strDataURL = "PieDataFrench.php";
	
	//Create the chart - Pie 3D Chart with dataURL as strDataURL
	echo renderChart("../../FusionCharts/FCF_Pie3D.swf", $strDataURL, "", "FactorySum", 650, 450);
?>
<BR><BR>
<a href='../NoChart.html' target="_blank">Unable to see the chart above?</a>
<H5 ><a href='../default.htm'>&laquo; Back to list of examples</a></h5>
</CENTER>
</BODY>
</HTML>