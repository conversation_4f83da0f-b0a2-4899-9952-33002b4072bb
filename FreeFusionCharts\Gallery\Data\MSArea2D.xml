<graph caption='Sales Volume' subcaption='For the month of Aug 2004' divlinecolor='F47E00' numdivlines='4' showAreaBorder='1' areaBorderColor='000000' numberPrefix='$' showNames='1' numVDivLines='29' vDivLineAlpha='30' formatNumberScale='1' rotateNames='1'  decimalPrecision='0'>
   <categories>
      <category name='08/01' /> 
      <category name='08/02' /> 
      <category name='08/03' /> 
      <category name='08/04' /> 
      <category name='08/05' /> 
      <category name='08/06' /> 
      <category name='08/07' /> 
      <category name='08/08' /> 
      <category name='08/09' /> 
      <category name='08/10' /> 
      <category name='08/11' /> 
      <category name='08/12' /> 
      <category name='08/13' /> 
      <category name='08/14' /> 
      <category name='08/15' /> 
      <category name='08/16' /> 
      <category name='08/17' /> 
      <category name='08/18' /> 
      <category name='08/19' /> 
      <category name='08/20' /> 
      <category name='08/21' /> 
      <category name='08/22' /> 
      <category name='08/23' /> 
      <category name='08/24' /> 
      <category name='08/25' /> 
      <category name='08/26' /> 
      <category name='08/27' /> 
      <category name='08/28' /> 
      <category name='08/29' /> 
      <category name='08/30' /> 
      <category name='08/31' /> 
   </categories>
   <dataset seriesname='Product A' color='FF5904' showValues='0' areaAlpha='50' showAreaBorder='1' areaBorderThickness='2' areaBorderColor='FF0000'>
      <set value='36634' /> 
      <set value='43653' /> 
      <set value='55565' /> 
      <set value='49457' /> 
      <set value='64654' /> 
      <set value='58457' /> 
      <set value='66456' /> 
      <set value='48765' /> 
      <set value='52574' /> 
      <set value='49546' /> 
      <set value='42346' /> 
      <set value='51765' /> 
      <set value='78456' /> 
      <set value='53867' /> 
      <set value='38359' /> 
      <set value='63756' /> 
      <set value='45554' /> 
      <set value='6543' /> 
      <set value='7555' /> 
      <set value='4567' /> 
      <set value='7544' /> 
      <set value='6565' /> 
      <set value='6433' /> 
      <set value='3465' /> 
      <set value='3574' /> 
      <set value='6646' /> 
      <set value='4546' /> 
      <set value='9565' /> 
      <set value='5456' /> 
      <set value='5667' /> 
      <set value='4359' /> 
   </dataset>
   <dataset seriesname='Product B' color='99cc99' showValues='0' areaAlpha='50' showAreaBorder='1' areaBorderThickness='2' areaBorderColor='006600'>
      <set value='12152' /> 
      <set value='15349' /> 
      <set value='16442' /> 
      <set value='17551' /> 
      <set value='13478' /> 
      <set value='16553' /> 
      <set value='17338' /> 
      <set value='17263' /> 
      <set value='16552' /> 
      <set value='17649' /> 
      <set value='12442' /> 
      <set value='11151' /> 
      <set value='15478' /> 
      <set value='16553' /> 
      <set value='16538' /> 
      <set value='17663' /> 
      <set value='13252' /> 
      <set value='16549' /> 
      <set value='14342' /> 
      <set value='13451' /> 
      <set value='15378' /> 
      <set value='17853' /> 
      <set value='17638' /> 
      <set value='14363' /> 
      <set value='10952' /> 
      <set value='10049' /> 
      <set value='19442' /> 
      <set value='13951' /> 
      <set value='19778' /> 
      <set value='18453' /> 
      <set value='17338' /> 
   </dataset>
</graph> 