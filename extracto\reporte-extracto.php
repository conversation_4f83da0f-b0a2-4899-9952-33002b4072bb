<?php ?>
<script  type="text/javascript">
    var explode = function () {
        jQuery("#recaudadora").select2();
    };
   // setTimeout(explode, 500);

    cmbBanco();
    entidadSubRed();

    function entidadSubRed() { 
        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: "id=15",
            success: function (msg) {
                msg = msg.replace(/^\s+/, "");
                var resultado = msg.split("|");
                if (resultado[1] == 1) {
                    $("#recaudadora").html(resultado[3]);
                    $("#status").fadeOut("Fast");
                    //$("table").tablesorter();
					// setTimeout(explode, 500);
					 jQuery("#recaudadora").select2();
                } else {
                    $("#status").fadeOut("Fast");
                    $("#main").html('Error"#15 ' + resultado[2] + '",consulte con el administrador facilitandole este dato.');
                }
            }
        });
    }
    function cmbBanco() {
        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: "id=95",
            success: function (msg) {
                msg = msg.replace(/^\s+/, "");
                var resultado = msg.split("|");
                if (resultado[1] == 1) {
                    var cmbbanco = "<select name='cmbBanco' id='cmbBanco' style='width: 100px;'> <option value = '*' >.:Todos:.</option> " + resultado[3] + "</select>";
                    $("#divBanco").html(cmbbanco);
                    //$("#cmbBanco").html(resultado[3]);
                    $("#status").fadeOut("Fast");
                    //$("table").tablesorter();
                } else {
                    $("#status").fadeOut("Fast");
                    $("#main").html('Error"#15 ' + resultado[2] + '",consulte con el administrador facilitandole este dato.');
                }
            }
        });
    }


    $("#btnGenerarReporte").click(function () {
        var error = 0;
        var fecha = $("#fecha").val();
        var fecha_fin = $("#fecha_fin").val();
        
        var rec = $("#recaudadora").val();
        var moneda = $("#cmbMoneda").val();
        var banco = $("#cmbBanco").val();
        var saldo = $("#cmbSaldo").val();
        var tipoCredito = $("#tipoCredito").val();
        //var remesa = $("#cmbRemesa").val();
        var subRed = $("#subRed").val();
        var entRecupero = $("#cmbEntRecupero").val();
        var filtroFecha = $("#filtroFecha").val();

        var termAct = $("#cmbTermAct").val();
        var modeloPos = $("#cmbModeloPos").val();
        
        if ((fecha == '') || (fecha_fin == '')) {
            error = 1;
            var msg = 'ERROR: Uno de los campos fecha se encuentra vacio';
        }

        if (error == 1) {
            alert(msg);
        } else {
            var condicion = "id=66&fecha=" + fecha + "&fechafin=" + fecha_fin + "&rec=" + rec + "&moneda=" + moneda + "&banco=" + banco + "&saldo=" + saldo + "&tipoCredito=" + tipoCredito + "&subRed=" + subRed + "&entRecupero=" + entRecupero + "&filtroFecha=" + filtroFecha + "&modeloPos=" + modeloPos + "&termAct=" + termAct;
            if (confirm('Esta seguro que desea realizar la consulta, puede demorarse unos segundos?')) {
                // $("#filtros").hide(300);
                // $('#btnGenerarReporte').attr('disabled', '-1');
                $("#status").fadeIn("Slow");
                $.ajax({
                    type: "POST",
                    url: "commonFunctions.php",
                    data: condicion,
                    success: function (msg) {
                        ///  alert (msg);
                        msg = msg.replace(/^\s+/, "");
                        var resultado = msg.split("|");
                        if (resultado[1] == 1) {
                            $("#main").find('#main1').html(resultado[3]);
                            $("#status").fadeOut("Fast");
                            $("table").tablesorter();
                        } else {
                            $("#status").fadeOut("Fast");
                            $("#main").html('Error"#013 ' + resultado[2] + '",consulte con el administrador facilitandole este dato.');
                        }
                    }
                });
            }
        }
    });


</script>

<div id="filtros" align="center">
    <ul>
        <li><b>Reporte RESUMEN c/ SALDO (Fec. PAGO) </b><br>
        </li>
    </ul>
    <div id="divFechaInicial"> 
        <script type="text/javascript">

            $("#fecha").datepicker({changeYear: true, changeMonth: true, firstDay: 1, dateFormat: 'dd/mm/yy'});
            $("#fecha_fin").datepicker({changeYear: true, changeMonth: true, firstDay: 1, dateFormat: 'dd/mm/yy'});
        </script>
        <table border="0">

            <tbody>
                <tr>
                    <td>Fecha Desde: </td>
                    <td><input name="fecha" id="fecha" class="date" /><span style="color: red; font-weight: bold; font-size: 12px">*</span></td>
                </tr>
                <tr>
                    <td>Fecha Hasta: </td>
                    <td><input name="fecha_fin" id="fecha_fin" class="date" /><span style="color: red; font-weight: bold; font-size: 12px">*</span> </td>
                </tr>

                <tr>
                    <td>Ordenar Fecha:</td>
                    <td><select name="filtroFecha" id="filtroFecha" style="width: 100px;">
                            <option value='1'>Ascendente</option>
                            <option value='2'>Descendente</option>
                        </select></td>
                </tr>

                <tr>
                    <td>Recaudadora: </td>
                    <td><select id="recaudadora" name="recaudadora">
                        </select></td>
                </tr>
            </tbody>
        </table>
        <table>            
            <tbody>
                <!-- -->
                <tr>
                    <td>Sub Red: </td>
                    <td><select name="subRed" id="subRed" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value='S'>Si</option>
                            <option value='N'>No</option>
                        </select></td>

                    <td>Moneda:</td>
                    <td><select name="cmbMoneda" id="cmbMoneda" style="width: 100px;">
                            <option value='1'>GUARANI</option>
                            <option value='2'>DOLAR</option>
                        </select></td>

                    <td>Terminal Activa:</td>
                    <td><select name="cmbTermAct" id="cmbTermAct" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value='S'>Si</option>
                            <option value='N'>No</option>
                        </select></td>

                </tr>

                <tr>
                    <td>Banco:</td>
                    <td><select name="cmbBanco" id="cmbBanco" style="width: 100px;"> 
                            <option value="30">AMAMBAY S.A.</option>
                        </select></td>

                    <td>Remesa:</td>
                    <td><select name="cmbRemesa" id="cmbRemesa" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value='S'>Si</option>
                            <option value='N'>No</option>
                        </select></td>
                    <td>Modelo Pos:</td>
                    <td><select name="cmbModeloPos" id="cmbModeloPos" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value="0">ePos</option>
                            <option value="7">PosWeb</option>
                            <option value="14">ePos2</option>
                            <option value="9">APIPagos</option>
                            <option value="2">Pagos Web</option>
                            <option value="1">Verix</option> 
                            <option value="3">Esalud</option>
                            <option value="4">IBM</option>
                            <option value="5">Movil Celular</option>
                            <option value="6">Bpos</option>
                            <option value="10">PosCobradoresMovil</option> 
                            <option value="11">Pos Bepsa</option>
                            <option value="13">PosMovil</option>
                            <option value="8">HomeBankingWS</option>
                            <option value="12">Cajero Automatico</option>
                        </select></td>
                </tr>

                <tr>
                    <td>Tipo de Credito:</td>
                    <td><select name="tipoCredito" id="tipoCredito" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value='Pos'>Pos pago</option>
                            <option value='Pre'>Pre pago</option>
                        </select></td>

                    <td>Ver Ent. Recupero:</td>
                    <td><select name="cmbEntRecupero" id="cmbEntRecupero" style="width: 100px;">
                            <option value='N'>No</option>
                            <option value='S'>Si</option>
                        </select></td>
                </tr>

                <tr>
                    <td>Saldo:</td>
                    <td><select name="cmbSaldo" id="cmbSaldo" style="width: 100px;">
                            <option value='*'>.:Todos:.</option>
                            <option value='positivo'>Positivo</option>
                            <option value='negativo'>Negativo</option>
                        </select></td>
                </tr>

                <!-- -->
                <tr>
                    <td colspan="4"><span style="color: red; font-weight: bold; font-size: 10px">* Campos requeridos</span></td>
                </tr>
                <tr>
                    <td>&nbsp;</td><td>&nbsp;</td>
                    <td><input type="button" value="Generar Reporte" id="btnGenerarReporte"></td>
                </tr>
            </tbody>
        </table>
        <input type="hidden" name="des_rec" id="des_rec" value="">
    </div>
</div>

<div id="main1"></div>
