body {
background-color: #FFFFFF;
background-image:url(../img/bodybg.png);
color:#666666;
font-family:Tahoma,sans-serif,Verdana,Arial,Helvetica;
font-size:0.8em;
margin:0;
padding:0;
}
h1 {
border-bottom:5px solid #EEEEEE;
color: #333333;
font-family:"Trebuchet MS",<PERSON><PERSON>,Tahoma;
font-size:1.5em;
font-weight:normal;
height:20px;
margin-bottom:20px;
padding:0 0 10px;
}
h2 {
color:#336699;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.3em;
font-weight:100;
height:20px;
margin-bottom:10px;
margin-top:10px;
padding:0 0 10px;
width:80%;
}
h3 {
color:#669900;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.2em;
font-weight:100;
height:20px;
margin-bottom:10px;
margin-top:10px;
padding:0 0 10px;
width:80%;
}
a {
color:#336699;
text-decoration:underline;
}
.input {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
.input:hover {
background-color:#CCD5E6;
}
.input a {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
input:hover {
background-color:#CCD5E6;
border:1px solid #AAB3DD;
color:#000000;
}
input {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
select {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
textarea {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
height:40px;
height:40px;
width:180px;
}
.button {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
height:22px;
width:250px;
}
#main {
background-color:#FFFFFF;
border-color:#ADBFCC -moz-use-text-color -moz-use-text-color;
border-style:solid none none;
border-width:5px 0 0;
margin-left:30px;
margin-right:30px;
margin-top:0;
min-width:800px;
padding-bottom:10px;
padding-top:0;
}
#mainReportes {
background-color:#FFFFFF;
border-color:#ADBFCC -moz-use-text-color -moz-use-text-color;
border-style:solid none none;
border-width:2px 0 0;
margin-left:0px;
margin-right:0px;
margin-top:0;
min-width:1200px;
padding-bottom:10px;
padding-top:0;
}
#main2 {
background-color:#FFFFFF;
border-color:#ADBFCC -moz-use-text-color -moz-use-text-color;
border-style:solid none none;
border-width:5px 0 0;
margin:0;
padding-bottom:10px;
padding-top:0;
}
#header {
background-position:left center;
background-repeat:no-repeat;
border:0 none;
height:125px;
margin:0 30px 0;
min-width:800;
padding:0;
}
#header img {
display:block;
float:left;
margin-top:-8px;
}
#header h1 {
margin:0;
padding:0;
}
#header h1 a {
display:block;
height:70px;
left:30px;
position:absolute;
text-indent:-9000px;
top:20px;
width:220px;
}
#header ul {
	float:left;
	height:36px;
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin-left:0;
	margin-top:92px;
	min-width:800;
	padding-left:0;
	position:absolute;
	left: 31px;
	top: -2px;
	width: 911px;
}
#header ul li {
display:inline;
margin:0;
padding:0;
text-align:center;
}
#header ul li a {
background:transparent url(img/bg_tabs4.png) no-repeat scroll 0 0;
color: #FFFFFF;
display:table-cell;
float:left;
font-weight:bold;
height:25px;
margin-left:5px;
padding-top:8px;
text-align:center;
text-decoration:none;
vertical-align:bottom;
width:100px;
}
#header li a:hover, #header li a:focus {
background-position:0 -33px;
color:#000000;
}
.selected_menu {
background-position:0 -33px;
color:#000000;
}
#foto {
background-repeat:no-repeat;
float:right;
height:45px;
margin-top:0;
width:45px;
}
#usuarioInfo {
float:right;
height:45px;
margin-top:0;
width:295px;
margin-right:5px;
}
#usuarioInfo h1 {
border:0 none;
color:#EEEEEE;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.6em;
margin-bottom:10px;
padding-top:10px;
text-indent:83px;
}
#usuarioInfo img {
border:0 none;
float:right;
margin-right:0;
}
#usuarioInfo a {
color:#CCCCCC;
padding-left:65px;
text-decoration:none;
}

#empresaInfo {
/*background-image:url(img/logo.jpg);*/
background-repeat:no-repeat;
float:left;
height:85px;
margin-top:0;
width:309px;
}
#empresaInfo h1 {
border:0 none;
color:#EEEEEE;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.6em;
margin-bottom:10px;
padding-top:10px;
text-indent:30px;
}
#empresaInfo img {
border:0 none;
float:left;
margin-right:0;
}
#empresaInfo a {
color:#CCCCCC;
padding-left:65px;
text-decoration:none;
}

#footer {
color:#FFFFFF;
margin-left:30px;
margin-right:30px;
padding-bottom:10px;
padding-top:10px;
text-align:right;
}

#adic {
color: #FFFFFF;
margin-left:30px;
margin-right:30px;
padding-bottom:10px;
padding-top:10px;
text-align:right;
}


#cliente label.error {
  background:url("../img/unchecked.gif") no-repeat 0px 0px;
  padding-left: 16px;
  padding-bottom: 2px;
  font-weight: bold;
  color: #EA5200;
}

#cliente label.checked {
  background:url("../img/checked.gif") no-repeat 0px 0px;
}

th {
	padding: 2px 30px 2px 6px;
	font-size: 12px;
	font-weight: bold;
	text-align: left;
	background-color:#E6EEEE;
}

.new_th {
	padding: 2px 30px 2px 6px;
	font-size: 10px;
	font-weight: bold;
	text-align: left;
	background-color:#E6EEEE;
}

td {
	padding: 2px 30px 2px 6px;
	font-size: 12px;
	text-align: left;
}

.mostrar {
	padding: 2px 30px 2px 6px;
	font-size: 10px;
	text-align: left;
}

.pendiente {
	color:#FF0000;
}

.disponible {
	color:#00FF00;
}

.ticket{
	border:1px dashed #CCCCCC;
	font-family:Arial, Helvetica, sans-serif;
	/*margin:150px 150px 150px 150px;*/
	margin-left:375px;
	width:200px;
	padding:2px;
	background-color:#FFFFFF;
}