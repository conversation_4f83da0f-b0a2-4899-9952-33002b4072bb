<?php
include("../db.inc.php");	
include("../class/class.log4My.php");
$log = new log4My(); //Se instancia la clase.
$idXLog = $log->getId4Log(); //Se genera el identificador.
session_start();
?>

<script id="demo" type="text/javascript">

const codEmpresa = <?php echo json_encode($_SESSION['codEmpresa']); ?>;

$(document).ready(function() {
  
  $("#frmEditar").hide('slow', function(){
	   $("#filtros").slideDown('slow'); 
	});


	//generar reporte
	$("#btnGenerarReporte").click(function (){
		$("#reportes").hide();
		$("#reportes").html("");
	  reporte(); 

	});

	$("#fecha_desde").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});
  $("#fecha_hasta").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});

});


function reporte(){
		

	var datos = "id=165&tipo_operacion=1"; 

	if($("#fecha_desde").val().length > 0){
			console.log('fecha_desde is ok');
		}else{ 
		   alert('Ingrese Fecha Desde');
		   return false;
	  }

	  if($("#fecha_hasta").val().length > 0){
			console.log('fecha_hasta is ok');
		}else{ 
		   alert('Ingrese Fecha Hasta');
		   return false;
	  }
	$("#status").fadeIn("Fast");	
	  
	datos += "&terminal="+$("#terminal").val();
	datos += "&lote="+$("#lote").val();
	datos += "&trx="+$("#trx").val();
	datos += "&fecha_hasta="+$("#fecha_hasta").val();		
	datos += "&fecha_desde="+$("#fecha_desde").val();	
	datos += "&estado="+$("#estado").val();	
	datos += "&documento="+$("#doc").val();	
	datos += "&mtcn="+$("#mtcn").val();
	datos += "&codEmpresa=" + codEmpresa;
	
	$.ajax({
		type: "POST",
		url: "commonFunctions.php",
		data: datos,
		success: function(msg){	
			msg = msg.replace(/^\s+/, "");
			var resultado = msg.split("|");
			if(resultado[0] == 1){
				$("#frmEditar").hide('slow');
				//$("#filtros").hide('slow'); 			
				$("#reportes").html("<div align='center' style='overflow: scroll;' >"+resultado[1]+"</div>");								
				$("#status").fadeOut("Fast");							
				$("#reportes").show(); 		
				$("#reportes").slideDown('slow'); 
			}else{
				$("#status").fadeOut("Fast");
				alert(resultado[1]);
				//$("#main").html('Error"#021 '+resultado[1]+'",consulte con el administrador facilitandole este dato.');
			}
		}
	});

}

function editar(terminal,lote,trx,estado){	
	
	//if(estado =="BLOQUEADA" || estado =="R" || estado =="L"){
		$("#frmEditar").slideDown('slow');	
		$(".lala").show();

		$("#terminale").val(terminal).attr('disabled','-1');
		$("#lotee").val(lote).attr('disabled','-1');
		$("#trxe").val(trx).attr('disabled','-1');
		$("#estadoe").val(estado);
		$("#obs").val("");


		window.scroll(0,0);
	//}
}



function guardarUsuario(){	

	var datos = "id=165&tipo_operacion=2";

	datos += "&terminal="+$("#terminale").val();
	datos += "&lote="+$("#lotee").val();
	datos += "&trx="+$("#trxe").val();
	datos += "&fecha_hasta=";		
	datos += "&fecha_desde=";	
	datos += "&obs="+$("#obs").val();	
	datos += "&estado="+$("#estadoe").val();	


	if( $("#terminale").val() == '' ){ alert('Ingrese  terminale'); return false; }
	if( $("#lotee").val() == '' ){ alert('Ingrese lotee'); return false; }
	if( $("#trxe").val() == '' ){ alert('Ingrese trxe'); return false; }
	if( $("#estado").val() == '' ){ alert('Ingrese estado'); return false; }
	if( $("#obs").val() == '' ){ alert('Ingrese obs'); return false; }

  $("#status").fadeIn("Fast");

	  $.ajax({
		type: "POST",
		url: "commonFunctions.php",
		data: datos,
		success: function(msg){	
			msg = msg.replace(/^\s+/, "");
			var resultado = msg.split("|");
			if(resultado[0] == 1){
				$("#status").fadeOut("Fast");
				alert(resultado[2]);
				$("#reportes").html("");								
				$("#reportes").hide();
				$("#btnGenerarReporte").click();
				
			}else{
				$("#status").fadeOut("Fast");
				//$("#main").html(resultado[1]);
				alert(resultado[2]);
			}
		}

	});

}
	
</script>
<!-- CUERPO HTML INICIAL -->
<div align="center"><ul><li><b>Transaciones Wester Union.</b></li></ul></div><br>
<div id='mensajes' style='color: #FF0000; display:none;'></div>
<div id="opciones" align="center">
</div>

<!-- filtro in -->
<!-- filtro in -->
<div id="filtros" align="center" style="display:none">
    <table border="0">    
    	 <tr>
        <td>Fecha Desde: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="fecha_desde"  name="fecha_desde" /></div>
        </td>
      </tr>
      <tr>
        <td>Fecha Hasta: </td>
        <td><div id="">
        		<input type="text" id="fecha_hasta"  name="fecha_hasta" /></div>
        </td>
      </tr>
    	<tr>
        <td>Terminal: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="terminal" placeholder="Opcional"  name="terminal" /></div>
        </td>
      </tr>
      <tr>
        <td>Lote: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="lote"  placeholder="Opcional" name="lote" /></div>
        </td>
      </tr>

      <tr>
        <td>Trx: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="trx" placeholder="Opcional"  name="trx" /></div>
        </td>
      </tr>

      <tr>
        <td>MTCN: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="mtcn" placeholder="Opcional"  name="mtcn" /></div>
        </td>
      </tr>

      <tr>
        <td>DOCUMENTO: </td>
        <td><div id="divNumeroDocumento">
        		<input type="text" id="doc" placeholder="Opcional"  name="doc" /></div>
        </td>
      </tr>


      <tr>
        <td>Estado: </td>
        <td>
        		<select id="estado" name="estado" >
        			<option value="*">TODOS</option>
        			<option value="B">BLOQUEADO</option>
        			<option value="A">APROBADO</option>
        			<option value="N">TRANSACCION RECHAZADA</option>
        			<option value="L">AUTORIZADO</option>
        			<option value="R">RECHAZADO CUMPLIMIENTO</option>
        		</select>
      	</td>
      </tr>
      <tr>
        <td>&nbsp;</td>
        <td><input type="button" value="Consultar" id="btnGenerarReporte" /></td>
      </tr>
    </table>
</div>
<br><br><br>
<!-- filtro out -->

<!-- editar in -->
<div id='frmEditar' style='display:none;' align="center">
<form id='frmEditarUsuario'>
<table>	
	<tr>
		<td>Terminal: </td>
        <td>
        		<input type="text" id="terminale"  name="terminale" />
        </td>					
	</tr>	
	<tr class='lala'>
		<td>Lote: </td>
        <td>
        		<input type="text" id="lotee"  name="lotee" />
        </td>
	</tr>
 	<tr class='lala'>
		<td>Trx: </td>
        <td>
        		<input type="text" id="trxe"  name="trxe" />
        </td>		
	</tr>
	<tr class='lala'>
		<td>Estado: </td>
        <td>
        		<select id="estadoe" name="estadoe" >
        			<!--<option value="B">BLOQUEADO</option>-->
        			<!--<option value="A">APROBADO</option>-->
        			<!--<option value="N">NO AUTORIZADO</option>-->
        			<option value="L">AUTORIZADO</option>
        			<option value="R">RECHAZADO</option>
        		</select>
      	</td>			
	</tr>	
	<tr class='lala'>
		<td>Observacion: </td>
        <td>
        		<input type="text" id="obs"  name="obs" />
      	</td>			
	</tr>			
	<tr class='lala'>
		<td>&nbsp;</td>
		<td align='right'><center><input onclick=" javascript: guardarUsuario();" name='guardar' type='button' id='guardar' value='Guardar' /></center></td>		
	</tr>							
	</table>
	</form>
</div>
<br><br><br>
<!-- editar out -->

<div id="reportes"></div>