<?php
error_reporting(E_ALL);
if(! isset($_SESSION['dbOraUsuario']) ){ session_start(); }
ini_set("display_errors", 1); 
include_once("class/class.log4My.php");
$log = new log4My(); //Se instancia la clase.
$idXLog = $log->getId4Log(); //Se genera el identificador.
$config_max_log = 5;
$log->write($idXLog, 0, __FILE__, 'init file');


function consultarSQL($query, $ubicacion, $logNoError = 1){
	global $ora_conn; global $idXLog; global $log; 	
	$programa = substr(strrchr ($ubicacion, "/"), 1);
	
	if($logNoError == 1){ $log->write($idXLog, 0, $ubicacion, 'QUERY: '.$query); }
	if($logNoError == 2){ $log->write($idXLog, 3, $ubicacion, 'QUERY: '.$query); }	
	
	$id_sentencia = @oci_parse( $ora_conn, $query );	
	if (!$id_sentencia){
		$e = oci_error($ora_conn);
		$log->write($idXLog, 3, $ubicacion, 'ERROR: '.$e['message'].' ERROR: '.$query);						
		return false;		
	}		
	$r = @oci_execute( $id_sentencia, OCI_DEFAULT );				
	if ( !$r ){
		$e = oci_error($id_sentencia);
		$log->write($idXLog, 3, $ubicacion, 'ERROR: '.$e['message'].' ERROR: '.$query);						
		return false;		
	}	
	return $id_sentencia;
}

if(isset($_POST['usuario']) && isset($_POST['pass'])){
	$_SESSION['dbOraUsuario'] = $_POST['usuario'];
	$_SESSION['dbOraPassword'] = $_POST['pass'];
	$_SESSION['dbOraConn'] = 0;
	include("db.inc.php");
	if($_SESSION['dbOraConn'] != 1){ die("0|0|ERROR DE USUARIO Y/O PASSWORD"); }
	$log->write($idXLog, 0, __FILE__, $user." ".$password." ".$host." ".$db);		
}else{
	die("0|0|ERROR NO SE RECIBIO USUARIO Y/O PASSWORD");
}

$_SESSION['inicioSesion_gi'] = 0;
$_SESSION['infoXuser_ge'] = '';


if(isset($_POST['usuario']) and isset($_POST['pass'])) {
	$log->write($idXLog, 0, __FILE__, $_POST['usuario']." ".$_POST['pass']);
	sleep(1); //Duermo asi se ve la barra de carga.
	$nom_usuario = '';
	$nom_empresa = '';
	$user = $_POST['usuario']; // Limpio el usuario y lo almaceno
	$pass = $_POST['pass'];   // Limpio la contraseña y la almaceno
	$codEmpresa = $_POST['codEmpresa'];
	unset($POST); // Elimino en resto de los valores
	$r_getUsuario = getUsuario($log, $idXLog, 0, $user, $pass);
	$vars = explode("|", $r_getUsuario );
	$id = $vars[0]; $est = $vars[1];  $msg = $vars[2];  $adic = $vars[3]; 
	unset($vars);
	if($est == 1){
	$_SESSION['inicioSesion_gi'] = 1;
	$vars = explode(";", $adic );
	$_SESSION['codUsuario'] = $vars[0];
	$_SESSION['usuario'] = $vars[1];
	$_SESSION["codEmpresa"] = $vars[2];
	$_SESSION["codPerfil"] = $vars[3];
	$_SESSION["email"] = $vars[4];
	$_SESSION["codPerfilInterno"] = $vars[5];
	$_SESSION["codPersonaGestionInterna"] = $vars[6];
	$log->write($idXLog, 0, 'codPersonaGestionInterna', 'ERROR: '.$_SESSION["codPersonaGestionInterna"]);		
	unset($vars);
	
	$codUsuario = $_SESSION['codUsuario'];
	
		if(isset($_SESSION['inicioSesion_gi']) and ($_SESSION['inicioSesion_gi'] == 1)){
			include_once('utils.php');
			$empresaInfo = generarEmpresaInfo();
			$menu = generarMenu($_SESSION["codPerfilInterno"]);
			$infoUsuario = generarUsuarioInfo($_SESSION['usuario']);
			//$foto = getFoto();
			oci_close($ora_conn);
			$msg = 'OK';
			$adic = $empresaInfo."*".$menu."*".$infoUsuario."*".cambiarEstilosPorEmpresa();
			$_SESSION['infoXuser_ge'] = $adic;
			//$log->write($idXLog, 0, 'gestorLogin.php/gestorLogin', 'resultado: '.$adic);
			echo '1|1|'.$msg.'|'.$adic;
		}
	}else{
		$msg = 'Existe un error con su usuario/contrase&ntilde;a, o no posee permisos suficientes para ingresar al sistema. ';
		$log->write($idXLog, 0, 'gestorLogin.php', 'ERROR: '.$msg);
		echo '1|2|'.$msg.'|0';
	}
}else{
$log->write($idXLog, 0, __FILE__, 'NO LLEGO USUARIO Y O PASSWORD, Contacte con TicPro');	
}

function getFoto(){
//Muestra la foto del usuario.
	$img = '<img src="img/usuarios/default.jpg" height="50" width="50"></br>';
	return $img;
}
function generarMenu($codPerfil){
// Genera el menu, hasta ahora estático.
	$links_a_ignorar = [
		'eficash/reportes.php',
		'petrobras/petrobras.php',
	];
	$ubicacion = getcwd()."/".__FUNCTION__;
	if(trim($codPerfil) != ''){
		$query = "
				select u.cod_perfil, u.des_perfil, m.cod_menu, m.desc_menu, m.link 
				from admin.perfil_usuario_interno u, admin.perfil_usuario_interno_menu p, admin.perfil_gestion_menu m
				where u.cod_perfil = $codPerfil  
				and u.cod_perfil = p.cod_perfil 
				and p.cod_menu = m.cod_menu 
				order by cod_menu
				";
		$result = consultarSQL($query, $ubicacion);
		$opciones = '';
		while ( $fila = oci_fetch_array( $result, OCI_RETURN_NULLS)){
			// Se ignora Petrobras y Eficash
			if (in_array($fila['LINK'], $links_a_ignorar)) {
				echo "-> Ignorando item LINK {$fila['LINK']}.\n";
				continue; 
			}
			$opciones .= '<li><a href="javascript:loadContent(\''.$fila['LINK'].'\');">'.$fila['DESC_MENU'].'</a></li>';			
		}	
	}
	$menu = '<ul>';	
	$menu .= $opciones;
	$menu .= '<li><a href="javascript:loadContent(\'acerca.php\');">ACERCA DE</a></li>';	
	$menu .='</ul>';
	return $menu;	
}

function generarEmpresaInfo(){
// Genera la plantilla con la info de la empresa, por ahora es fijo.
		return '
		<h1><b></b></h1>
		<table width="300" border="0" align="left" style="padding:0; margin:0;">
			<tr>
				<td>&nbsp;</td>
			</tr>
		</table>';
}

function generarUsuarioInfo($nom_usuario){
// Genera la plantilla con la info del usuario.
	return '
		<h1><b>Bienvenid@'.$nom_usuario.'<a href="javascript:logout();"></b></h1>
		
		<table width="200" border="0" align="right" style="padding:0; margin:0;">
			<tr>
				<td>
					<a href="javascript:logout();">Cerrar Sesion</img></a>
				</td>
			</tr>
		</table>';
}

function limpiarCadena($cadena) {
// Limpia una cadena de posibles inyecciones SQL y XSS.
	
	$cadena = strip_tags($cadena);	
	$cadena = str_replace ("*", "", $cadena);
	$cadena = str_replace ("%", "", $cadena);
	$cadena = str_replace ("'", "", $cadena);
	$cadena = str_replace ("#", "", $cadena);
	$cadena = str_replace ("\\", "", $cadena);
	$cadena = str_replace("mysql","",$cadena);
	$cadena = str_replace("mssql","",$cadena);
	$cadena = str_replace("query","",$cadena);
	$cadena = str_replace("insert","",$cadena);
	$cadena = str_replace("into","",$cadena);
	$cadena = str_replace("update","",$cadena);
	$cadena = str_replace("delete","",$cadena);
	$cadena = str_replace("select","",$cadena);
	$cadena = str_replace("Character","",$cadena);
	$cadena = str_replace("MEMB_INFO","",$cadena);
 	$cadena = str_replace("IN","",$cadena);
 	$cadena = str_replace("OR","",$cadena);
 	$cadena = str_replace (";", "", $cadena);
	return $cadena;
	//$cadena = str_replace (",", "", $cadena);
	/*
	$cadena = trim($cadena);
	$cadena = strip_tags($cadena);
	if(strlen($cadena)>100){
		$cadena = substr($cadena,0,99);
	}
	$cadena = mysql_escape_string($cadena);
	return $cadena;
	*/
}

function generarAuditoria($log, $idXLog, $codUsuario, $exito){
	global $ora_conn;

	// Valores fijos.
	$est = '2';
	$msg = 'No se pudo realizar el insert.';
	$adic = '***';
	
	//Valores variables.
	$HttpXForwardedFor = getRealIP();
	$remoteAddr = $_SERVER['REMOTE_ADDR'];



	$query  = "INSERT INTO admin.auditoria_web";
	$query .= "(ID_AUDITORIA ,";
	$query .= "FECHA_HORA ,";
	$query .= "REMOTE_ADDR ,";
	$query .= "HTTP_X_FORWARDED_FOR ,";
	$query .= "COD_USUARIO ,";
	$query .= "SESSION_ID ,";
	$query .= "COD_EMPRESA ,";
	$query .= "COD_ENTIDAD ,";
	$query .= " APLICACION,";
	$query .= " URL, EXITO, ID_APLICACION_WEB)";
	$query .= " values (";
	$query .= " ".$idXLog;
	$query .= ", TO_DATE('".date("d-m-Y H:i:s")."', 'DD-MM-YYYY HH24:MI:SS') ";
	$query .= " ,'".$remoteAddr."'";
	$query .= " ,'".$HttpXForwardedFor."'";
	$query .= " ,".$codUsuario;
	$query .= " ,'".session_id()."' ";
	$query .= " ,'".$_SESSION["codEmpresa"]."' ";
	$query .= " ,".$_SESSION["codEntidad"];
	$query .= " ,'".$app."'";
	$query .= " ,'".$url."'";
	$query .= " ,'".$exito."'";
	$query .= " ,".$idAplicacionWeb.")";
	
	$log->write($idXLog, 0, 'gestorLogin.php/generarAuditoria', 'Query: '.$query);

	
	$id_sentencia = @oci_parse( $ora_conn, $query );
	
	if (!$id_sentencia){
		$e = oci_error($ora_conn);
		$msg = $e['message'];
		$log->write($idXLog, 3, 'gestorLogin.php/generarAuditoria', 'ERROR: '.$msg);
		$est = 0;
		$respuesta = $id.'|'.$est.'|'.$msg.'|'.$adic;
		return $respuesta;
	}
	
	$r = @oci_execute( $id_sentencia, OCI_DEFAULT );
	
	if ( !$r ){
		$e = oci_error($id_sentencia);
		$msg = $e['message'];
		$log->write($idXLog, 3, 'gestorLogin.php/generarAuditoria', 'ERROR: '.$msg);
		$est = 0;
		$respuesta = $id.'|'.$est.'|'.$msg.'|'.$adic;
		return $respuesta;
	}
		
	$committed = oci_commit($ora_conn);
	
	if ($committed) {
		$est = '1';
		$msg = 'OK';
		
	}
	else{
		$e = oci_error($id_sentencia);
		$msg = $e['message'];
		$log->write($idXLog, 3, 'gestorLogin.php/generarAuditoria', 'ERROR: '.$msg);
		$est = 0;
		$respuesta = $id.'|'.$est.'|'.$msg.'|'.$adic;
		return $respuesta;
	}
	oci_close($ora_conn);
	$adic = 'INSERT INTO auditoria->OK!';
	$respuesta = $id.'|'.$est.'|'.$msg.'|'.$adic;
	$log->write($idXLog, 0, 'gestorLogin.php/generarAuditoria', 'respuesta: '.$respuesta);
	return $respuesta;
}

function getRealIP()
{
   if( $_SERVER['HTTP_X_FORWARDED_FOR'] != '' )
   {
      $client_ip =
         ( !empty($_SERVER['REMOTE_ADDR']) ) ?
            $_SERVER['REMOTE_ADDR']
            :
            ( ( !empty($_ENV['REMOTE_ADDR']) ) ?
               $_ENV['REMOTE_ADDR']
               :
               "unknown" );
   
      // los proxys van añadiendo al final de esta cabecera
      // las direcciones ip que van "ocultando". Para localizar la ip real
      // del usuario se comienza a mirar por el principio hasta encontrar
      // una dirección ip que no sea del rango privado. En caso de no
      // encontrarse ninguna se toma como valor el REMOTE_ADDR
   
      $entries = explode('[, ]', $_SERVER['HTTP_X_FORWARDED_FOR']);
   
      reset($entries);
      while (list(, $entry) = each($entries))
      {
         $entry = trim($entry);
         if ( preg_match("/^([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)/", $entry, $ip_list) )
         {
            // http://www.faqs.org/rfcs/rfc1918.html
            $private_ip = array(
                  '/^0\./',
                  '/^127\.0\.0\.1/',
                  '/^192\.168\..*/',
                  '/^172\.((1[6-9])|(2[0-9])|(3[0-1]))\..*/',
                  '/^10\..*/');
   
            $found_ip = preg_replace($private_ip, $client_ip, $ip_list[1]);
   
            if ($client_ip != $found_ip)
            {
               $client_ip = $found_ip;
               break;
            }
         }
      }
   }
   else
   {
      $client_ip =
         ( !empty($_SERVER['REMOTE_ADDR']) ) ?
            $_SERVER['REMOTE_ADDR']
            :
            ( ( !empty($_ENV['REMOTE_ADDR']) ) ?
               $_ENV['REMOTE_ADDR']
               :
               "unknown" );
   }
   
   return $client_ip;
}
function getUsuario($log, $idXLog, $id, $user_login, $pass_login){
    $user_login=  trim($user_login);
	$ubicacion = getcwd()."/".__FUNCTION__;
	$est = '2';
	$msg = 'No se obtuvieron resultados para su consulta';
	$adic = '***';
	$inicioF4 = microtime(true);
	$query  = "select * from admin.usuario where usuario = '$user_login' and clave_acceso = '".md5($pass_login)."' and ACTIVO_SN='S' and BLOQUEADO='N' FETCH FIRST 1 ROW ONLY";
	$log->write($idXLog, 0, $ubicacion, 'Query: '.$query);
	$id_sentencia = consultarSQL($query, $ubicacion);;
	while ( $fila = oci_fetch_array( $id_sentencia, OCI_RETURN_NULLS ) ) {
		$adic = $fila['COD_USUARIO'].";".$fila['USUARIO'].";".$fila['COD_EMPRESA'].";".$fila['COD_PERFIL'].";".$fila['EMAIL'].";".$fila['PERFIL_GESTION_INTERNA'].";".$fila['COD_PERSONA'];
			$est = '1';
			$msg = 'OK';
	}

	$respuesta = $id.'|'.$est.'|'.$msg.'|'.$adic;
	$finF4 = microtime(true);
	$totalF4 = round($finF4 - $inicioF4, 4);
	$log->write($idXLog, 0, $ubicacion, 'respuesta: '.$respuesta.' y se demoro '.$totalF4.' segundos');
	return $respuesta;
}

?>