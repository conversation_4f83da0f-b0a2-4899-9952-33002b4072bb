<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><!-- InstanceBegin template="/Templates/DocMain.dwt" codeOutsideHTMLIsLocked="false" -->
<head>
<!-- InstanceBeginEditable name="doctitle" -->
<title>FusionCharts</title>
<!-- InstanceEndEditable --> 
<link rel='stylesheet' href="Style.CSS">
<!-- InstanceBeginEditable name="head" --><!-- InstanceEndEditable -->
</head>

<body leftmargin="0" topmargin="0">
<!-- InstanceBeginEditable name="MainTable" -->
<table width="98%" border="0" align="right" cellpadding="2" cellspacing="1">
  <tr> 
    <td><span class="header">Gantt Chart &gt; Anatomy</span></td>
  </tr>
  <tr> 
    <td>&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="text"> <p>FusionCharts Gantt chart consists of the 
        following elements:</p>
      <ul>
        <li>Categories and sub-categories (Dates)</li>
        <li> Process names</li>
        <li> Data Table for showing additional information</li>
        <li> Gantt Task Bars</li>
        <li> Milestones</li>
        <li> Task Connectors</li>
        <li> Trend Lines </li>
        <li>Hover Caption</li>
      </ul>
      <p>Here we&#8217;ll see each of them in detail. Let&#8217;s first have a 
        look at a standard Gantt chart enlisting a few of the above elements: 
      </p>
      <p>&nbsp; </p></td>
  </tr>
  <tr> 
    <td class="text"><img src="Images/Gantt_Anatomy.gif" width="509" height="388"></td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Categories and sub-categories (Dates)</td>
  </tr>
  <tr> 
    <td class="text"><p>A Gantt chart is constructed with a horizontal axis representing 
        the total time span of the project, broken down into increments (for example, 
        days, weeks, or months). FusionCharts Gantt chart allows you to define 
        any number of sub-categories to show dates broken into smaller units.</p>
      <p>For example, if you were to show a Gantt Chart spanning 2 years, you 
        can the first sub-category as quarters, then divide these quarters into 
        months, then weeks, and finally into days. This is all possible with FusionCharts 
        Gantt Chart.</p></td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Process names</td>
  </tr>
  <tr> 
    <td class="text">The vertical data table representing the tasks that make 
      up the project (for example, if the project is outfitting your computer 
      with new software, the major tasks involved might be: conduct research, 
      choose software, install software) is called process names.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Data Table for showing additional information</td>
  </tr>
  <tr> 
    <td class="text">If you need to provide more data pertinent to each process, 
      you can show that data in an interactive and fully customizable data table.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header"> Gantt Task Bars</td>
  </tr>
  <tr> 
    <td class="text">Task Bars are horizontal bars of varying lengths that represent 
      the sequences, timing, and time span for each task.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Milestones</td>
  </tr>
  <tr>
    <td class="text">Milestones are important checkpoints or interim goals for 
      a project. You can represent them using stars, diamonds etc on FusionCharts 
      Gantt Chart.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Trend Lines</td>
  </tr>
  <tr> 
    <td class="text">Trend Lines can be used to show important dates on the chart 
      - like today or scheduled date etc.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td class="header">Hover Caption</td>
  </tr>
  <tr> 
    <td class="text">FusionCharts Gantt chart supports hover caption (tool tip) 
      for all the Gantt task bars. You can define your custom hover text to show 
      additional information as tool tip.</td>
  </tr>
  <tr> 
    <td class="text">&nbsp;</td>
  </tr>
</table>
<!-- InstanceEndEditable -->
</body>
<!-- InstanceEnd --></html>
