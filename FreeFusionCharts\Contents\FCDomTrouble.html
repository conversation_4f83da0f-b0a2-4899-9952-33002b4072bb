<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><!-- InstanceBegin template="/Templates/Documentation Page.dwt" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- InstanceBeginEditable name="doctitle" -->
<title>FudionCharts DOM Documentation - Troubleshooting</title>
<!-- InstanceEndEditable -->

<script type="text/javascript" src="js/lib.js"></script>

<link rel="stylesheet" type="text/css" href="assets/prettify/prettify.css">
<script type="text/javascript" src="assets/prettify/prettify.js"></script>

<link rel="stylesheet" type="text/css" href="css/typoset.css">

<!-- InstanceBeginEditable name="head" --><!-- InstanceEndEditable -->

</head>

<body>

<div id="wrapper">

  <noscript class="hl-red">&nbsp;<br />
  	For maximum compatibility, it is requested that you browse this page in a JavaScript enabled browser.</noscript>
  
  <div id="topshadow"></div>
  <div id="viewport">
  <h2><!-- InstanceBeginEditable name="pagetitle" --> FusionCharts DOM Troubleshooting<!-- InstanceEndEditable --></h2>

  <div id="contents"><!-- InstanceBeginEditable name="pagebody" -->
    <p>When you try to build a chart using FusionCharts DOM,           you may get errors in the chart itself (or  the chart may not render) despite  your FusionCharts DOM works correctly. There could be several reasons behind it. Here we'll discuss about these. </p>
    <h3>SWF Movie not Loading or No chart shown </h3>
    <p>The chart will not render if the SWF movie doesn't get loaded or takes unlimited time to load. Then you have to check whether the SWF path is  provided in your HTML page is correct. To know more about this please see the &quot;Basic Troubleshooting&quot; page under &quot;Debugging Your Chart&quot; section in FusionCharts v3 Documentation (www.fusioncharts.com/docs). </p>
    <h3>&quot;Error in Loading Data&quot; message</h3>
    <p>If you get             a message showing &quot;Error in Loading Data&quot; in your chart, it suggests that FusionCharts fails to find XML data           at the specified URL. In that case  you have to check that whether you actually provided dataURL             or dataXML. If you do not provide either,             FusionCharts will search for a default Data.xml file             in the same path. However, if that is also unavailable, it will throw the error above mentioned error message. For more information on that please see the &quot;Basic Troubleshooting&quot; page under &quot;Debugging Your Chart&quot; section in FusionCharts v3 Documentation (www.fusioncharts.com/docs). </p>
    <h3>&quot;Invalid XML Data&quot; message</h3>
    <p>If your chart shows a message like &quot;Invalid XML Data&quot;, it means that the XML data document is malformed. The message comes due to the difference in case of tags. <span class="code-inline">&lt;chart&gt;</span>             should end with <span class="code-inline">&lt;/chart&gt;</span> and <strong>not</strong> <span class="code-inline">&lt;/Chart&gt;</span> or             <span class="code-inline">&lt;/CHART&gt;</span>. For more information: visit www.fusioncharts.com/docs. </p>
    <h3>&quot;No data to display&quot; message</h3>
    <p>If your XML data doesn't contain any data that could be plotted by FusionCharts, you would get a            message like &quot;No data to display&quot;.             In this case, your XML just contains the <span class="code-inline">&lt;chart&gt;</span>             or <span class="code-inline">&lt;dataset&gt;</span> tags without any             data between them. For more information: visit <a href="www.fusioncharts.com/docs">www.fusioncharts.com/docs</a>. </p>
    <p>&nbsp;</p>
  <!-- InstanceEndEditable --></div>
  
  <div id="footer"><!-- InstanceBeginEditable name="pageFooter" --><!-- InstanceEndEditable --></div>
  </div>
  
</div>

</body>
<!-- InstanceEnd --></html>
