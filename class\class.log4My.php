<?php
/***************************************************/
/* Name: Gestor de log's.					     */ 
/* Version: 1.0         						     */
/* Autor: <PERSON>.  					     */
/* Fecha: Enero-2009.    					     */
/* Funcionalidades:   						     */
/* Backup de varios niveles (debug, notice, warning, etc)     */
/* Envio de notificaciones para:                                              */
/*     - Los niveles de escritura, se configura con $mailXlog*/
/*     - Backup's realizados					     */
/* Control del tama�o del archivo log 			     */
/* Generador de ID �nico para cada proceso de escritura   */
/**************************************************/
class log4My{

	var $type;//Definici�n de variables.
	var $fromName;
	
 	function __construct(){
		include($_SERVER['DOCUMENT_ROOT']."/config/log4MyGestionInterna.inc.php"); //Variables
		$this->path=$path;
		$this->filename=$filename;
		$this->maxFileSize=$maxFileSize*1048576; // 1 megabyte(MB) = 1,048,576 bytes 
		$this->to=$to;
		$this->subject=$subject; 
		$this->from=$from;
		$this->enviarNotificacion=$enviarNotificacion;
		$this->mailXlog=$mailXlog;
		$this->app=$app;
	}
	
	function write($id, $nivel, $locate, $msg){
		$enviarMail = '0'; //Por defecto.
		switch($nivel){
			case 0:
				$nivel = "�DEBUG!";
				$enviarMail = substr($this->mailXlog, 0, 1);
				break;
			case 1:
				$nivel = "�NOTICE!";
				$enviarMail = substr($this->mailXlog, 1, 1);
				break;
			case 2:
				$nivel = "�WARNING!";
				$enviarMail = substr($this->mailXlog, 2, 1);
				break;
			case 3:
				$nivel = "�FATAL!";
				$enviarMail = substr($this->mailXlog, 3, 1);
				break;
			default:
				$nivel = "�ERROR!";
				$enviarMail = substr($this->mailXlog, 4, 1);
		}
		$usuario = 'none';
		if(isset($_SESSION['dbOraUsuario'])){
			$usuario = $_SESSION['dbOraUsuario'];
		}
		
		$this->backup(); //Se realizarn los controles correspondientes al backup
		$datos="[".$id."]".date("H:i:s")."|dbOraUsuario:" . $usuario . "|".$nivel."-->".$locate.": ".$msg."\r\n";
		$this->fileProcess(0);
		$this->fileProcess(1, $datos);
		$this->fileProcess(2);		
		if($enviarMail == '1') $this->SendMail($datos);
	}

	function getId4Log(){
	//Se obtiene el ID para identificar al proceso en el log-->dia+mes+a�o+hora+min+seg+miliseg(exactitud 4 puntos decimales.)
	   list($MiliSegundos, $Segundos) = explode(" ", microtime());
	   $id=substr(strval($MiliSegundos),2,4);
	   return strval(date("d").date("m").date("y").date("H").date("i").date("s").$id);
	}
	
	function FileSize(){//Obtiene el tama�o del archivo
		$tam = filesize($this->path.'/'.$this->filename);
		return $tam;
	}
	
	function backup(){
		$name = $this->filename;
		$path = $this->path;

		if (file_exists($path.'/'.$name)){
			if ($this->FileSize() > $this->maxFileSize){
			//Si el archivo pesa mas de "$this->maxFileSize" MB se copia en el historico (renombrado) y se borra el original.
				$this->newName=date("dmY-His").'_'.$name;
				copy($path.'/'.$name,$path.'/history/'.$this->newName);
				unlink($path.'/'.$name);
				$datos=date("d/m/Y H:i:s")." Backup realizado para ".$this->newName.".\r\n";
				$this->fileProcess(0);
				$this->fileProcess(1, $datos);
				$this->fileProcess(2);
				if ($this->enviarNotificacion) $this->SendMail('B');
			}
		}
	}
	
	function fileProcess($action, $datos = ''){
	// $action->0 = Abrir.	1 = Escribir.	2 = Cerrar
		switch($action){
			case 0: //Se abre el archivo.
				$this->type=fopen($this->path.'/'.$this->filename,"a+");
				break;
			case 1: //Se escribe en el.
				fwrite($this->type,$datos);
				break;
			default: //Se cierra.
				fclose($this->type);
		}
	}
	
	function sendMail($datos){//Env�o de mail con el datalle del proceso.
		if($datos == 'B'){
			$contenido  = '<html>';
			$contenido .= 	'<body>';
			$contenido .= 		'<h2>'.$this->subject.'</h2>';
			$contenido .= 		'<b>Llegamos al l&iacute;mite del tama�o del log, fue almacenado en el hist&oacute;rico</b>';
			$contenido .= 		'<br><p><u>Detalle</u></p>';
			$contenido .= 		'<p>Archivo Original: '.$this->filename.'</p>';
			$contenido .= 		'<p>Destino: history/'.$this->newName.'</p>';
			$contenido .= 		'<p>App: '.$this->app.'</h2>';
			$contenido .= 	'</body>';
			$contenido .= '</html>';
			if (mail ($this->to, $this->subject, $contenido, "From: ".$this->from."\nReply-To:".$this->from."\nContent-Type: text/html; charset=iso-8859-1\nContent-Transfer-Encoding: 8bit"))//Se registra en el log si se pudo enviar el mail.
			{
				$datos=date("d/m/Y H:i:s")." Notificaci&oacute;n enviada para: ".$this->newName." .\r\n";
				$this->fileProcess(0);
				$this->fileProcess(1, $datos);
				$this->fileProcess(2);
			}
			else{
				$datos=date("d/m/Y H:i:s")." La notificaci&oacute;n no se pudo enviar para: ".$this->newName." .\r\n";
				$this->fileProcess(0);
				$this->fileProcess(1, $datos);
				$this->fileProcess(2);
			}
		}
		else{
			$contenido  = '<html>';
			$contenido .= 	'<body>';
			$contenido .= 		'<h2>'.$this->subject.'</h2>';
			$contenido .= 		'<b>Mensaje enviado por class.log4My.php</b>';
			$contenido .= 		'<br><p><u>Detalle</u></p>';
			$contenido .= 		'<p>'.$datos.'</p>';
			$contenido .= 		'<p>App: '.$this->app.'</h2>';
			$contenido .= 	'</body>';
			$contenido .= '</html>';
			if (mail ($this->to, $this->subject, $contenido, "From: ".$this->from."\nReply-To:".$this->from."\nContent-Type: text/html; charset=iso-8859-1\nContent-Transfer-Encoding: 8bit"))//Se registra en el log si se pudo enviar el mail.
			{
				$datos=date("d/m/Y H:i:s")." Notificaci&oacute;n enviada .\r\n";
				$this->fileProcess(0);
				$this->fileProcess(1, $datos);
				$this->fileProcess(2);
			}
			else{
				$datos=date("d/m/Y H:i:s")." La notificaci&oacute;n no se pudo enviar.\r\n";
				$this->fileProcess(0);
				$this->fileProcess(1, $datos);
				$this->fileProcess(2);
			}
		}
	}
}
?>
