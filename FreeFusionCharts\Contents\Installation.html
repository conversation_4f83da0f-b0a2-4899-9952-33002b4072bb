<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Installing FusionCharts Free </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>Installation of FusionCharts Free merely involves 
        copying and pasting the SWF files from the package into any of your folders. 
      </p></td>
  </tr>
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="header">Installing FusionCharts Free for your web application</td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>Installation of FusionCharts Free for your web application is just a two-step affair: </p>
      <ol>
        <li>Create a folder named <span class="codeInline">FusionCharts</span> 
          in the root of your application (though, it's not mandatory to name 
          the folder as <span class="codeInline">FusionCharts </span>or create 
          it under root folder. However, it organizes things a lot 
          more, as all the pages within your website can now access the common 
          set of charts).</li>
        <li>Copy all the SWF files from <span class="codeInline">Download Package 
        &gt; Charts</span> Folder and paste it in this folder.</li>
      </ol>
      <p>Installation Complete - Yes, there's no more step involved - you're 
          now ready to use FusionCharts in your web application, which will soon 
      see.</p></td>
  </tr>
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="header">Installing FusionCharts on your local machine 
      for general charting</td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>If you want to create charts on your local 
        machine, follow the steps below:</p>
      <ol>
        <li>Go to the folder where you want to save the chart.</li>
        <li>Create a folder named <span class="codeInline">FusionCharts</span> 
          inside this. Again note that, it's not mandatory to name the folder 
          as <span class="codeInline">FusionCharts</span> or create it as a new 
          folder. However, doing this way organizes things a lot more.</li>
        <li>Copy all the SWF files from <span class="codeInline">Download Package 
          &gt; Charts</span> Folder and now paste it in this folder.</li>
      </ol>
      <p>That completes the installation for FusionCharts Free. Now, all you need 
      to do is build your XML data and HTML page, which we'll see next.</p></td>
  </tr>
</table>
</body>
</html>
