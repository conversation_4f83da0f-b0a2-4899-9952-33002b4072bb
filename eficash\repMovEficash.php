  <?php 
  

?>
<script  type="text/javascript">
cargarMoneda();
function cargarMoneda(){

$("#resultado").html('');
    $.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: "id=28",
					success: function(msg){
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[0]=='0'){
							$("#cmbMoneda").html('<option>Error #28 </option>');
							$("#status").fadeOut("Slow");
						}
						else if (resultado[1]=='0'){
							$("#status").fadeOut("Slow");
							$("#cmbMoneda").html('<option>Error #28 </option>');
						}
						else{
							$("#status").fadeOut("Slow");
                                                        $("#cmbMoneda").html(resultado[1]);
						}
					}
				});

}
cargarCmbRec();
function cargarCmbRec(){
 
    $.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: "id=30",
					success: function(msg){  
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[0] == 1){
							$("#entidad").html(resultado[1]);
                            $('#entidad').append('<option value="*" selected="selected">Todos</option>');
							$("#status").fadeOut("Fast");
						   // $("table").tablesorter();
						}else{
							$("#status").fadeOut("Fast");
							$("#main").html('Error"#030 '+resultado[1]+'",consulte con el administrador facilitandole este dato.');
						}
					}
				});



} 
	$("#btnGenerarReporte").click(function (){ 
		var fecha = $("#fecha_inicio").val(); 			
		var tipoReporte=$("#cmbTipoReporte").val();
                var entidad=$("#entidad").val();
                var moneda=$("#cmbMoneda").val();
                var tipoFecha=$("#cmbTipoFecha").val();
		if(fecha == ''){
			alert('Debe ingresar una Fecha Valida');
			return false;
		}
		/*if(fecha_fin == ''){
			alert('debe ingresar fecha fin');
			return false;
		}*/
		
		/*
		var rec = $("#recaudadora").val();	 
	        var indice = document.getElementById('entidad').selectedIndex;
		var des_rec =  document.getElementById('entidad').options[indice].text; 
		*/
  var condicion ="id=31&fecha="+fecha+"&tipoFecha="+tipoFecha+"&entidad="+entidad+"&moneda="+moneda+"&tipoReporte="+tipoReporte;
			if(confirm('Esta seguro que desea realizar la consulta, puede demorarse unos segundos?')){
                            $("#resultado").html('');
			//	$("#filtros").hide();																										
				//$('#btnGenerarReporte').attr('disabled','-1');
				$("#status").fadeIn("Slow");
				$.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: condicion,
					success: function(msg){
                                          ///  alert (msg);
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[1] == 1){
							$("#resultado").html(resultado[3]);
							$("#status").fadeOut("Fast");
						    $("table").tablesorter(); 	
                                                    //$('#btnGenerarReporte').attr('disabled','1');
						}else{
							$("#status").fadeOut("Fast");
                                                          //  $('#btnGenerarReporte').attr('disabled','1');
							$("#resultado").html('Error"#031 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
						}
					}
				});
			}
	});

</script>

<div id="filtros" align="center">
<ul><li><b>Movimientos Recaudadoras Resumido.</b></li></ul>
    <br>
    <div id="divFechaInicial">
		
		<script type="text/javascript">
			/*
			$(function()
            {
				$('.date').datePicker({startDate: '01/01/2005',endDate: (new Date()).asString()
                    }
                );
            });
			*/
			$("#fecha_inicio").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});						
			$("#fecha_fin").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});						
			
        </script>
        <table border="0">
         <tr>
            <td>Tipo Reporte: </td>
            <td><select id="cmbTipoReporte">
                     <option value="R" >Resumido</option>
                    <option value="D" >Detallado</option>
                   
                </select></td>
        </tr>    
        <tr>
            <td>Tipo Fecha: </td>
            <td><select id="cmbTipoFecha">
                    
                    <option value="P" >Fecha Proceso</option>
                    <option value="M" >Fecha Ingreso</option>
                </select></td>
        </tr>
        <tr>
            <td>Fecha: </td>
            <td><input name="fecha_inicio" id="fecha_inicio" class="date" /></td>        
        </tr> 
        <tr>
        <td>Entidad: </td>
        <td><select id="entidad" name="entidad"></select></td>
        </tr>
        <tr>
        <td>Moneda: </td>
        <td><select id="cmbMoneda" name="cmbMoneda"></select></td>
        </tr>
        
	 <tr>
        <td>&nbsp;</td>
        <td><input type="button" value="Generar Reporte" id="btnGenerarReporte"></td>
        </tr>               
        </table>
        <input type="hidden" name="des_rec" id="des_rec" value="">
    </div>
</div>
<div id="resultado" style="padding-left: 10%;padding-right: 10%   "></div>
