FusionCharts Free v2
http://www.fusioncharts.com/free

Copyright (c) 2009 InfoSoft Global (P) Ltd.
Dual licensed under the MIT (X11) and GNU GPL licenses.
http://www.fusioncharts.com/free/license

MIT License: http://www.opensource.org/licenses/mit-license.php
GPL License: http://www.gnu.org/copyleft/gpl.html

========================**====**====**=============================

Structure of source code
---------------------------
In FusionCharts Free, each chart type is a .fla file that accepts
an XML data in the required format. To look for .fla file for a chart,
search for the folder resembling the name of chart.

How to compile the charts?
---------------------------
You'll need Flash MX IDE or Flash 8 IDE to compile the charts. The 
charts are to be compiled as ActionScript 1 (Flash 6) SWF files. This
is the default setting in Flash MX IDE. In Flash 8, you'll need to 
configure the same under File > Publish Settings > Flash.

Can I get support on this source code?
---------------------------------------
At this point in time, we do not provide personalized support for 
FusionCharts Free. However, you may visit our forums at 
www.fusioncharts.com/forum to get support from our vibrant community.

Contents of Codebase folder
---------------------------
The Codebase folder contains the central objects and helper objects
for chart. Here, 2 file naming conventions have been followed. For charts
that belonged to FusionCharts v2.3, the file name looks like FC____.as.
For charts that belonged to FusionCharts Instrumentation suite (like 
Gantt, Funnel etc.), the file name looks like FI____.as.

Can I modify the source code for my purpose?
--------------------------------------------
Yes - you may modify and then use/distribute the same, as per the terms
of MIT/GPL license. Please make sure to retain our copyright information.

How can I contribute to the development of FusionCharts Free, its API
and other products from FusionCharts stable?
---------------------------------------------------------------------------
Please contact <NAME_EMAIL> and we'll be glad to assist you
in doing the same.

How to get source code of FusionCharts v3 and FusionWidgets v3?
----------------------------------------------------------------
FusionCharts v3 and FusionWidgets v3 (previously FusionCharts Instrumentation
Suite) are commercially open-source. That is, you get access to full source
code of these products upon purchasing the apt license. Please visit
www.fusioncharts.com/buy for details.
