<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Using FusionCharts with PHP &gt; Charting Data from an Array </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>In this section, we'll show you how to use FusionCharts and PHP to plot charts from data contained in PHP arrays. We'll cover the following examples here:</p>
      <ul>
        <li>Creating a single series chart from data contained in arrays</li>
        <li>Creating a multi-series chart from data contained in arrays </li>
      </ul>
      <p><strong>Before you go further with this page, we recommend you to please see the previous section &quot;Basic Examples&quot; as we start off from concepts explained in that page. </strong></p>
      <p class="highlightBlock">The code examples contained in this page are present in <span class="codeInline">Download Package &gt; Code &gt; PHP &gt; ArrayExample</span> folder. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Creating a single series chart from data contained in arrays</td>
  </tr>
  <tr>
    <td valign="top" class="text">The code to create a single series chart is contained in <span class="codeInline">SingleSeries.php</span> and can be listed as under: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock">
      <p>&lt;?php<br />
      <span class="codeComment">
      //We've included ../Includes/FusionCharts.php, which contains functions<br />
      //to help us easily embed the charts.</span><br />
      include(&quot;../Includes/FusionCharts.php&quot;);<br />
	   <span class="codeComment">//a file having a list of colors to be applied to each column (using getFCColor() function</span>)<br />
 	include(&quot;../Includes/FC_Colors.php&quot;);<br />   ?&gt;<br /><BR />
      &lt;HTML&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;	FusionCharts Free - Array Example using Single Series Column 3D Chart&lt;/TITLE&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;/HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;BODY&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;?php<br />
      &nbsp;&nbsp;<span class="codeComment">&nbsp;//In this example, we plot a single series chart from data contained<br />
&nbsp;&nbsp;&nbsp;//in an array. The array will have two columns - first one for data label<br />
&nbsp;&nbsp;&nbsp;//and the next one for data values.<br />
&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;//Let's store the sales data for 6 products in our array). We also store<br />
&nbsp;&nbsp;&nbsp;//the name of products. </span><br />
      <span class="codeComment">&nbsp;&nbsp;&nbsp;//Store Name of Products</span><br />
      &nbsp;&nbsp;&nbsp;$arrData[0][1] = &quot;Product A&quot;;<br />
      &nbsp;&nbsp;&nbsp;$arrData[1][1] = &quot;Product B&quot;;<br />
      &nbsp;&nbsp;&nbsp;$arrData[2][1] = &quot;Product C&quot;;<br />
      &nbsp;&nbsp;&nbsp;$arrData[3][1] = &quot;Product D&quot;;<br />
      &nbsp;&nbsp;&nbsp;$arrData[4][1] = &quot;Product E&quot;;<br />
      &nbsp;&nbsp;&nbsp;$arrData[5][1] = &quot;Product F&quot;;<br />
      <br />
      &nbsp;<span class="codeComment">&nbsp;&nbsp;//Store sales data</span><br />
      &nbsp;&nbsp;&nbsp;$arrData[0][2] = 567500;<br />
      &nbsp;&nbsp;&nbsp;$arrData[1][2] = 815300;<br />
      &nbsp;&nbsp;&nbsp;$arrData[2][2] = 556800;<br />
      &nbsp;&nbsp;&nbsp;$arrData[3][2] = 734500;<br />
      &nbsp;&nbsp;&nbsp;$arrData[4][2] = 676800;<br />
      &nbsp;&nbsp;&nbsp;$arrData[5][2] = 648500;</p>
      <p> &nbsp;<span class="codeComment">&nbsp;&nbsp;//Now, we need to convert this data into XML. We convert using string concatenation.</span><br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Initialize &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;$strXML = &quot;&lt;graph caption='Sales by Product' numberPrefix='$' formatNumberScale='0' decimalPrecision='0' &gt;&quot;;<br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;//Convert data to XML and append</span><br />
        &nbsp;&nbsp;&nbsp;foreach ($arrData as $arSubData)<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strXML .= &quot;&lt;set name='&quot; . $arSubData[1] . &quot;' value='&quot; . $arSubData[2] . &quot;' colo='&quot; .getFCColor() . &quot;/&gt;&quot;;<br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Close &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;$strXML .= &quot;&lt;/graph&gt;&quot;;<br />
  <br />
        <span class="codeComment">&nbsp;&nbsp;&nbsp;//Create the chart - Column 3D Chart with data contained in strXML</span><br />
        &nbsp;&nbsp;&nbsp;echo renderChart(&quot;../../FusionCharts/FCF_Column3D.swf&quot;, &quot;&quot;, $strXML, &quot;productSales&quot;, 600, 300);<br />
        ?&gt;<br />
  &lt;/BODY&gt;<br />
  &lt;/HTML&gt;</p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above example, we first include <span class="codeInline">FusionCharts.js</span> file to enable us embed the chart using JavaScript. We also include <span class="codeInline">FusionCharts.php</span> to help us easily embed the charts.</p>
    <p>Thereafter, we define a PHP array <span class="codeInline">arrData</span> to store sales data for 6 different products. The array has two columns - first one for data label and the next one for data values. </p>
    <p>We define a variable <span class="codeInline">strXML</span> to store the entire XML data. To build the XML, we iterate through the array and using string concatenation. Finally, we render the chart using <span class="codeInline">renderChart()</span> function and pass <span class="codeInline">strXML</span> as <span class="codeInline">dataXML</span>. </p>
    We include FC_Colors.php, which contains the default color set for the FusionCharts. These colors in Hex code are optimized to give a snazzy look to the charts. The getFCColor() function in cyclic iteration returns these color values.
	<p>When you view the chart, you'll see a chart as under: </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_ArraySS.jpg"  /></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Creating a multi-series chart from data contained in arrays </td>
  </tr>
  <tr>
    <td valign="top" class="text">Let us now create a multi-series chart from data contained in arrays. We create a file <span class="codeInline">MultiSeries.php</span> with the following code: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock">
      <p>&lt;?php<br />
      <span class="codeComment">
      //We've included ../Includes/FusionCharts.php, which contains functions<br />
      //to help us easily embed the charts.</span><br />
      include(&quot;../Includes/FusionCharts.php&quot;);<br />
      ?&gt;<br />
      &lt;HTML&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;	FusionCharts Free - Array Example using Multi Series Column 3D Chart	&lt;/TITLE&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;/HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;BODY&gt;</p>
      <p>&nbsp;&nbsp;&nbsp;&lt;?php<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//In this example, we plot a multi series chart from data contained<br />
&nbsp;&nbsp;&nbsp;//in an array. The array will have three columns - first one for data label (product)<br />
&nbsp;&nbsp;&nbsp;//and the next two for data values. The first data value column would store sales information<br />
&nbsp;&nbsp;&nbsp;//for current year and the second one for previous year.<br />
  <br />
&nbsp;&nbsp;&nbsp;//Let's store the sales data for 6 products in our array. We also store<br />
&nbsp;&nbsp;&nbsp;//the name of products. </span><br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Store Name of Products</span><br />
        &nbsp;&nbsp;&nbsp;$arrData[0][1] = &quot;Product A&quot;;<br />
        &nbsp;&nbsp;&nbsp;$arrData[1][1] = &quot;Product B&quot;;<br />
        &nbsp;&nbsp;&nbsp;$arrData[2][1] = &quot;Product C&quot;;<br />
        &nbsp;&nbsp;&nbsp;$arrData[3][1] = &quot;Product D&quot;;<br />
        &nbsp;&nbsp;&nbsp;$arrData[4][1] = &quot;Product E&quot;;<br />
        &nbsp;&nbsp;&nbsp;$arrData[5][1] = &quot;Product F&quot;;<br />
        <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;//Store sales data for current year</span><br />
        &nbsp;&nbsp;&nbsp;$arrData[0][2] = 567500;<br />
        &nbsp;&nbsp;&nbsp;$arrData[1][2] = 815300;<br />
        &nbsp;&nbsp;&nbsp;$arrData[2][2] = 556800;<br />
        &nbsp;&nbsp;&nbsp;$arrData[3][2] = 734500;<br />
        &nbsp;&nbsp;&nbsp;$arrData[4][2] = 676800;<br />
        &nbsp;&nbsp;&nbsp;$arrData[5][2] = 648500;<br />
        <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Store sales data for previous year</span><br />
        &nbsp;&nbsp;&nbsp;$arrData[0][3] = 547300;<br />
        &nbsp;&nbsp;&nbsp;$arrData[1][3] = 584500;<br />
        &nbsp;&nbsp;&nbsp;$arrData[2][3] = 754000;<br />
        &nbsp;&nbsp;&nbsp;$arrData[3][3] = 456300;<br />
        &nbsp;&nbsp;&nbsp;$arrData[4][3] = 754500;<br />
        &nbsp;&nbsp;&nbsp;$arrData[5][3] = 437600;</p>
      <p> &nbsp;<span class="codeComment">&nbsp;&nbsp;//Now, we need to convert this data into multi-series XML. <br />
&nbsp;&nbsp;&nbsp;//We convert using string concatenation.<br />
&nbsp;&nbsp;&nbsp;// $strXML - Stores the entire XML<br />
&nbsp;&nbsp;&nbsp;// $strCategories - Stores XML for the &lt;categories&gt; and child &lt;category&gt; elements<br />
&nbsp;&nbsp;&nbsp;// $strDataCurr - Stores XML for current year's sales<br />
&nbsp;&nbsp;&nbsp;// $strDataPrev - Stores XML for previous year's sales</span><br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Initialize &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;$strXML = &quot;&lt;graph caption='Sales by Product' numberPrefix='$' decimalPrecision='0'  &gt;&quot;;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Initialize &lt;categories&gt; element - necessary to generate a multi-series chart</span><br />
        &nbsp;&nbsp;&nbsp;$strCategories = &quot;&lt;categories&gt;&quot;;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Initiate &lt;dataset&gt; elements</span><br />
        &nbsp;&nbsp;&nbsp;$strDataCurr = &quot;&lt;dataset seriesName='Current Year'  color='AFD8F8' &gt;&quot;;<br />
        &nbsp;&nbsp;&nbsp;$strDataPrev = &quot;&lt;dataset seriesName='Previous Year'  color='F6BD0F' &gt;&quot;;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;//Iterate through the data </span><br />
        &nbsp;&nbsp;&nbsp;foreach ($arrData as $arSubData) {<br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;//Append &lt;category name='...' /&gt; to strCategories</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strCategories .= &quot;&lt;category name='&quot; . $arSubData[1] . &quot;' /&gt;&quot;;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;//Add &lt;set value='...' /&gt; to both the datasets</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strDataCurr .= &quot;&lt;set value='&quot; . $arSubData[2] . &quot;' /&gt;&quot;;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strDataPrev .= &quot;&lt;set value='&quot; . $arSubData[3] . &quot;' /&gt;&quot;;<br />
        &nbsp;&nbsp;&nbsp;}<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Close &lt;categories&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;$strCategories .= &quot;&lt;/categories&gt;&quot;;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;//Close &lt;dataset&gt; elements</span><br />
        &nbsp;&nbsp;&nbsp;$strDataCurr .= &quot;&lt;/dataset&gt;&quot;;<br />
        &nbsp;&nbsp;&nbsp;$strDataPrev .= &quot;&lt;/dataset&gt;&quot;;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;//Assemble the entire XML now</span><br />
        &nbsp;&nbsp;&nbsp;$strXML .= $strCategories . $strDataCurr . $strDataPrev . &quot;&lt;/graph&gt;&quot;;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;//Create the chart - MS Column 3D Chart with data contained in strXML</span><br />
        &nbsp;&nbsp;&nbsp;echo renderChart(&quot;../../FusionCharts/MSColumn3D.swf&quot;, &quot;&quot;, $strXML, &quot;productSales&quot;, 600, 300);<br />
        ?&gt;<br />
  &lt;/HTML&gt;</p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above example, we first include <span class="codeInline">FusionCharts.js</span> file to enable us embed the chart using JavaScript. We also include <span class="codeInline">FusionCharts.php</span> to help us easily embed the charts.</p>
      <p>Thereafter, we define a PHP array <span class="codeInline">arrData</span> to store sales data for 6 different products. The array has three columns - first one for data label (product) and the next two for data values. The first data value column would store sales information<br />
      for current year and the second one for previous year.</p>
      <p>We define a variable <span class="codeInline">strXML</span> to store the entire XML data. We also define <span class="codeInline">strCategories</span>, <span class="codeInline">strDataCurr</span> and <span class="codeInline">strDataPrev</span> variables to store XML data for categories elements, current year's dataset and previous year's dataset respectively. To build the XML, we iterate through the array and using string concatenation. We concatenate the entire XML finally in <span class="codeInline">strXML</span>. </p>
      <p>Finally, we render the chart using <span class="codeInline">renderChart()</span> function and pass <span class="codeInline">strXML</span> as <span class="codeInline">dataXML</span>. </p>
    <p>When you view the chart, you'll see a chart as under: </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_ArrayMS.jpg" alt=""  /></td>
  </tr>
  <tr>
    <td valign="top" class="highlightBlock">In <span class="codeInline">Download Package &gt; Code &gt; PHP &gt; ArrayExample</span>, we've more example codes to create Stacked and Combination Charts too, which have not been explained here, as they're similar in concept. You can directly see the code if you want to. </td>
  </tr>
</table>
</body>
</html>
