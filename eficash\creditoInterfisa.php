<?php
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
?>
<script type="text/javascript">
    $(document).ready(function() {
        //monedaSelect('');
$("#fecha").datepicker({changeYear: true, changeMonth: true, firstDay:1, dateFormat: 'dd/mm/yy'});

    });
    //detectar inputs vacios

//fin



    function consultarRango() {
        $("#dvAgregar").hide(300);
        $("#dvResultado").show(300);
        var fecha=$('#fecha').val()
        if(fecha == ''){
            alert("Fecha no puede estar vacio");
            return false;
        }
         $("#status").fadeIn("Fast");
        var datos = "id=90&fecha="+fecha;
        $.ajax({
            type: "POST",
            url: "commonFunctions.php",
            data: datos,
            success: function(msg) {
                //alert(msg);
                var resultado = msg.split("|");
                //alert(resultado[1]);
                if (resultado[0] == 1) {
                    $("#dvResultado").html("<div align='center'>" + resultado[1] + "</div>");
                    $("#status").fadeOut("Fast");
                 } else if (resultado[0] == 0) {
                    $("#status").fadeOut("Fast");
                    $("#dvResultado").html('<div style="color: #FF0000; font-size: 17px; font-weight: bold; text-align: center;">' + resultado[1] + '</div>');
              
                } else{
                    $("#status").fadeOut("Fast");
                    $("#dvResultado").html('Error #090 "' + resultado[1] + '",consulte con el administrador facilitandole este datos.');

                }

            }
        });
    }
    function monedaSelect(sele) {

        var datos = "funcion=select2&cod=1";
///alert(datos)
        $.ajax({
            type: "POST",
            url: "seprelad/commonFunctions.php",
            data: datos,
            success: function(msg) {
                //alert(msg);
                $('#dvmoneda').html(msg);

            }
        });

    }

</script>
<center>
    <h2>Extracciones de Eficash, credito al recaudador Interfisa</h2>
    <table>
        <tr>
            <td> Fecha:</td>
            <td> <input type="text" name="fecha" id="fecha" > </td>
        </tr>
        <tr>
            <td> </td>
            <td>     <input type="button" value="Consultar" id="consultar" onclick="consultarRango()"/>
            </td>
        </tr>
    </table>

    <div id="dvAgregar" style="display: none;">
        <br>
        <form id="formRangos">
            <input type="hidden" value="0" name="operacion" id="operacion">
            <input type="hidden" value="0" name="codigo_operacion" id="codigo_operacion">
            <table>

                <tr>
                    <td>Ocupación</td>
                    <td><input type="text" name="ocupacion" id="ocupacion"/></td>
                </tr>


                <tr>
                    <td>Puntaje Riesgo</td>
                    <td><input type="text" name="puntoR" id="punto_riesgo"/></td>
                </tr>
                <tr>
                    <td></td>
                    <td><input type="submit" id="enviando" value="Guardar" onclick="guardarDatos()" /></td>
                </tr>
            </table>

        </form>

    </div>
    <div id="dvResultado"></div>

</center>