<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Using FusionCharts with ASP &gt; Plotting data from a database </h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>In this section, we'll show you how to use FusionCharts and ASP to plot charts from data contained in a database. We'll create a pie chart to show &quot;Production by Factory&quot; using: </p>
      <ul>
        <li><span class="codeInline">dataXML</span>  method first.</li>
        <li>Thereafter, we'll convert this chart to use <span class="codeInline">dataURL</span>  method. </li>
      </ul>
      <p>For the sake of ease, we'll use an Access Database. The database is present in <span class="codeInline">Download Package &gt; Code &gt; ASP &gt; DB </span>folder. You can, however, use any database with FusionCharts including MS SQL, Oracle, MySQL etc. </p>
      <p><strong>Before you go further with this page, we recommend you to please see the previous section &quot;Basic Examples&quot; as we start off from concepts explained in that page. </strong></p>
      <p class="highlightBlock">The code examples contained in this page are present in <span class="codeInline">Download Package &gt; Code &gt; ASP &gt; DBExample </span> folder. The Access database is present in <span class="codeInline">Download Package &gt; Code &gt; ASP &gt;</span> <span class="codeInline">DB</span>. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Database Structure </td>
  </tr>
  <tr>
    <td valign="top" class="text">Before we code the ASP pages to retrieve data, let's quickly have a look at the database structure. </td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_DB.gif" /></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>The database contains just 2 tables:</p>
      <ol>
        <li><span class="codeInline">Factory_Master</span>: To store the name and id of each factory</li>
        <li><span class="codeInline">Factory_Output</span>: To store the number of units produced by each factory for a given date.</li>
      </ol>
    <p>For demonstration, we've fed some dummy data in the database. Let's now shift our attention to the ASP page that will interact with the database, fetch data and then render a chart. </p></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Building the ASP Page for dataXML Method </td>
  </tr>
  <tr>
    <td valign="top" class="text">The ASP page for <span class="codeInline">dataXML</span> method example is named as <span class="codeInline">BasicDBExample.asp</span> (in <span class="codeInline">DBExample</span> folder). It contains the following code: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock"><p>&lt;%@ Language=VBScript %&gt;<br />
      &lt;HTML&gt;<br />
      &nbsp;&nbsp;&nbsp;&lt;HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;FusionCharts Free - Database Example&lt;/TITLE&gt;<br />
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;/HEAD&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;!-- #INCLUDE FILE=&quot;../Includes/FusionCharts.asp&quot; --&gt;<br />
  &nbsp;&nbsp;<span class="codeComment">&nbsp;'Adding library script that will  connect to the Access Databse file</span> automatically. <br />
  &nbsp;&nbsp;&nbsp;&lt;!-- #INCLUDE FILE=&quot;../Includes/DBConn.asp&quot; --&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;BODY&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;CENTER&gt;<br />
  &nbsp;&nbsp;&nbsp;&lt;%&nbsp;&nbsp;&nbsp;<br />
   &nbsp;&nbsp;<span class="codeComment">&nbsp;'In this example, we show how to connect FusionCharts to a database.<br />
&nbsp;&nbsp;&nbsp;'For the sake of ease, we've used an Access database which is present in<br />
&nbsp;&nbsp;&nbsp;'../DB/FactoryDB.mdb. It just contains two tables, which are linked to each<br />
&nbsp;&nbsp;&nbsp;'other. <br />
  <br />
&nbsp;&nbsp;&nbsp;'Database Objects - Initialization</span><br />
      &nbsp;&nbsp;&nbsp;Dim oRs, oRs2, strQuery<br />
      &nbsp;&nbsp;<span class="codeComment">&nbsp;'strXML will be used to store the entire XML document generated</span><br />
      &nbsp;&nbsp;&nbsp;Dim strXML<br />
      <br />
      &nbsp;<span class="codeComment">&nbsp;&nbsp;'Create the recordset to retrieve data</span><br />
      &nbsp;&nbsp;&nbsp;Set oRs = Server.CreateObject(&quot;ADODB.Recordset&quot;)</p>
      <p> &nbsp;&nbsp;<span class="codeComment">&nbsp;'Generate the chart element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = &quot;&lt;graph caption='Factory Output report' subCaption='By Quantity' decimalPrecision='0' showNames='1'  numberSuffix=' Units'  pieSliceDepth='30' formatNumberScale='0'&gt;&quot;<br />
  <br />
        &nbsp;<span class="codeComment">&nbsp;&nbsp;'Iterate through each factory</span><br />
        &nbsp;&nbsp;&nbsp;strQuery = &quot;select * from Factory_Master&quot;<br />
        &nbsp;&nbsp;&nbsp;Set oRs = oConn.Execute(strQuery)<br />
  <br />
        &nbsp;&nbsp;&nbsp;While Not oRs.Eof<br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;'Now create second recordset to get details for this factory</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = Server.CreateObject(&quot;ADODB.Recordset&quot;)<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strQuery = &quot;select sum(Quantity) as TotOutput from Factory_Output where FactoryId=&quot; &amp; ors(&quot;FactoryId&quot;)<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = oConn.Execute(strQuery) <br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;'Generate &lt;set name='..' value='..'/&gt;
        </span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;set name='&quot; &amp; ors(&quot;FactoryName&quot;) &amp; &quot;' value='&quot; &amp; ors2(&quot;TotOutput&quot;) &amp; &quot;' /&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;'Close recordset</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = Nothing<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;oRs.MoveNext<br />
        &nbsp;&nbsp;&nbsp;Wend<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Finally, close &lt;chart&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;/chart&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;Set oRs = nothing<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Create the chart - Pie 3D Chart with data from strXML</span><br />
        &nbsp;&nbsp;&nbsp;Call renderChart(&quot;../../FusionCharts/FCF_Pie3D.swf&quot;, &quot;&quot;, strXML, &quot;FactorySum&quot;, 650, 450)<br />
        %&gt;<br />
  &lt;/BODY&gt;<br />
  &lt;/HTML&gt;</p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>The following actions are taking place in this code:</p>
      <ol>
        <li>We first include <span class="codeInline">FusionCharts.js</span> JavaScript class and <span class="codeInline">FusionCharts.asp</span> , to enable easy embedding of FusionCharts.</li>
        <li>We then include <span class="codeInline">  DBConn.asp</span>, which contains connection parameters to connect to Access database. </li>
        <li>Thereafter, we generate the XML data document by iterating through the recordset and store it in <span class="codeInline">strXML</span> variable. </li>
        <li>Finally, we render the chart using <span class="codeInline">renderChart()</span> method and pass <span class="codeInline">strXML</span> as <span class="codeInline">dataXML</span>. </li>
      </ol>
    <p>When you now run the code, you'll get an output as under: </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><img src="Images/Code_DBOut.jpg" class="imageBorder" /></td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Converting the example to use dataURL method </td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>Let's now convert this example to use dataURL method. As previously explained, in dataURL mode, you need two pages:</p>
      <ol>
        <li><strong>Chart Container Page</strong> - The page which embeds the HTML code to render the chart. This page also tells the chart where to load the data from. We'll name this page as <span class="codeInline">Default.asp</span>. </li>
        <li><strong>Data Provider Page</strong> - This page provides the XML data to the chart. We'll name this page as <span class="codeInline">PieData.asp</span></li>
      </ol>
      <p class="highlightBlock">The pages in this example are contained in<span class="codeInline"> Download Package &gt; Code &gt; ASP &gt; DB_dataURL</span> folder. </p>    </td>
  </tr>
  <tr>
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top" class="header">Chart Container Page - <span class="codeInline">Default.asp </span></td>
  </tr>
  <tr>
    <td valign="top" class="text"><span class="codeInline">Default.asp</span> contains the following code to render the chart: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock">&lt;%@ Language=VBScript %&gt;<br />
      &lt;HTML&gt;<br />
      &lt;HEAD&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;TITLE&gt;	FusionCharts Free - dataURL and Database  Example&lt;/TITLE&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;SCRIPT LANGUAGE=&quot;Javascript&quot; SRC=&quot;../../FusionCharts/FusionCharts.js&quot;&gt;&lt;/SCRIPT&gt;<br />
&lt;/HEAD&gt;<br />
&lt;!-- #INCLUDE FILE=&quot;../Includes/FusionCharts.asp&quot; --&gt; <br />
&lt;BODY&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;%<br />
&nbsp;&nbsp;<span class="codeComment">&nbsp;'In this example, we show how to connect FusionCharts to a database <br />
&nbsp;&nbsp;&nbsp;'using dataURL method. In our previous example, we've used dataXML method<br />
&nbsp;&nbsp;&nbsp;'where the XML is generated in the same page as chart. Here, the XML data<br />
&nbsp;&nbsp;&nbsp;'for the chart would be generated in PieData.asp.<br />
<br />
&nbsp;&nbsp;&nbsp;'For the sake of ease, we've used an Access database which is present in<br />
&nbsp;&nbsp;&nbsp;'../DB/FactoryDB.mdb. It just contains two tables, which are linked to each<br />
&nbsp;&nbsp;&nbsp;'other.<br />
<br />
&nbsp;&nbsp;&nbsp;'Variable to contain dataURL</span><br />
&nbsp;&nbsp;&nbsp;Dim strDataURL	<br />
&nbsp;&nbsp;&nbsp;<span class="codeComment">'the asp file piedata.asp interacts with the database,</span> <br />
&nbsp;&nbsp;&nbsp;<span class="codeComment">'converts the data into proper XML form and finally</span> <br />
&nbsp;&nbsp;&nbsp;<span class="codeComment">'relays XML data document to the chart</span><br />
&nbsp;&nbsp;&nbsp;strDataURL = &quot;PieData.asp&quot;<br />
<br />
&nbsp;&nbsp;&nbsp;<span class="codeComment">'Create the chart - Pie 3D Chart with dataURL as strDataURL</span><br />
&nbsp;&nbsp;&nbsp;Call renderChart(&quot;../../FusionCharts/FCF_Pie3D.swf&quot;, strDataURL, &quot;&quot;, &quot;FactorySum&quot;, 650, 450)<br />
%&gt;<br />
&lt;/BODY&gt;<br />
&lt;/HTML&gt;</td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above code, we:</p>
      <ol>
        <li>Include <span class="codeInline">FusionCharts.js</span> JavaScript class and <span class="codeInline">FusionCharts.asp,</span></li>
        <li>Create the <span class="codeInline">dataURL</span> string and store it in <span class="codeInline">strDataURL</span> variable. </li>
        <li>Finally, we render the chart using <span class="codeInline">renderChart()</span> method and set <span class="codeInline">dataURL</span> as <span class="codeInline">strDataURL</span>. </li>
      </ol>    </td>
  </tr>
  <tr>
    <td valign="top" class="header">Creating the data provider page <span class="codeInline">PieData.asp </span></td>
  </tr>
  <tr>
    <td valign="top" class="text">PieData.asp contains the following code to output XML Data: </td>
  </tr>
  <tr>
    <td valign="top" class="codeBlock"><p>&lt;%@ Language=VBScript %&gt;</p>
      <p>&lt;!-- #INCLUDE FILE=&quot;../Includes/DBConn.asp&quot; --&gt;<br />
        &lt;%<br />
        &nbsp;&nbsp;&nbsp;<span class="codeComment">'This page generates the XML data for the Pie Chart contained in<br />
&nbsp;&nbsp;&nbsp;'Default.asp. <br />
  <br />
&nbsp;&nbsp;&nbsp;'For the sake of ease, we've used an Access database which is present in<br />
&nbsp;&nbsp;&nbsp;'../DB/FactoryDB.mdb. It just contains two tables, which are linked to each<br />
&nbsp;&nbsp;&nbsp;'other. <br />
  <br />
&nbsp;&nbsp;&nbsp;'Database Objects - Initialization</span><br />
        &nbsp;&nbsp;&nbsp;Dim oRs, oRs2, strQuery<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'strXML will be used to store the entire XML document generated</span><br />
&nbsp;&nbsp;&nbsp;Dim strXML<br />
<br />
        &nbsp;<span class="codeComment">&nbsp;</span><span class="codeComment">&nbsp;'Create the recordset to retrieve data</span><br />
        &nbsp;&nbsp;&nbsp;Set oRs = Server.CreateObject(&quot;ADODB.Recordset&quot;)</p>
      <p> &nbsp;<span class="codeComment">&nbsp;&nbsp;'Generate the graph element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = &quot;&lt;graph caption='Factory Output report' subCaption='By Quantity' decimalPrecision='0' showNames='1' numberSuffix=' Units' pieSliceDepth='30'  formatNumberScale='0'&gt;&quot;<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Iterate through each factory</span><br />
        &nbsp;&nbsp;&nbsp;strQuery = &quot;select * from Factory_Master&quot;<br />
        &nbsp;&nbsp;&nbsp;Set oRs = oConn.Execute(strQuery)<br />
  <br />
        &nbsp;&nbsp;&nbsp;While Not oRs.Eof<br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;'Now create second recordset to get details for this factory</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = Server.CreateObject(&quot;ADODB.Recordset&quot;)<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strQuery = &quot;select sum(Quantity) as TotOutput from Factory_Output where FactoryId=&quot; &amp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ors(&quot;FactoryId&quot;)<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = oConn.Execute(strQuery) <br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;'Generate &lt;set name='..' value='..' /&gt;</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;set name='&quot; &amp; ors(&quot;FactoryName&quot;) &amp; &quot;' value='&quot; &amp; ors2(&quot;TotOutput&quot;) &amp; &quot;' /&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;&nbsp;<span class="codeComment">&nbsp;&nbsp;'Close recordset</span><br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set oRs2 = Nothing<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;oRs.MoveNext<br />
        &nbsp;&nbsp;&nbsp;Wend<br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Finally, close &lt;graph&gt; element</span><br />
        &nbsp;&nbsp;&nbsp;strXML = strXML &amp; &quot;&lt;/graph&gt;&quot;<br />
        &nbsp;&nbsp;&nbsp;Set oRs = nothing<br />
  <br />
        &nbsp;&nbsp;<span class="codeComment">&nbsp;'Set Proper output content-type</span><br />
        &nbsp;&nbsp;&nbsp;Response.ContentType = &quot;text/xml&quot;<br />
  <br />
        &nbsp;&nbsp;&nbsp;<span class="codeComment">'Just write out the XML data<br />
        <strong>&nbsp;&nbsp;&nbsp;'NOTE THAT THIS PAGE DOESN'T CONTAIN ANY HTML TAG, WHATSOEVER</strong></span><br />
        &nbsp;&nbsp;&nbsp;Response.Write(strXML)<br />
        %&gt;<br />
    </p></td>
  </tr>
  <tr>
    <td valign="top" class="text"><p>In the above page:</p>
      <ol><li>We generate the data and store it in <span class="codeInline">strXML</span> variable</li>
        <li>Finally, we write this data to output stream without any HTML tags. </li>
      </ol>
    <p>When you view this page, you'll get the same output as before. </p></td>
  </tr>
</table>
</body>
</html>
