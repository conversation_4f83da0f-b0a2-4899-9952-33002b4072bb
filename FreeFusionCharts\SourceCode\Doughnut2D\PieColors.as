/*
PieColors.as is basically a file containing a list of colors
to be used as default colors for the pie chart in case the colors
have not been defined in the XML file.

To change the list of colors, simply change the RGB code of 
the color given below (pls remember not to use # before colors).

Finally, after changing just compile the .fla file.
*/
_global.pieDefaultColors = new Array();
pieDefaultColors[0] = "0099CC"; //Blue Shade
pieDefaultColors[1] = "FF0000"; //Bright Red
pieDefaultColors[2] = "006F00"; //Dark Green
pieDefaultColors[3] = "0099FF"; //Blue (Light)
pieDefaultColors[4] = "FF66CC"; //Dark Pink
pieDefaultColors[5] = "996600"; //Variant of brown
pieDefaultColors[6] = "669966"; //Dirty green
pieDefaultColors[7] = "7C7CB4"; //Violet shade of blue
pieDefaultColors[8] = "FF9933"; //Orange
pieDefaultColors[9] = "CCCC00"; //Chrome Yellow+Green
pieDefaultColors[10] = "9900FF"; //Violet
pieDefaultColors[11] = "999999"; //Grey
pieDefaultColors[12] = "99FFCC"; //Blue+Green Light
pieDefaultColors[13] = "CCCCFF"; //Light violet
pieDefaultColors[14] = "669900"; //Shade of green
pieDefaultColors[15] = "1941A5"; //Dark Blue
