body {
background-color: #0D0D1D;
background:#ffffff url(../img/background.jpg) repeat-x scroll 0 0;
color:#666666;
font-family:Tahoma,sans-serif,Verdana,Arial,Helvetica;
font-size:0.8em;
margin:0;
padding:0;
}
a{ text-decoration:none; border:none; text-align:center; }
img{ border:none; text-align:center; }
h1 {
border-bottom:5px solid #EEEEEE;
color: #333333;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.5em;
font-weight:normal;
height:20px;
margin-bottom:20px;
padding:0 0 10px;
}
h2 {
color:#336699;
font-family:"Trebuchet MS",<PERSON><PERSON>,Tahoma;
font-size:1.3em;
font-weight:100;
height:20px;
margin-bottom:10px;
margin-top:10px;
padding:0 0 10px;
width:80%;
}
/*
h3 {
color:#669900;
font-family:"Trebuchet MS",<PERSON><PERSON>,<PERSON><PERSON><PERSON>;
font-size:1.2em;
font-weight:100;
margin-bottom:10px;
margin-top:10px;
padding:0 0 0 0;
width:80%;
}
*/
a {
color:#336699;
text-decoration:underline;
}
.input {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
.input:hover {
background-color:#CCD5E6;
}
.input a {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
input:hover {
background-color:#CCD5E6;
border:1px solid #AAB3DD;
color:#000000;
}
input {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
select {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
color:#000000;
}
textarea {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
/*
height:40px;
height:40px;
width:180px;
*/
}
#main {
background-color:#FFFFFF;
border-color:#ADBFCC -moz-use-text-color -moz-use-text-color;
border-style:solid none none;
border-width:5px 0 0;
margin-left:30px;
margin-right:30px;
margin-top:0;
min-width:800px;
padding-bottom:10px;
padding-top:0;
border: 1px solid #d4dee9;
}
#main2 {
background-color:#FFFFFF;
border-color:#ADBFCC -moz-use-text-color -moz-use-text-color;
border-style:solid none none;
border-width:5px 0 0;
margin:0;
padding-bottom:10px;
padding-top:0;
width:800px;
height:400px;
}
#header {
background-position:left center;
background-repeat:no-repeat;
border:0 none;
height:125px;
margin:0 30px 0;
min-width:800;
padding:0;
}
#header img {
display:block;
float:left;
margin-top:-8px;
}
#header h1 {
margin:0;
padding:0;
}
#header h1 a {
display:block;
height:70px;
left:30px;
position:absolute;
text-indent:-9000px;
top:20px;
width:220px;
}
#header ul {
	float:left;
	height:36px;
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin-left:0;
	margin-top:92px;
	min-width:800;
	padding-left:0;
	position:absolute;
	left: 31px;
	top: -2px;
	width: 100%;
}

#header ul li a {
color:003;
display:table-cell;
float:left;
font-weight:bold;
height:25px;
margin-left:5px;
margin-right: 20px;
padding-top:8px;
text-align:center;
text-decoration:none;
vertical-align:bottom;
/*width:100px;*/
}
#header li a:hover, #header li a:focus {
background-position:0 -33px;
color:#C00;
}

.menu li {
	color:#C36;
}

#foto {
background-repeat:no-repeat;
float:right;
height:45px;
margin-top:0;
width:45px;
}

#usuarioInfo {
float:right;
height:45px;
margin-top:0;
width:295px;
margin-right:5px;
}
#usuarioInfo h1 {
border:0 none;
color:#003;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.6em;
margin-bottom:10px;
padding-top:10px;
text-indent:83px;
}
#usuarioInfo img {
border:0 none;
float:right;
margin-right:0;
}
#usuarioInfo a {
color:#003;
padding-left:65px;
text-decoration:none;
}

#empresaInfo {
/*background-image:url(img/logo.jpg);*/
background-repeat:no-repeat;
float:left;
height:85px;
margin-top:0;
width:450px;
}
#empresaInfo h1 {
border:0 none;
color:#003;
font-family:"Trebuchet MS",Arial,Tahoma;
font-size:1.6em;
margin-bottom:10px;
padding-top:10px;
text-indent:30px;
}
#empresaInfo img {
border:0 none;
float:left;
margin-right:0;
}
#empresaInfo a {
color:#CCCCCC;
padding-left:65px;
text-decoration:none;
}

#footer {
color:#003;
margin-left:30px;
margin-right:30px;
padding-bottom:10px;
padding-top:10px;
text-align:right;
}

.menu {
	color: #003;
	margin-left:30px;
	margin-right:30px;
	padding-bottom:10px;
	padding-top:10px;
	text-align:right;	
}

#adic {
color: #003;
margin-left:30px;
margin-right:30px;
padding-bottom:10px;
padding-top:10px;
text-align:right;
}
/*
th {
	padding: 0;
	margin:0;
	font-size: 11px;
	font-weight: bold;
	text-align: left;
	background-color:#E6EEEE;
}
*/
td {
	padding: 0px 30px 2px 0px;
	font-size: 10px;
	text-align: left;
}

.pendiente {
	color:#FF0000;
}

.disponible {
	color:#00FF00;
}

.ticket{
	border:1px dashed #CCCCCC;
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	/*margin:150px 150px 150px 150px;*/
	margin-left:375px;
	width:350px;
	padding:2px;
	background-color:#FFFFFF;
}
.button {
background-color:#E9EBF3;
border:1px solid #AAB3DD;
height:22px;
width:250px;
}

/* Calendario*/
a.dp-choose-date {
	background: url(../img/cal.gif) no-repeat; 
}

/* Cerrar Sesion*/
.cerrar {
	color:#F00;
}

/* TABLAS */
#tabla table {
	width: 100%;
	border:1px solid #9EB6CE;
	border-style:solid;
	border-width: 0 0 1px 1px;
	border-spacing:0px;
}

#tabla th {
	color:#000000;
	text-align:left;
	padding:0px;
	padding-left:5px;
	padding-right:5px;
	background-color:#D2DBE8;
	background-image:url(../img/bg_th2.png);
	background-repeat:repeat-x;
	/*height:12px;*/
	/*min-width:60px;*/
	border:1px solid #9EB6CE;
	border-style:solid;
	border-width: 1px 1px 1px 1px;
	font-size:12px;
}

#tabla td {
	border:1px solid #D0D7E5;
	border-style:solid;
	border-width: 1px 1px 1px 1px;
	height:12px;
	padding-left:5px;
	padding-right:5px;
	font-size:11px;
}

.input_error{
	color:#F00;
	background-image:url(../images/input_error.png);		
	background-repeat:repeat-x;	
}


#footer {
	display: flex;
	justify-content: end;
}

#formLogin #inputEmpresa {
	width: 259px;
}

#logoEmpresa img {
	margin-top: 0px !important;
}


body.estilosPE {
	background-image: none !important;
}


body.estilosPE #menu ul li a{
	color: #313943; 
	font-weight: 700;
	font-size: 13px;
}