<?xml version="1.0" encoding="iso-8859-1"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>FusionCharts Free Documentation</title>
<link rel="stylesheet" href="Style.css" type="text/css" />
</head>

<body>
<table width="98%" border="0" cellspacing="0" cellpadding="3" align="center">
  <tr> 
    <td><h2 class="pageHeader">Download Package Contents</h2></td>
  </tr>
  <tr> 
    <td valign="top" class="text"><p>FusionCharts Free Download Package contains 
        the following : </p></td>
  </tr>
  <tr> 
    <td valign="top" class="text">&nbsp;</td>
  </tr>
  <tr> 
    <td valign="top" class="text"><table width="95%" border="0" align="center" cellpadding="2" cellspacing="0">
        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">SWF Files (Ready to use charts)</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>All the charts are present in <span class="codeInline">Download 
            Package &gt; Charts</span> Folder. Whenever you need to create charts 
            for a new web application, just copy these SWF files and paste it 
            in your application.</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="middle" class="text"> 
          <td> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">FusionCharts JavaScript Class</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>FusionCharts Free JavaScript Class is present in <span class="codeInline">Download 
            Package &gt; JSClass </span>folder. This class helps you embed charts 
            in your HTML page in a more user-friendly way. Also, it helps avoid 
            the Internet Explorer <span class="codeInline">&quot;Click to Activate 
            this control&quot;</span> issue.</td>
        </tr>
        <tr valign="middle" class="text"> 
          <td>&nbsp;</td>
          <td class="textBold">&nbsp;</td>
        </tr>
        <tr valign="middle" class="text"> 
          <td width="15"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Sample Code</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>All the code that we refer to in this documentation, is present 
            in <span class="codeInline">Download Package &gt; Code</span> folder. 
            These are ready to use code samples, that you can copy-paste and run. 
            Just make sure that you copy the Charts along with the respective 
            example, else you would get an empty screen with a never ending progress 
            bar.</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text"> 
          <td width="15" valign="middle"> <div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
          <td class="textBold">Chart Samples</td>
        </tr>
        <tr valign="top" class="text"> 
          <td>&nbsp;</td>
          <td>We've built a few chart samples for your viewing pleasure. These 
            are present in <span class="codeInline">Download Package &gt; Gallery</span> 
            folder. You can also access them using the menu on left. Select <span class="codeInline">Sample 
            Charts</span>. </td>
        </tr>
        <tr valign="top" class="text">
           <td>&nbsp;</td>
           <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text">
           <td><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
           <td class="textBold">Chart Documentation</td>
        </tr>
        <tr valign="top" class="text">
           <td>&nbsp;</td>
           <td>We provide you a full length discussion on FusionCharts Free. This is a complete package in itself that introduces FusionCharts Free to the beginners as well as provides advanced developers with complete programming reference.</td>
        </tr>
        <tr valign="top" class="text">
           <td>&nbsp;</td>
           <td>&nbsp;</td>
        </tr>
        <tr valign="top" class="text">
           <td><div align="center"><img src="Images/Bullet.gif" width="9" height="9" /></div></td>
           <td class="textBold">Chart Source-code</td>
        </tr>
        <tr valign="top" class="text">
           <td>&nbsp;</td>
           <td>FusionCharts FREE is open-source. We pack the full source code of all  charts in <span class="codeInline">Download 
            Package &gt; SourceCode </span>folder. </td>
        </tr>
        
        
        
      </table></td>
  </tr>
</table>
</body>
</html>
