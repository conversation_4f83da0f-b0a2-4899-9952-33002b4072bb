<?php

//DESARROLLO
//session_start();
//include("db.inc.php");
//include("../db.inc.php");
include("../db.inc.php");
include("../class/class.log4My.php");
$log = new log4My(); //Se instancia la clase.
$idXLog = $log->getId4Log(); //Se genera el identificador. 
//$log->write($idXLog, 0, 'commonFunctions.php/', 'empieza el file');	

if (isset($_POST['id'])) { //Se puede pasar el $id por POST o GET.
    $id = $_POST['id'];
}

if ($_POST) {
    $keys_post = array_keys($_POST);
    foreach ($keys_post as $key_post) {
        $$key_post = $_POST[$key_post];
    }
}

if ($id == 1) {
// Chequea si ya se inicio sesion, si es asi... se carga de vuelta el menu y los datos
// de la empresa y el usuario.
    if (isset($_SESSION['dbOraConn']) && ($_SESSION['dbOraConn'] == 1)) {
        $est = '1';
        $msg = 'OK';
        $adic = $_SESSION['infoXuser_ge'];
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        $log->write($idXLog, 0, 'commonFunctions.php/Primera funcion id_1', 'Respuesta: ' . $msg);
        echo $respuesta;
    } else {
        unset($_SESSION['inicioSesion_gi']);
        $est = '2';
        $msg = 'El mismo usuario inicio sesion en otra maquina, cierre sesion e inicie de vuelta o pongase en contacto con el SAC para obtener mas informacion.';
        $adic = '***';
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        $log->write($idXLog, 0, 'commonFunctions.php/Primera funcion id_1', 'Respuesta: ' . $msg);
        echo $respuesta;
    }
} elseif ($id == 2) {
// Se destruye le sesion y se devuelve el menú inicial con la opcion de LOGIN y
// el nombre del producto (Eficash - Pronet S.A.).
    //$log->write($idXLog, 0, 'commonFunctions.php/session_destroy', 'Se destruyo la sesion para el usuario Nro.: '.$_SESSION["cod_usuario"]);
    session_destroy();
    echo '<ul>
              <li><a href="javascript:loadContent(\'login.php\');">LOGIN</a></li>
            </ul>|<h1><b>Sistema de Gesti&oacute;n Interna.</b></h1>';
} elseif ($id == 15) {
    echo $aux = comboRecaudadorasSubRED($log, $idXLog, $_POST['id']);
} elseif ($id == 66) {
    echo $aux = saldoArrastreConSaldo($log, $idXLog);
} elseif ($id == 95) {
    echo $aux = cmbBanco($log, $idXLog, $_POST['id']);
}
/* * ****************** */
/* Bloque de funciones */
/* * ****************** */
/*
  function pedidoActualizacion($id, $log, $idXLog){
  echo $aux = enviarPedidoActualizacion($id, $log, $idXLog, 3);
  }
 */

function cmbBanco($log, $idXLog, $id) {
    include("../db.inc.php");

    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select distinct b.cod_banco, ba.des_banco
from admin.Banco_Recaudadora b, admin.banco ba
where b.cod_banco = ba.cod_banco
and b.activo = 'S'
order by 2";

    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboBancos', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';

    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }
    oci_close($ora_conn);
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    /*    $vars = explode("|", $resulGetNoficaciones);
      $id = $vars[0];
      $est = $vars[1];
      $msg = $vars[2];
      $adic = $vars[3];
      unset($vars);
     */


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/comboBancos', 'respuesta: ' . $respuesta . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function saldoArrastreConSaldo($log, $idXLog) {
    include("../db.inc.php");
    $id = 66;
    $fecha = $_POST['fecha'];
    $rec = $_POST['rec'];
    $fechafin = $_POST['fechafin'];
    $moneda = $_POST['moneda'];
    $banco = $_POST['banco'];
    $saldo = $_POST['saldo'];
    $filtroFecha = $_POST['filtroFecha'];

    $tipoCuenta = $_POST['tipoCredito'];
    $subRed = $_POST['subRed'];
    $entRecupero = $_POST['entRecupero'];

    $modeloPos = $_POST['modeloPos'];
    $termAct = $_POST['termAct'];


    $whereSql = "";
    if ($filtroFecha == 1) {
        $orderFecha = " sp.fecha_pago asc ";
    } else {
        $orderFecha = " sp.fecha_pago desc ";
    }
    if ($modeloPos != '*') {
        $whereSql .= " and mo.id_modelo_pos =$modeloPos ";
        $filtro = "  mo.cod_tipo_pos,
                    
                    t.activo terminal_activa,
                    mo.des_modelo_pos ";
    } else {
        $filtro = " '--' cod_tipo_pos,
                    
                    '--' terminal_activa,
                    '--' des_modelo_pos ";
    }
    if ($termAct != '*'){
        $whereSql .= " and t.activo='$termAct' ";
    }
    if ($rec != '*') {
        $whereSql .= " and sp.cod_recaudadora = $rec ";
    }

    if ($banco != '*') {
        $whereSql .= " and sp.cod_banco = $banco ";
    }

    if ($subRed != '*') {
        $whereSql .= " and br.subred = '$subRed' ";
    }

    /* if ($remesa != '*'){
      $whereSql = " and '$remesa' = admin.f_rec_hace_remesa(sp.cod_empresa, sp.cod_recaudadora) ";
      } */

    if ($saldo == 'positivo') {
        $whereSql .= " and sp.saldo > 0 ";
    } elseif ($saldo == 'negativo') {
        $whereSql .= " and sp.saldo < 0 ";
    }

    if ($tipoCuenta == 'Pos') {
        $whereSql .= " and r.tipo_credito = '$tipoCuenta' ";
    } elseif ($tipoCuenta == 'Pre') {
        $whereSql .= " and r.tipo_credito = '$tipoCuenta' ";
    }

    if ($entRecupero == 'N') {
        $whereSql .= " AND upper (des_entidad) not like '%RECUPERO%' ";
    }

    $ora_conn = oci_connect($user, $password, $host);

    $query = "select em.des_entidad,
                b.des_banco,
                m.des_moneda,
       sp.cod_empresa,
       sp.cod_recaudadora,
       sp.fecha_pago,
       sp.cod_banco,
       sp.cod_moneda,
       sp.total_debitos,
       sp.total_creditos,
       sp.saldo_ant,
       sp.dias_sobregiro,
       sp.fecha_ultimo_pago,
       sp.saldo,
       sp.desembolso, 
                tr.descripcion || ' ' || r.tipo_credito || 'Pago' tipo_red,
                r.cobra_mh,
      
      /* inicio nuevo bloque*/
       
       MAX(PO.VENCIMIENTO_POLIZA) vencimiento_poliza,
       nvl(po.importe_poliza, 0) as importe_poliza,
       
       $filtro
     /*fin nuevo bloque */ 
            from admin.control_sob_saldo_fpag sp,
                admin.entidad                em,
                admin.banco                  b,
                admin.moneda                 m,
                admin.recaudadora            r,
                admin.tipo_red               tr, 
       admin.banco_recaudadora      br, --NUEVO
       /*nuevo bloque */
       admin.caja_sucursal          cs,
       admin.terminal               t,
       admin.modelo_pos             mo,
       admin.poliza po
       /* fin nuevo bloque */
            where sp.cod_recaudadora = em.cod_entidad
                and em.cod_empresa = sp.cod_empresa
                and b.cod_banco = sp.cod_banco
                and m.cod_moneda = sp.cod_moneda
                and r.cod_empresa = sp.cod_empresa 
                and r.cod_recaudadora = sp.cod_recaudadora 
                and tr.cod_tipo_red = r.cod_tipo_red 
            --COMBO REMESAS
                and 'N' = admin.f_rec_hace_remesa(sp.cod_empresa, sp.cod_recaudadora)
                and sp.fecha_pago BETWEEN to_date('$fecha', 'DD/MM/YYYY') and
                    to_date('$fechafin', 'DD/MM/YYYY')
   $whereSql
and po.cod_empresa(+)=r.cod_empresa
and po.cod_recaudadora(+)=r.cod_recaudadora

/* inicio nuevo bloque */


                and br.cod_empresa = r.cod_empresa
                and br.cod_recaudadora = r.cod_recaudadora
                and br.prioridad = 1
                and br.activo = 'S'
   and br.cod_moneda = m.cod_moneda
                and sp.cod_moneda = $moneda
   and cs.cod_empresa = r.cod_empresa
   and cs.cod_recaudadora = r.cod_recaudadora
   and cs.id_terminal = t.id_terminal
   and t.id_modelo_pos = mo.id_modelo_pos  
and po.vencimiento_poliza(+)>=sysdate--to_date('12/12/2017', 'DD/MM/YYYY')  --Filtro

 group by em.des_entidad,
          b.des_banco,
          m.des_moneda,
          tr.descripcion || ' ' || r.tipo_credito || 'Pago'  ,
          mo.des_modelo_pos,
          sp.cod_empresa,
       sp.cod_recaudadora,
       sp.fecha_pago,
       sp.cod_banco,
       sp.cod_moneda,
       sp.total_debitos,
       sp.total_creditos,
       sp.saldo_ant,
       sp.dias_sobregiro,
       sp.fecha_ultimo_pago,
       sp.saldo,
       mo.cod_tipo_pos,sp.desembolso,
        t.activo  ,po.importe_poliza,r.cobra_mh
 /* fin nuevo bloque */     
 order by em.des_entidad, sp.fecha_pago desc, sp.cod_moneda, $orderFecha ";

    $log->write($idXLog, 0, 'commonFunctions.php/subRedesResumenSaldo', 'Query: ' . $query);
    $id_sentencia = @oci_parse($ora_conn, $query);
    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $r = @oci_execute($id_sentencia, OCI_DEFAULT);
    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/subRedesResumenSaldo', 'ERROR: ' . $msg . " QUERY: " . $query);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }
    $cabecera = "<div id='rep' align='center'>
		<table border='0'>
			<tr>
				<td><a href='ticket/impresion.php' target='_blank'><img src='img/imprimir.jpg' alt='IMPRIMIR' border='0'></a></td>
				<td><a href='ticket/xls.php' target='_blank'><img src='img/excel.jpg' alt='EXCEL' border='0'></a></td>				
				<td><a href='ticket/pdf.php' target='_blank'><img src='img/pdf.jpg' width='40' height='41' alt='PDF' border='0' /></a></td>								
			</tr>
		</table>
	</div>";
    $adic = '
				<table cellspacing="1" align="center" class="tablesorter">
					<thead>
						<tr>
							<th>COD. BANCO</th>
							<th>BANCO</th>
							<th>COD. RECAUDADORA</th>
							<th>ENTIDAD</th>
							<th>TIPO RED</th>
							<th>FECHA PAGO</th>
							<th>MONEDA</th>
							<th>DEBITOS</th>
							<th>DESEMBOLSOS</th>
							<th>DEPOSITOS</th>
							<th>DIFERENCIA</th>
							<th>SALDO</th>
							<th>DIAS SOBREGIRO</th>
                                                        <th>MH</th>
                                                        
                                                        <th>VENCIMIENTO POLIZA</th>
                                                        <th>IMPORTE POLIZA</th>
                                                        
                                                        <th>COD TIPO POS</th>
                                                        <th>TERMINAL ACTIVA</th>
                                                        <th>MODELO POS</th>
						</tr>
					</thead>
					<tbody>';
    $total_debitos = 0;
    $total_creditos = 0;
    $saldo_ant = 0;
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $style = '';
        $total_debitos += $fila['TOTAL_DEBITOS'];
        $total_creditos += $fila['TOTAL_CREDITOS'];
        $saldo_ant += $fila['SALDO_ANT'];
        $saldo = (int) $fila['SALDO'];
        $sobregiro = (int) $fila['DIAS_SOBREGIRO'];
        //  if (($saldo < 0) && ($sobregiro > 0)) {
        if (($saldo < 0) || $sobregiro > 0) {
            $style = 'style="color:#fff !important; font-weight:bold; background:#C60000 !important;"';
        } else {
            $style = '';
        }
        $adic .= '<tr >';
        $adic .= '<td ' . $style . '>' . $fila['COD_BANCO'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['DES_BANCO'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['COD_RECAUDADORA'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['DES_ENTIDAD'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['TIPO_RED'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['FECHA_ULTIMO_PAGO'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['DES_MONEDA'] . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['TOTAL_DEBITOS'], 0, ',', '.') . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['DESEMBOLSO'], 0, ',', '.') . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['TOTAL_CREDITOS'], 0, ',', '.') . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['SALDO_ANT'], 0, ',', '.') . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['SALDO'], 0, ',', '.') . '</td>';
        $adic .= '<td  ' . $style . '>' . $fila['DIAS_SOBREGIRO'] . '</td>';
        $adic .= '<td  ' . $style . '>' . $fila['COBRA_MH'] . '</td>';

        $adic .= '<td ' . $style . '>' . $fila['VENCIMIENTO_POLIZA'] . '</td>';
        $adic .= '<td ' . $style . '>' . number_format($fila['IMPORTE_POLIZA'], 0, ',', '.') . '</td>';

        $adic .= '<td ' . $style . '>' . $fila['COD_TIPO_POS'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['TERMINAL_ACTIVA'] . '</td>';
        $adic .= '<td ' . $style . '>' . $fila['DES_MODELO_POS'] . '</td>';

        $adic .= '</tr>';
        $est = '1';
        $msg = 'OK';
    }
    $adic .= '<tr>';
    $adic .= '<td colspan="7"><b style="font-size:16px"> &nbsp; TOTAL </b></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($total_debitos, 0) . '</b></td>';
    $adic .= '<td ></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($total_creditos, 0) . '</b></td>';
    $adic .= '<td ><b style="font-size:16px">' . numberFormater($saldo_ant, 0) . '</b></td>';
    $adic .= '<td ><b style="font-size:16px"> -- </b></td>';
    $adic .= '<td ><b style="font-size:16px"> -- </b></td>';
    $adic .= '<td ><b style="font-size:16px"> </b></td>';

    $adic .= '</tr>';


    $adic .= '</tbody>
				</table>';
    $_SESSION['datosImprimir'] = $adic;
    $_SESSION['repName'] = 'Reporte Resumen c/ Saldo (Fec. PAGO) de ' . $fecha . ' hasta ' . $fechafin . '';
    $adic = $adic . '</table>';
    $adic = '<h3 style="text-align:center;"> Reporte Resumen c/ Saldo (Fec. PAGO) de ' . $fecha . ' hasta ' . $fechafin . '</h3>' . $adic;

    oci_close($ora_conn);
    $est = 1;
    $msg = "OK";
    $respuesta = $id . '|' . $est . '|' . $msg . '|' . $cabecera . $adic;


    return $respuesta;
}

function comboRecaudadorasSubRED($log, $idXLog, $id) {
    include("../db.inc.php");
    $ora_conn = oci_connect($user, $password, $host);
    $est = '2';
    $msg = 'No se obtuvieron resultados para su consulta';
    $adic = '***';
    $inicioF4 = microtime(true);
    $debito = 0;
    $credito = 0;

    if (!$ora_conn) {
        $e = oci_error();
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $query = "select b.cod_recaudadora, e.des_entidad 
from admin.banco_recaudadora b, admin.entidad e
where (b.cod_banco = 98 or b.subred='S' or b.calcular_saldo='S')
and b.prioridad = 1 
and b.cod_recaudadora = e.cod_entidad
group by b.cod_recaudadora, e.des_entidad
order by 2";

    $log->write($idXLog, 0, 'commonFunctions.php/comboRecaudadorasSubRED', 'Query: ' . $query);

    $id_sentencia = @oci_parse($ora_conn, $query);

    if (!$id_sentencia) {
        $e = oci_error($ora_conn);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $r = @oci_execute($id_sentencia, OCI_DEFAULT);

    if (!$r) {
        $e = oci_error($id_sentencia);
        $msg = $e['message'];
        $log->write($idXLog, 3, 'commonFunctions.php/comboRecaudadorasSubRED', 'ERROR: ' . $msg);
        $est = 0;
        $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
        return $respuesta;
    }

    $adic = '';
    $adic.="<option value='*' >..:: Recaudador ::..</option>";
    while ($fila = oci_fetch_array($id_sentencia, OCI_RETURN_NULLS)) {
        $adic.="<option value='" . $fila[0] . "' >" . $fila[1] . "</option>";
        $est = '1';
        $msg = 'OK';
    }



    oci_close($ora_conn);
    echo $respuesta = $id . '|' . $est . '|' . $msg . '|' . $adic;
//	list($id, $est, $msg, $adic) = ("\|", $resulGetNoficaciones );

    /*  $vars = explode("|", $resulGetNoficaciones);.:: Recaudador ::
      $id = $vars[0];
      $est = $vars[1];
      $msg = $vars[2];
      $adic = $vars[3];
      unset($vars);
     */


    $finF4 = microtime(true);
    $totalF4 = round($finF4 - $inicioF4, 4);
    $log->write($idXLog, 0, 'commonFunctions.php/getDebitosCreditos', 'query : ' . $query . ' y se demoro ' . $totalF4 . ' segundos');
    return $respuesta;
}

function numberFormater($number, $decimal) {
    if (empty($decimal)) {
        $decimal = 0;
    }
    $number = str_ireplace(',', '.', $number);
    $number = number_format($number, $decimal, ',', '.');
    return $number;
}

?>
