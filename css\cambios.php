<?php
if(isset($_GET['usuario']) and $_GET['usuario'] <> ''){
	$var  = '<script type="text/javascript">';
	$var .=	'usuario = <?php echo $usuario = '.$_GET['usuario'];
	$var .=	'alert(usuario)';
	$var .=	'$("#usuario").val(usuario)';
	$var .=	'</script>';
	echo $var;
}
?>    
    
<script type="text/javascript">
$(document).ready(function() {
	

	$("#nombre").focus();
	
	$("#btnLimpiar").click(function (){ 
		limpiarTodo();
	});
	
	$("#btnCambiarPass").click(function (){ 
		// funcion #003
		var usuario = $("#usuario").val();
		if (usuario != ''){
			$("#main").load('cambiarPass.php?usuario='+usuario);
		}
		else{
			alert("Favor ingrese el usuario!");
		}
	});
	
	$("#btnCambiarEstado").click(function (){ 
		// funcion #003
		usuario = $("#usuario").val();
		if (usuario != ''){
			$("#main").load('cambiarEstado.php?usuario='+usuario);
		}
		else{
			alert("Favor ingrese el usuario!");
		}
	});
	
});

</script>


<div id="main" >
    <div id="content" align="center">
        <ul><li>Ingrese el usuario y la acci&oacute;n que desea realizar.</li></ul>
        
         <tr>
            <td class="label"><label id="lusuario" for="lusuario">Usuario</label></td>
            <td class="field">
                <input id="usuario" name="usuario" type="text" value=""/>
            </td>
        </tr>
        <br><br>
        <input type="button" value="Cambiar Contrase&ntilde;a" id="btnCambiarPass">
        <input type="button" value="Cambiar Estado" id="btnCambiarEstado">
       
    </div>
</div>
