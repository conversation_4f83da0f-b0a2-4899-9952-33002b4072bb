 
<script  type="text/javascript">
 

function cargarEntidad() {
    var datos="id=32";
    $.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: datos,
					success: function(msg){				
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[1] == 1){
							$("#cmbRecaudadora").html(resultado[3]);
							$("#status").fadeOut("Fast");
						    $("table").tablesorter(); 	
						}else{
							$("#status").fadeOut("Fast");
							$("#main").html('Error"#032 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
						}
					}
				});
}
function cargarEjecutivo(){
    var datos="id=33";
    $.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: datos,
					success: function(msg){	 						
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[1] == 1){
							$("#cmbEjecutivo").html(resultado[3]);
							$("#status").fadeOut("Fast");
						    $("table").tablesorter(); 	
						}else{
							$("#status").fadeOut("Fast");
							$("#main").html('Error"#033 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
						}
					}
				});
}

$(document).ready(function() {
cargarEntidad();
 cargarEjecutivo();
	$("#btnGenerarReporte").click(function (){ 
		var anhoProyectado = $("#cmbAnhoProyectado").val();
		var cuatrimestre = $("#cmbCuatrimestre").val();				
		var ejecutivo = $("#cmbEjecutivo").val();
		var recaudadora = $("#cmbRecaudadora").val();	 
	    if (cuatrimestre=='*'){
                alert ('Debe seleccionar un Cuatrimestre');
                $("#cmbCuatrimestre").focus();	
                return false;
            }
			if(confirm('Esta seguro que desea realizar la consulta, puede demorarse unos segundos?')){
				$("#filtros").hide();																										
				//$('#btnGenerarReporte').attr('disabled','-1');
				$("#status").fadeIn("Slow");
				var datos = "id=34&anhoProyectado="+anhoProyectado+"&cuatrimestre="+cuatrimestre+"&ejecutivo="+ejecutivo;
				datos += "&recaudadora="+recaudadora;
				$.ajax({
					type: "POST",
					url: "commonFunctions.php",
					data: datos,
					success: function(msg){	 					
						msg = msg.replace(/^\s+/, "");
						var resultado = msg.split("|");
						if(resultado[1] == 1){
							$("#resultadoCuatrimestre").html(resultado[3]);
							$("#status").fadeOut("Fast");
						    $("table").tablesorter(); 	
						}else{
							$("#status").fadeOut("Fast");
							$("#resultadoCuatrimestre").html('Error"#034 '+resultado[2]+'",consulte con el administrador facilitandole este dato.');
						}
					}
				});
			}
	});
});
</script>

<div id="filtros" align="center">
<ul><li><b>Metas Cautrimestrales - Ejecutivos Comerciales.</b></li></ul>
    <br>
 
 
        <table border="0">
        <tr>
            <td>A&ntilde;o Proyectado: </td>
            <td><select id="cmbAnhoProyectado" name="cmbAnhoProyectado">
                <option value="2012">2012</option>            
            	<option value="2011">2011</option></select></td>        
        </tr>
        <tr>
            <td>Cuatrimestre: </td>
            <td><select id="cmbCuatrimestre" name="cmbCuatrimestre">
                <option value="*">Cuatrimestre</option>            
            	<option value="1">Primer</option>
            	<option value="2">Segundo</option>
                <option value="3">Tercer</option> </select></td>        
        </tr> 
        <tr>
            <td>Ejecutivo: </td>
            <td>
            <select id="cmbEjecutivo" name="cmbEjecutivo">
            	               
            </select>
            </td>        
        </tr>
        
        <tr>
        <td>Recaudadora: </td>
        <td>
            <select id="cmbRecaudadora" name="cmbRecaudadora">
                
            </select></td>
        </tr> 
		<tr>
        <td> </td>
        <td><input type="button" value="Generar Reporte" id="btnGenerarReporte"></td>
        </tr>               
        </table>
    
</div>
<hr><div id="resultadoCuatrimestre"></div>


