<script type="text/javascript" src="js/jquery.tablesorter.min.js"></script>
<script id="demo" type="text/javascript">
<?php session_start(); ?>
$(document).ready(function() {
	$("#mainReportes").hide();
	$("#btnPerfilDeClientes").click(function (){ 
		$("#main").load("seprelad/perfilDeClientes.php");
		$("#status").fadeOut("Fast");
	});
	$("#btnRangoVol").click(function (){ 
		$("#main").load("seprelad/rango-volumen.php");
		$("#status").fadeOut("Fast");
	});
	$("#btnOcupacion").click(function (){ 
		$("#main").load("seprelad/ocupacion.php");
		$("#status").fadeOut("Fast");
	});
	
	$("#btnPais").click(function (){ 
		$("#main").load("seprelad/pais.php");
		$("#status").fadeOut("Fast");
	});
	$("#btnFactRiesgo").click(function (){ 
		$("#main").load("seprelad/factores-riesgo.php");
		$("#status").fadeOut("Fast");
	});
	$("#btnReportOperaciones").click(function (){ 
		$("#main").load("seprelad/reportOperaciones.php");
		$("#status").fadeOut("Fast");
	});
	//////

	$("#btneventos").click(function (){ 
		$("#main").load("seprelad/Eventos.php");
		$("#status").fadeOut("Fast");
	});

	$("#btnfrmseprelad").click(function (){ 
		$("#main").load("seprelad/FrmSeprelad.php");
		$("#status").fadeOut("Fast");
	});

	$("#btnconglistas").click(function (){ 
		$("#main").load("seprelad/ConfigListas.php");
		$("#status").fadeOut("Fast");
	});

	$("#btnlistas").click(function (){ 
		$("#main").load("seprelad/Listas.php");
		$("#status").fadeOut("Fast");
	});

	$("#btntrxpersona").click(function (){ 
		$("#main").load("seprelad/SaldoPersona.php");
		$("#status").fadeOut("Fast");
	});

	$("#bttrxwu").click(function (){ 
		$("#main").load("seprelad/TransaccionesWu.php");
		$("#status").fadeOut("Fast");
	});

	$("#btblacklist").click(function (){
      $("#main").load("procesos/BlackList.php");
      $("#status").fadeOut("Fast");
  });

  $("#btnconfigimpor").click(function (){
      $("#main").load("seprelad/ConfigImporte.php");
      $("#status").fadeOut("Fast");
  });

  $("#btntrxbasetigo").click(function (){
      $("#main").load("seprelad/ListadoBaseTigo.php");
      $("#status").fadeOut("Fast");
  });

  

	
});
</script>
<center>
<div id="titulo" align="center">
  <ul>
    <li> <b>Generaci&oacute;n de reportes.</b> </li>
  </ul>
</div>
<div id="interna" align="center">
  <table>
    <tr>
      <td><input type="button" value="Perfil de Clientes" id="btnPerfilDeClientes" class="button"></td>
      <td>&nbsp;</td>
    </tr>
  </table>
</div>
  <ul>
    <li> <b>ABM Remesas</b> </li>
  </ul>
 <table>
    <tr>
      <td><input type="button" value="Rango Volumne" id="btnRangoVol" class="button"></td>
          <td><input type="button" value="Ocupacion" id="btnOcupacion" class="button"></td>
      
    </tr>
    <tr>
      <td><input type="button" value="Pais" id="btnPais" class="button"></td>
      <td><input type="button" value="Factor Riesgo" id="btnFactRiesgo" class="button"></td>
      
    </tr>
  </table>
  <?php if($_SESSION["codEmpresa"]== 1){ ?>
    <ul>
      <li> <b>Archivos Remesas</b> </li>
    </ul>
    <table>
      <tr>
        <td><input type="button" value="Reporte de Operaciones" id="btnReportOperaciones" class="button"></td>
      </tr>
    </table>
  <?php } ?>
  <ul>
    <li> <b>Cumplimiento</b> </li>
  </ul>
 <table>
    <tr>
      <td><input type="button" value="Saldo Persona" id="btntrxpersona" class="button"></td>
      <td><input type="button" value="Listas de Control" id="btnlistas" class="button"></td>
    </tr>
    <tr>
      <td><input type="button" value="Configuracion Listas" id="btnconglistas" class="button"></td>
      <td><input type="button" value="Formularios Seprelad" id="btnfrmseprelad" class="button"></td>
    </tr>
    <tr>
      <td><input type="button" value="Eventos" id="btneventos" class="button"></td>
      <td><input type="button" value="Transacciones" id="bttrxwu" class="button"></td>
    </tr>
    <tr>
      <td><input type="button" value="Configuracion Importe" id="btnconfigimpor" class="button"></td>
      <td><input type="button" value="Black List de Documentos" id="btblacklist" class="button"></td>
    </tr>
  </table>
  <ul>
    <li> <b>Reporte Remesas</b> </li>
  </ul>
  <table>
    <tr>
      <td><input type="button" value="Listado Base Tigo" id="btntrxbasetigo" class="button"></td>
    </tr>
  </table>
</center>